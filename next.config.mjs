import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin();

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false,
  compiler: {
    styledComponents: {
      ssr: true
    }
  },
  eslint: {
    ignoreDuringBuilds: true
  }
  // webpack: (config) => {
  //   config.module.rules.push({
  //     test: /\.js$/,
  //     parser: { sourceType: 'module' } // Add this line to set sourceType
  //   });
  //   return config;
  // },

  // ### node 23 이상 사용 시 설정 필요
  // webpack5: true,
  // webpack: (config, options) => {
  //   config.cache = false;
  //   return config;
  // },
};

export default withNextIntl(nextConfig);
