{"name": "gai-platform-user-2025", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 33060", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@mui/material": "^5.16.14", "@mui/x-date-pickers": "^7.13.0", "@next/third-parties": "^15.1.3", "@popperjs/core": "^2.11.8", "@stomp/stompjs": "^7.0.0", "@tosspayments/tosspayments-sdk": "^2.3.4", "@types/jquery": "^3.5.31", "ace-builds": "^1.38.0", "apexcharts": "^4.7.0", "cookies-next": "^4.2.1", "dayjs": "^1.11.12", "eventemitter3": "^5.0.1", "firebase": "^10.13.1", "jquery.terminal": "^2.44.1", "next": "15.4.5", "next-auth": "^5.0.0-beta.20", "next-firebase-auth-edge": "^1.7.0-canary.2", "next-fonts": "^1.5.1", "next-intl": "^3.21.1", "next-themes": "^0.4.6", "react": "19.1.0", "react-ace": "^14.0.1", "react-apexcharts": "^1.4.1", "react-cookie": "^7.2.0", "react-dom": "19.1.0", "react-icons": "^5.2.1", "react-json-editor-ajrm": "^2.5.14", "react-session-api": "^1.1.0", "react-swipeable": "^7.0.2", "react-toastify": "^11.0.5", "react-use-websocket": "^4.11.1", "sockjs-client": "^1.6.1", "swiper": "^11.2.4", "zod": "^3.23.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/postcss": "^4", "@types/lodash-es": "^4.17.12", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.19", "eslint": "^9", "eslint-config-next": "15.4.5", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "file-loader": "^6.2.0", "lodash-es": "^4.17.21", "mini-svg-data-uri": "^1.4.4", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4", "typescript": "^5", "url-loader": "^4.1.1", "zustand": "^5.0.3"}}