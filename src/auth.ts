import NextAuth, { User } from 'next-auth';

import GoogleProvider from 'next-auth/providers/google';

import CredentialsProvider from 'next-auth/providers/credentials';

import { getPlatformToken, login } from './action/auth-action';

import { googleAuth, microsoftAuth } from '@/lib/firebase';

import { GoogleAuthProvider, signInWithCredential, signInWithRedirect, OAuthProvider } from 'firebase/auth';

export const { handlers, auth, signIn, signOut } = NextAuth({
  providers: [
    CredentialsProvider({
      name: 'FireBase Auth',

      async authorize(credentials) {
        try {
          if (credentials.provider == 'firebase_google') {
            const idToken = (credentials.idToken as string) ?? '';
            const accessToken = (credentials.accessToken as string) ?? '';
            const credential = GoogleAuthProvider.credential(idToken);
            const userCredential = await signInWithCredential(googleAuth, credential);
            const user = userCredential.user;
            if (user) {
              const token = await user.getIdToken();
              const userJson: any = await user.toJSON();

              return {
                id: userJson.uid,
                name: user<PERSON><PERSON>.displayName,
                email: userJson.email,
                accessToken: accessToken,
                provider: credentials.provider,
                idToken: idToken,
                picture: userJson.photoURL,
                mode: credentials.mode,
                locale: credentials.locale
              } as User;
            } else {
              return null;
            }
          } else if (credentials.provider == 'firebase_microsoft') {
            const idToken = (credentials.idToken as string) ?? '';
            const accessToken = (credentials.accessToken as string) ?? '';
            const credential = credentials.credential;

            const user: any = JSON.parse(credentials.user as string);
            if (user) {
              return {
                id: user.uid,
                name: user.displayName,
                email: user.email,
                accessToken: accessToken,
                provider: credentials.provider,
                idToken: idToken,
                picture: '',
                mode: credentials.mode,
                locale: credentials.locale
              } as User;
            } else {
              return null;
            }
          }
        } catch (error) {
          console.error('Firebase Google login error', error);
          return null;
        }
      }
    }),
    GoogleProvider({
      clientId: process.env.AUTH_GOOGLE_ID ?? '',
      clientSecret: process.env.AUTH_GOOGLE_SECRET ?? '',
      authorization: {
        params: {
          // prompt: 'select_account'
          // access_type: 'offline',
          // response_type: 'code'
        }
      }
    })
  ],

  secret: process.env.AUTH_SECRET,

  session: {
    strategy: 'jwt',
    maxAge: 60 * 60 * 2 // Session expiry in seconds (2 hours)
  },

  jwt: {
    maxAge: 60 * 60 * 24 * 1 // Session expiry in seconds (1 days)
  },

  pages: { signIn: '/auth/signin', signOut: '/auth/signout', error: '/auth/signin' },

  callbacks: {
    async signIn({ user, account, profile, email, credentials }) {
      // console.log('########################### signin ###########################');
      // console.log('user =========================== \n', user, '\n===========================');
      // console.log('account =========================== \n', account, '\n===========================');
      // console.log('profile =========================== \n', profile, '\n==================');
      // console.log('email =========================== \n', email, '\n==================');
      // console.log('credentials =========================== \n', credentials, '\n==================');
      // console.log('#################################################################');
      if (account) {
        if (account.provider == 'credentials') {
          if (user.provider == 'firebase_google') {
            try {
              const response = await login(user.accessToken);

              if (user.mode == 'signup') {
                if (response.status == 404) {
                  user.redirectUrl = credentials.termsUrl + '';
                  return true;
                } else {
                  return '/' + user.locale + '/auth/signup?error=user.duplicate';
                }
              } else if (user.mode == 'signin') {
                if (response.status == 404) {
                  user.redirectUrl = credentials.termsUrl + '';
                  return true;
                } else {
                  try {
                    // user.roles = response.role.map((item: any) => item.authority);
                    user.roles = ['ROLE_USER'];
                  } catch (error2: any) {
                    console.log('firebase_google get authority', error2);
                  }
                  return true;
                }
              } else {
                return false;
              }
            } catch (error) {
              console.error('[ERROR] Get Platform Token', error);
              return false;
            }
          } else if (user.provider == 'firebase_microsoft') {
            try {
              const response = await login(user.accessToken);

              if (user.mode == 'signup') {
                if (response.status == 404) {
                  user.redirectUrl = credentials.termsUrl + '';
                  return true;
                } else {
                  return '/' + user.locale + '/auth/signup?error=user.duplicate';
                }
              } else if (user.mode == 'signin') {
                if (response.status == 404) {
                  user.redirectUrl = credentials.termsUrl + '';
                  return true;
                } else {
                  try {
                    // user.roles = response.role.map((item: any) => item.authority);
                    user.roles = ['ROLE_USER'];
                  } catch (error2: any) {
                    console.log('firebase_microsoft get authority', error2);
                  }

                  return true;
                }
              } else {
                return false;
              }
            } catch (error) {
              console.error('[ERROR] Get Platform Token', error);
              return false;
            }
          } else {
            return true;
          }
        } else {
          return false;
        }
      }
      return true;
    },

    async redirect({ url, baseUrl }) {
      // console.log('########################### redirect ###########################');
      // console.log(url, baseUrl);
      return url;
    },

    async jwt({ token, user, account, trigger, session }) {
      // console.log('########################### jwt ###########################');
      // console.log('token =========================== \n', token, '\n===========================');
      // console.log('user =========================== \n', user, '\n===========================');
      // console.log('account =========================== \n', account, '\n==================');
      // console.log('trigger =========================== \n', trigger, '\n==================');
      // console.log('session =========================== \n', session, '\n==================');
      // console.log('#################################################################');

      if (account) {
        if (account.provider == 'google') {
          token.provider = account.provider;
          token.picture = user.picture;
          try {
            const response = await getPlatformToken(account.access_token);

            token.accessToken = response.token;
            token.namespace = response.namespace;
          } catch (error) {
            console.log('[ERROR] Get Platform Token', error);
          }
        } else if (account.provider == 'credentials') {
          //Firebase 이용시
          token.provider = user.provider;
          token.picture = user.picture;
          token.roles = user.roles;

          if (user.provider == 'firebase_google') {
            try {
              const response = await getPlatformToken(user.accessToken);
              // console.log('response getPlatformToken', response);

              if (response.status == 404) {
                token.redirectUrl = user.redirectUrl;
              } else {
                token.accessToken = response.token;
                token.namespace = response.namespace;
              }
            } catch (error) {
              console.log('[ERROR] Get Platform Token', error);
            }
          } else if (user.provider == 'firebase_microsoft') {
            try {
              const response = await getPlatformToken(user.accessToken);

              if (response.status == 404 || response.status == 401) {
                token.redirectUrl = user.redirectUrl;
              } else {
                token.accessToken = response.token;
                token.namespace = response.namespace;
              }
            } catch (error) {
              console.log('[ERROR] Get Platform Token', error);
            }
          }
        }
      }

      return token;
    },

    async session({ session, token, user }) {
      // console.log('########################### session ###########################');
      session.accessToken = token.accessToken;
      session.user.namespace = token.namespace;
      session.user.roles = token.roles as string[];

      if (token.redirectUrl) {
        session.user.redirectUrl = token.redirectUrl + '';
      }

      if (process.env.NODE_ENV === 'development') {
        // session.user.email = '<EMAIL>';
        // session.user.namespace = 'nsd0dfc524';
        // session.user.email = '<EMAIL>';
        // session.user.namespace = 'nsb6d85da9';
        // session.user.email = '<EMAIL>';
        // session.user.namespace = 'ns099716fe';
        // session.user.email = '<EMAIL>';
        // session.user.namespace = 'ns7de835db';
        // session.user.email = '<EMAIL>';
        // session.user.namespace = 'nsdf317ece';
        // console.log(session.accessToken);
        //
        // session.user.email = '<EMAIL>';
        // session.user.namespace = 'ns6d63c772';
      }

      // console.log('session =========================== \n', session, '\n===========================');
      // console.log('token =========================== \n', token, '\n===========================');
      // console.log('user =========================== \n', user, '\n==================');
      // console.log('#################################################################');
      return session;
    }
  }
});

export { auth as getSession };
