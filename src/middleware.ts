import { NextResponse, type NextRequest } from 'next/server';
import { auth } from '@/auth';

import { cookies } from 'next/headers';

import createMiddleware from 'next-intl/middleware';
import { routing } from './i18n/routing';
import { isBlank } from './utils';

// Authentication paths
const matchersForAuth = ['/agent/*', '/settings/*', '/user/*', '/provision/*', '/demand/*', '/point/*', '/gate/*', '/qna/*'];
const matchersForSignIn = [
  '/',
  '/index',
  '/agent/*',
  '/auth/signup',
  '/auth/signup/input/*',
  '/auth/signup/terms/*',
  '/auth/signin',
  '/auth/success',
  '/pricetable/*',
  '/docs/*',
  '/faq/*',
  '/contactus/*',
  '/gpu/*',
  '/terms/*',
  '/node/monitoring/*',
  '/survey/*'
];

const publicPathnameRegex = RegExp(
  `^(/(${routing.locales.join('|')}))?(${matchersForSignIn.flatMap((p) => (p === '/' ? ['', '/'] : p.replace('/*', '(/.*)?'))).join('|')})/?$`,
  'i'
);

const authPathnameRegex = RegExp(
  `^(/(${routing.locales.join('|')}))?(${matchersForAuth.flatMap((p) => (p === '/' ? ['', '/'] : p.replace('/*', '(/.*)?'))).join('|')})/?$`,
  'i'
);

const intlMiddleware = createMiddleware(routing, { alternateLinks: false });

// const authMiddleware = auth((req) => {
//   const { nextUrl } = req;
//   const isLoggedIn = !!req.auth;
//   const cookie = cookies();
//   const locale = cookie.get('NEXT_LOCALE')?.value ?? 'ko';

//   const isPublicRoute = publicPathnameRegex.test(nextUrl.pathname);
//   const isAuthRoute = authPathnameRegex.test(nextUrl.pathname);

//   if (!isLoggedIn && !isPublicRoute) {
//     return Response.redirect(new URL(`/${locale}/auth/signin`, nextUrl));
//   }

//   if (isLoggedIn) {
//     return intlMiddleware(req); // Apply internationalization for logged-in users
//   }
//   return;
// });

export default async function middleware(request: NextRequest) {
  const cookie = cookies();

  const localeTemp = cookie.get('NEXT_LOCALE')?.value ?? 'ko';
  const locale = isBlank(localeTemp) ? 'ko' : localeTemp;
  const session = await auth();

  const isPublicPage = publicPathnameRegex.test(request.nextUrl.pathname);
  const isAuthPage = authPathnameRegex.test(request.nextUrl.pathname);
  // console.log('########### middleware #############');

  // console.log((await auth())?.accessToken);
  // console.log(await auth());
  // console.log('routing.locales :: ', routing.locales);
  // console.log('locale  :: ', locale);
  // console.log('request :: ', request);
  // console.log('request.nextUrl :: ', request.nextUrl);
  // console.log('request.nextUrl.pathname :: ', request.nextUrl.pathname);
  // console.log('isPublicPage :: ', isPublicPage);
  // console.log('isAuthPage :: ', isAuthPage);

  if (
    request.nextUrl.pathname == '' ||
    request.nextUrl.pathname == '/' ||
    request.nextUrl.pathname == `/${locale}` ||
    request.nextUrl.pathname == `/${locale}/`
  ) {
    return Response.redirect(new URL(`/${locale}/index`, request.url), 301);
  }

  if (isPublicPage) {
    const response = intlMiddleware(request);
    response.headers.set('canonical', request.nextUrl.pathname);
    return response;
  } else {
    if (session != undefined && session.user && isAuthPage) {
      const response = intlMiddleware(request);

      response.headers.set('canonical', request.nextUrl.pathname);
      return response;
    } else {
      //원본
      // return Response.redirect(new URL(`/${locale}/index`, request.url));

      // 2025.01.21[에이전트 다운로드 리다이렉트 처리]
      //  에이전트 다운로드 접근 시 비로그인 일 경우, 로그인 창으로 이동
      if (request.url.indexOf('/provision/node/register') > -1) {
        const loginUrl = new URL(`/${locale}/auth/signin`, request.url);

        loginUrl.searchParams.set('callbackUrl', request.nextUrl.pathname);

        return Response.redirect(loginUrl);
      } else {
        //원본
        return Response.redirect(new URL(`/${locale}/index`, request.url), 301);
      }
      //2025.01.21[에이전트 다운로드 리다이렉트 처리]
    }
  }
}

// Middleware config
export const config = { matcher: ['/((?!api|_next|.*\\..*).*)', '/(ko|en)/:path*'] };
