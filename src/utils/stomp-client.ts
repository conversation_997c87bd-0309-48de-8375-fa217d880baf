import SockJS from 'sockjs-client';
import { Client, IMessage, Stomp } from '@stomp/stompjs';

let stompClient: Client | null = null;

export function connectWebSocket(
  url: string,
  onConnectCallback?: (client: Client) => void,
  onErrorCallback?: (error: IMessage) => void
): void {
  const socket = new SockJS(url);
  stompClient = new Client({
    webSocketFactory: () => socket,
    reconnectDelay: 5000, // 자동 재연결 대기 시간(ms)
    onConnect: () => {
      if (onConnectCallback) {
        onConnectCallback(stompClient!);
      }
    },
    onStompError: (frame: IMessage) => {
      console.error('Broker reported error: ' + frame.headers['message']);
      console.error('Additional details: ' + frame.body);
      if (onErrorCallback) {
        onErrorCallback(frame);
      }
    }
  });

  stompClient.activate();
}

export function disconnectWebSocket(): void {
  if (stompClient !== null) {
    stompClient.deactivate();
  }
}

export function sendMessage(destination: string, body: any): void {
  if (stompClient && stompClient.connected) {
    stompClient.publish({
      destination: destination,
      body: JSON.stringify(body)
    });
  }
}
