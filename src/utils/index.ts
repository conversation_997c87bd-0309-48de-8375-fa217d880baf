import dayjs from '@/lib/dayjs-lib';

export const generateUrlSearchParams = (params: any) => {
  const queryParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    // Only append parameters that have defined values
    if (value !== undefined) {
      // queryParams.append(key, value == null ? '' : value.toString());
      queryParams.append(key, value.toString());
    }
  });

  return queryParams.toString();
};

export const convertYearMonth = (date: number[]) => {
  let now;

  if (date != null) {
    if (date.length === 6) {
      now = new Date(Date.UTC(date[0], date[1] - 1, date[2], date[3], date[4], date[5]));
      // return dayjs(now).format('YYYY-MM-DD HH:mm:ss');
    } else {
      now = new Date(Date.UTC(date[0], date[1] - 1, date[2], date[3], date[4]));
    }

    // return dayjs(now.getTime() + now.getTimezoneOffset() * 60000 * -1).format('YYYY-MM-DD HH:mm:ss');
    return dayjs(now).tz().format('YYYY-MM');
  } else {
    return '';
  }
};

export const convertDateTime = (date: Date) => {
  return dayjs(date).tz().format('YYYY-MM-DD HH:mm:ss');
};

export const convertDate = (date: Date, time?: string) => {
  return dayjs(date).tz().format('YYYY-MM-DD') + ' ' + time;
};

export const convertTimeStampToDateTime = (time: number) => {
  return dayjs(time).tz().format('YYYY-MM-DD HH:mm:ss');
};

export const utcTime = (date: any) => {
  if (date != null) {
    if (typeof date === 'number') {
      return dayjs(date).tz().format('YYYY-MM-DD HH:mm:ss');
    } else if (Array.isArray(date) && date.every((item) => typeof item === 'number')) {
      let now;
      if (date.length === 6) {
        now = new Date(Date.UTC(date[0], date[1] - 1, date[2], date[3], date[4], date[5]));
      } else {
        now = new Date(Date.UTC(date[0], date[1] - 1, date[2], date[3], date[4]));
      }
      return dayjs(now.getTime() + now.getTimezoneOffset() * 60000 * -1).format('YYYY-MM-DD HH:mm:ss');
    }
  } else {
    return '';
  }
};

export const dateTime = (date: any) => {
  if (date != null) {
    if (typeof date === 'number') {
      return dayjs(date).tz().format('YYYY-MM-DD HH:mm:ss');
    } else if (Array.isArray(date) && date.every((item) => typeof item === 'number')) {
      let now;
      if (date.length === 6) {
        now = new Date(Date.UTC(date[0], date[1] - 1, date[2], date[3], date[4], date[5]));
      } else {
        now = new Date(Date.UTC(date[0], date[1] - 1, date[2], date[3], date[4]));
      }
      return dayjs(now.getTime()).tz().format('YYYY-MM-DD HH:mm:ss');
    }
  } else {
    return '';
  }
};

export const dateTime2 = (date: string) => {
  let d = date.replace('Z', '').split('T');
  let dates = d[0].split('-');
  let times = d[1].split(':');
  if (times.length === 2) {
    times.push('00');
  }
  let now = new Date(
    Date.UTC(Number(dates[0]), Number(dates[1]) - 1, Number(dates[2]), Number(times[0]), Number(times[1]), Number(times[2]))
  );

  return dayjs(now.getTime()).format('YYYY-MM-DD HH:mm:ss');
};

export const mutcTime = (date: string) => {
  let d = date.split(' ');
  let dates = d[0].split('-');
  let times = d[1].split(':');
  let now = new Date(Number(dates[0]), Number(dates[1]) - 1, Number(dates[2]), Number(times[0]), Number(times[1]), Number(times[2]));
  let dd = new Date(now.getTime() + now.getTimezoneOffset() * 60000);
  return dayjs(dd).format('YYYY-MM-DD HH:mm:ss');
};

export const mutcTime2 = (date: number[]) => {
  let now;

  if (date != null) {
    if (date.length === 6) {
      now = new Date(Date.UTC(date[0], date[1] - 1, date[2], date[3], date[4], date[5]));
      // return dayjs(now).format('YYYY-MM-DD HH:mm:ss');
    } else {
      now = new Date(Date.UTC(date[0], date[1] - 1, date[2], date[3], date[4]));
    }

    return dayjs(now.getTime() + now.getTimezoneOffset() * 60000).format('YYYY-MM-DD HH:mm:ss');
  } else {
    return '';
  }
};

export const millToTime = (val: number) => {
  const seconds = Math.floor(val / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  const remainingSeconds = seconds % 60;
  const remainingMinutes = minutes % 60;
  const reSecconds = remainingSeconds < 10 ? '0' + remainingSeconds : remainingSeconds;
  const reMinutes = remainingMinutes < 10 ? '0' + remainingMinutes : remainingMinutes;
  const reHours = hours < 10 ? '0' + hours : hours;
  return reHours + ':' + reMinutes + ':' + reSecconds;
};

export const comma = (val: number) => {
  if (val == undefined || val == null) return '';
  // return val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  const [integerPart, decimalPart] = val.toString().split('.'); // 정수부와 소수부 분리
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ','); // 정수부에 쉼표 추가
  return decimalPart ? `${formattedInteger}.${decimalPart}` : formattedInteger; // 소수부를 다시 붙임
};

export const convertGigabyte = (val: number) => {
  const gb = val / 1024;
  return parseFloat(gb.toFixed(2));
};

export const isMobileUserAgent = (userAgent: string) => {
  const mobileRegex = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
  return mobileRegex.test(userAgent);
};

export const toBpsToByte = function (val: number) {
  const kb = val / 8;
  const mb = kb / 1024;
  const gb = mb / 1024;
  let res = { kb: parseFloat(kb.toFixed(2)), mb: parseFloat(mb.toFixed(2)), gb: parseFloat(gb.toFixed(2)), unit: '' };
  if (mb > 1024.0) {
    res.unit = 'GB';
  } else if (kb > 1024.0) {
    res.unit = 'MB';
  } else {
    res.unit = 'KB';
  }
  return res;
};

export const isBlank = (val: any) => {
  return typeof val == 'undefined' || val == null || val.trim() == '';
};

export const dateDay = (date: number[]) => {
  if (date != null) {
    return dayjs(new Date(Date.UTC(date[0], date[1] - 1, date[2]))).format('YYYY-MM-DD');
  } else {
    return '';
  }
};

export const convertCuda = (item: string) => {
  if (item === '12000') {
    return '12.0.0';
  } else if (item === '12010') {
    return '12.1.0';
  } else if (item === '12020') {
    return '12.2.0';
  } else if (item === '12030') {
    return '12.3.0';
  } else if (item === '12040') {
    return '12.4.0';
  } else if (item === '12050') {
    return '12.5.0';
  } else if (item === '12060') {
    return '12.6.0';
  } else if (item === '12070') {
    return '12.7.0';
  } else if (item === '12080') {
    return '12.8.0';
  } else if (item === '12090') {
    return '12.9.0';
  } else {
    return item;
  }
};

export const diffMinutes = (startTime: number[], endTime: number[]) => {
  const startDate = new Date(
    startTime[0],
    startTime[1] - 1,
    startTime[2],
    startTime[3],
    startTime[4],
    startTime[5] == undefined ? 0 : startTime[5]
  );
  const endDate = new Date(endTime[0], endTime[1] - 1, endTime[2], endTime[3], endTime[4], endTime[5] == undefined ? 0 : endTime[5]);
  const diffMs = endDate.getTime() - startDate.getTime();
  const diffMinutes = Math.floor(diffMs / 1000 / 60);
  return diffMinutes;
};
