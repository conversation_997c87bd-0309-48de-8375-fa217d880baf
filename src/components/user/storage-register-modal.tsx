'use client';
import { KTModal } from '@/metronic/core';
import ReactDOM from 'react-dom';
import { UserStorage, UserStorageRegister } from '@/types/user';
import { ChangeEvent, MouseEvent, useCallback, useEffect, useState } from 'react';
import { useLocale, useTranslations } from 'next-intl';
import { z } from 'zod';

interface RegsiterModalProps {
  userStorageRegister?: UserStorageRegister;
  type?: string;
  onModalHandler: (userStorageRegister: UserStorageRegister, type: string) => void;
}

interface PageProps {
  storageRegisterModal: boolean;
  storageRegisterModalData: RegsiterModalProps;
  onCloseModal: () => void;
}

/**
 * @brief Register Modal 컴포넌트
 * @param param0
 * @returns
 */
export default function StorageRegisterModal({ storageRegisterModal, storageRegisterModalData, onCloseModal }: PageProps) {
  const locale = useLocale();
  const t_i18n = useTranslations('i18nData');
  const [uscData, setUSCData] = useState<UserStorageRegister | null>({
    owner: '',
    description: '',
    type: 'dropbox',
    clientId: '',
    clientSecret: '',
    remotePath: '',
    capacity: 10,
    accessMode: 'ReadWriteMany',
    accessKeyId: '',
    secretAccessKey: '',
    region: ''
  });
  const [errors, setErrors] = useState<StorageRegisterError>({});
  /**
   * @brief validate 정의
   */
  const schema = z.object({
    description: z.string().min(1, t_i18n('user_storage_msg_description')),
    remotePath: z.string().min(1, t_i18n('user_storage_msg_remote_path'))
  });

  const awsSchema = z.object({
    description: z.string().min(1, t_i18n('user_storage_msg_description')),
    remotePath: z.string().min(1, t_i18n('user_storage_msg_remote_path')),
    accessMode: z.string().min(1, t_i18n('user_storage_msg_access_mode')),
    capacity: z.number({ message: t_i18n('user_storage_msg_capacity') }),
    accessKeyId: z.string().min(1, t_i18n('user_storage_msg_access_key_id')),
    secretAccessKey: z.string().min(1, t_i18n('user_storage_msg_secret_access_key')),
    region: z.string().min(1, t_i18n('user_storage_msg_region'))
  });

  /**
   * @brief validate
   * @param data
   * @param field
   * @returns
   */
  const validateForm = (data: UserStorageRegister | any, field?: keyof StorageRegisterType): StorageRegisterError => {
    try {
      if (uscData.type === 's3') {
        awsSchema.parse(data);
      } else {
        schema.parse(data);
      }
      setErrors({});
      return field ? { [field]: null } : {};
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors = error.flatten().fieldErrors;
        field ? setErrors((prevData) => ({ ...prevData, [field]: newErrors[field] })) : setErrors(newErrors);
        return field ? { [field]: newErrors[field] || null } : newErrors;
      }
      return {};
    }
  };
  //Modal 변경에 따라서 실행
  useEffect(() => {
    if (storageRegisterModal) onOpen();
    else onClose();
  }, [storageRegisterModal]);

  const onSubmit = () => {
    const newErorrs = validateForm(uscData);

    if (Object.keys(newErorrs).length === 0) {
      storageRegisterModalData.onModalHandler(uscData, '');
      onClose();
    }
  };

  const onOpen = () => {
    const modalEl = document.querySelector('#storageRegisterModal') as HTMLElement;
    const modalInt = KTModal.getInstance(modalEl);
    modalInt.on('hidden', () => {
      onCloseModal();
    });
    modalInt.show();
  };

  //Register Modal 닫기
  const onClose = () => {
    storageRegisterModalData.onModalHandler(null, '');
    const modalEl = document.querySelector('#storageRegisterModal') as HTMLElement;
    const modalInt = KTModal.getInstance(modalEl);
    modalInt.hide();
  };

  //key 값 변경 시 Data에 값 변경
  const onChangeInput = useCallback(
    (e: any) => {
      const { name, value, type } = e.target as HTMLInputElement;
      let newVal = null;
      if (type == 'number') {
        newVal = Number(value);
      } else {
        newVal = value;
      }
      setUSCData({ ...uscData, [name]: newVal });
    },
    [uscData]
  );
  /**
   * @brief Select 값 변경
   * @param e
   */
  const onChangeSelect = (e: ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.currentTarget || e.target;
    setUSCData({ ...uscData, [name]: value });
  };
  const onChangeStorage = useCallback(
    (e: any) => {
      if (uscData != undefined && uscData != null) {
        const value = e.target.getAttribute('data-value');
        setUSCData({ ...uscData, type: value });
      }
    },
    [uscData]
  );

  if (!storageRegisterModal) return null;
  return ReactDOM.createPortal(
    <>
      <div className="modal" data-modal="true" id="storageRegisterModal" data-modal-backdrop-static="true">
        <div className="modal-content top-[20%] max-w-[800px]">
          <div className="modal-header">
            <h3 className="modal-title">{t_i18n('user_label_storage_person_storage_register')}</h3>
            <button className="btn-icon btn btn-xs btn-light" onClick={() => onClose()}>
              <i className="ki-outline ki-cross"></i>
            </button>
          </div>
          <div className="modal-body">
            <div className="grid grid-cols-1 gap-5 p-4">
              <div className="flex flex-col flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
                <label className="form-label max-w-52">{t_i18n('user_label_storage_type')}</label>
                <div className="flex flex-col gap-2 md:flex-row">
                  <div className="mt-3 flex gap-2" data-tabs="true">
                    <button
                      className={`btn flex items-center gap-2.5 text-nowrap !border !border-dotted ${uscData?.type == 'dropbox' ? '!border-[#8247ff] !bg-[#FAF8FF]' : '!border-[#DBDFE9] !bg-[#FFFFFF]'} hover:!border-[#8247ff] hover:!bg-[#FAF8FF]`}
                      data-tab-toggle="true"
                      data-value="dropbox"
                      onClick={onChangeStorage}
                    >
                      <img src="/da/img/ico_dropbox40.png" className="h-[22px]" alt="" data-value="dropbox" />
                      {locale === 'ko' ? '드롭박스' : 'Dropbox'}
                    </button>
                    {/* <button
                      className={`btn flex items-center gap-2.5 text-nowrap !border !border-dotted ${uscData?.type == 'drive' ? '!border-[#8247ff] !bg-[#FAF8FF]' : '!border-[#DBDFE9] !bg-[#FFFFFF]'} hover:!border-[#8247ff] hover:!bg-[#FAF8FF]`}
                      data-tab-toggle="true"
                      data-value="drive"
                      onClick={onChangeStorage}
                    >
                      <img src="/da/img/ico_gcp40.png" className="h-[22px]" alt="" data-value="drive" />
                      구글드라이브
                    </button> */}
                    <button
                      className={`btn flex items-center gap-2.5 text-nowrap !border !border-dotted ${uscData?.type == 's3' ? '!border-[#8247ff] !bg-[#FAF8FF]' : '!border-[#DBDFE9] !bg-[#FFFFFF]'} hover:!border-[#8247ff] hover:!bg-[#FAF8FF]`}
                      data-tab-toggle="true"
                      data-value="s3"
                      onClick={onChangeStorage}
                    >
                      <img src="/da/img/ico_aws_s340.png" className="h-[22px]" alt="" data-value="s3" />
                      AWS S3
                    </button>
                  </div>
                </div>
              </div>
              {uscData?.type === 's3' && (
                <>
                  <div className="flex flex-col flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
                    <label className="form-label max-w-52">Access Key ID</label>
                    <div className="flex w-full flex-col gap-2">
                      <input className="input" name="accessKeyId" type="text" value={uscData?.accessKeyId} onChange={onChangeInput} />
                      {errors.accessKeyId && (
                        <>
                          <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">{errors.accessKeyId}</span>
                        </>
                      )}
                    </div>
                  </div>
                  <div className="flex flex-col flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
                    <label className="form-label max-w-52">Secret Access Key</label>
                    <div className="flex w-full flex-col gap-2">
                      <input
                        className="input"
                        name="secretAccessKey"
                        type="text"
                        value={uscData?.secretAccessKey}
                        onChange={onChangeInput}
                      />
                      {errors.secretAccessKey && (
                        <>
                          <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">
                            {errors.secretAccessKey}
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                  <div className="flex flex-col flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
                    <label className="form-label max-w-52">Region</label>
                    <div className="flex w-full flex-col gap-2">
                      <input className="input" name="region" type="text" value={uscData?.region} onChange={onChangeInput} />
                      {errors.region && (
                        <>
                          <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">{errors.region}</span>
                        </>
                      )}
                    </div>
                  </div>
                </>
              )}
              <div className="flex flex-col flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
                <label className="form-label max-w-52">{t_i18n('user_label_storage_description')}</label>
                <div className="flex w-full flex-col gap-2">
                  <input className="input" name="description" type="text" value={uscData?.description} onChange={onChangeInput} />
                  {errors.description && (
                    <>
                      <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">{errors.description}</span>
                    </>
                  )}
                </div>
              </div>
              <div className="flex flex-col flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
                <label className="form-label w-full">
                  {/* 저장소 경로 (네트워크 드라이버의 {uscData?.type === 's3' ? '버킷/디렉토리' : '디렉토리'}를 입력하십시오.) */}
                  {t_i18n('user_label_storage_remote_path', {
                    0: uscData?.type === 's3' ? t_i18n('user_label_storage_s3_directory') : t_i18n('user_label_storage_directory')
                  })}
                </label>
                <div className="flex w-full flex-col gap-2">
                  <input
                    className="input"
                    name="remotePath"
                    type="text"
                    placeholder={
                      uscData?.type === 's3' ? t_i18n('user_label_storage_s3_directory') : t_i18n('user_label_storage_directory')
                    }
                    value={uscData?.remotePath}
                    onChange={onChangeInput}
                  />
                  {errors.remotePath && (
                    <>
                      <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">{errors.remotePath}</span>
                    </>
                  )}
                </div>
              </div>
              <div className="flex flex-col flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
                <label className="form-label w-full">{t_i18n('user_label_storage_access_mode')}</label>
                <div className="flex w-full flex-col gap-2">
                  <select className="select w-40" name="accessMode" value={uscData?.accessMode} onChange={onChangeSelect}>
                    <option value="ReadWriteMany">ReadWriteMany</option>
                    <option value="ReadWriteOnce">ReadWriteOnce</option>
                  </select>
                  {errors.accessMode && (
                    <>
                      <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">{errors.accessMode}</span>
                    </>
                  )}
                </div>
              </div>
              <div className="flex flex-col flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
                <label className="form-label w-full">{t_i18n('user_label_storage_capacity')}</label>
                <div className="flex w-full flex-col gap-2">
                  <div className="input w-40 text-right" data-toggle-password="true" data-toggle-password-permanent="true">
                    <input name="capacity" type="number" min="1" max="9999" value={uscData?.capacity} onChange={onChangeInput} /> Gi
                  </div>
                  {errors.capacity && (
                    <>
                      <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">{errors.capacity}</span>
                    </>
                  )}
                </div>
              </div>
              <div className="modal-footer justify-end">
                <div className="flex gap-4">
                  <button className="btn btn-light" onClick={(e: MouseEvent<HTMLButtonElement>) => onClose()}>
                    {t_i18n('but_cancel')}
                  </button>
                  <button
                    className="btn btn-primary"
                    onClick={(e: MouseEvent<HTMLButtonElement>) => {
                      e.preventDefault();
                      onSubmit();
                    }}
                  >
                    {t_i18n('but_register')}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>,
    document.getElementById('global_modal')
  );
}
