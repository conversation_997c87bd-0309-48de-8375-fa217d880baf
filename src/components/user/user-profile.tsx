'use client';

import { User } from '@/types/user';
import { useEffect, useState } from 'react';
import UserModal from './user-modal';

import { updateUser } from '@/action/user-action';

import AlertModal from '@/components/modal/alert-modal';

import { useLocale, useTranslations } from 'next-intl';

interface PageProps {
  user: User | null;
}

/**
 * @brief 사용자 프로파일 페이지
 * @param param0
 * @returns
 */
export default function UserProfile({ user }: PageProps) {
  const locale = useLocale();
  const t_i18n = useTranslations('i18nData');

  const [userData, setUserData] = useState<User>(null);
  const [userModal, setUserModal] = useState<boolean>(false);
  const [alertModal, setAlertModal] = useState<boolean>(false);
  const [alertModalData, setAlertModalData] = useState<any>();

  const [modalData, setModalData] = useState<any | null>(null);

  //페이지 로드 시 최초 1회 실행
  //user 값이 들어오면 userData에 값 세팅
  useEffect(() => {
    // setTitles(['UserManagement', 'Profile']);
    setUserData(user);
  }, []);

  /**
   * @brief User Modal 열기 (수정할 항목)
   * @param key 수정할 데이터 key
   * @param value 수정할 데이터 value
   */
  const openModal = () => {
    setUserModal(false);
    setModalData({
      // title: t_i18n('user_profile_edit_title'),
      data: user,
      onModalHandler: modalHandler
    });

    setUserModal(true);
  };

  /**
   * @brief User Modal에서 전달한 값으로 사용자 수정요청
   * @param key
   * @param value
   */
  const modalHandler = (data: User) => {
    setTimeout(() => {
      setUserModal(false);
    }, 300);
    // const user: User = { ...userData, data };
    update(data);
  };

  /**
   * @brief 사용자 정보 수정
   * @param user
   */
  const update = async (user: User) => {
    const response = await updateUser({
      email: user.email,
      name: user.name,
      phone: user.phone,
      company: user.company,
      jobPosition: user.jobPosition
      // picture: user.picture,,
    });

    if (response.status != 200) {
      setAlertModalData({
        btnColor: 'btn-primary',
        title: t_i18n('dialog_title_waring'),
        content: t_i18n('user_profile_edit_msg_failed'),
        okBtn: t_i18n('but_ok')
      });

      setAlertModal(true);
    } else {
      window.location.reload();
    }
  };

  /**
   * brief 전화번호 마스킹
   * @param item
   * @returns
   */
  const maskingPhone = (item: string) => {
    if (item == undefined || item == null) return '';
    let phone = item;
    if (item.indexOf('-') == -1) {
      phone = item.replace(/(\d{3})(\d{4})(\d{4})/, '$1-$2-$3');
    }
    return phone.replace(/(\d{3})-(\d{3,4})-(\d{4})/, (match, p1, p2, p3) => {
      // 가운데 자리를 모두 *로 변환
      const maskedMiddle = p2.replace(/\d/g, '*');
      const maskedEnd = p3.replace(/(\d{2})(\d{2})/, '**$2');
      return `${p1}-${maskedMiddle}-${maskedEnd}`;
    });
  };

  return (
    <>
      <div className="flex flex-col">
        <div className="grid gap-5 lg:gap-7.5">
          <div className="card min-w-full">
            <div className="card-header">
              <h3 className="card-title">{t_i18n('user_profile_user_info')}</h3>
              <button type="button" className="btn btn-sm btn-primary" onClick={openModal}>
                {t_i18n('but_profile_edit')}
              </button>
            </div>

            <div className="card-body lg:py-7.5">
              <div className="flex flex-col gap-3">
                <div className="grid grid-cols-1 gap-3 lg:grid-cols-2">
                  <div className="flex items-center gap-1.5 max-md:w-full">
                    <span className="text-medium min-w-36 text-2sm text-gray-600">{t_i18n('user_label_email')}</span>
                    <span className="text-2sm font-semibold text-gray-900">{user.email}</span>
                  </div>
                  <div className="flex items-center gap-1.5 max-md:w-full">
                    <span className="text-medium min-w-36 text-2sm text-gray-600">{t_i18n('user_label_namespace')}</span>
                    <span className="text-2sm font-semibold text-gray-900">{user.namespace}</span>
                  </div>
                </div>
                <div className="grid grid-cols-1 gap-3 lg:grid-cols-2">
                  <div className="flex items-center gap-1.5 max-md:w-full">
                    <span className="text-medium min-w-36 text-2sm text-gray-600">{t_i18n('user_label_name')}</span>
                    <span className="text-2sm font-semibold text-gray-900">{user.name}</span>
                  </div>
                  <div className="flex items-center gap-1.5 max-md:w-full">
                    <span className="text-medium min-w-36 text-2sm text-gray-600">{t_i18n('user_label_cellphone')}</span>
                    <span className="text-2sm font-semibold text-gray-900">{maskingPhone(user.phone)}</span>
                  </div>
                </div>

                <div className="grid grid-cols-1 gap-3 lg:grid-cols-2">
                  <div className="flex items-center gap-1.5 max-md:w-full">
                    <span className="text-medium min-w-36 text-2sm text-gray-600">{t_i18n('user_label_company')}</span>
                    <span className="text-2sm font-semibold text-gray-900">{user.company}</span>
                  </div>
                  <div className="flex items-center gap-1.5 max-md:w-full">
                    <span className="text-medium min-w-36 text-2sm text-gray-600">{t_i18n('user_label_position')}</span>
                    <span className="text-2sm font-semibold text-gray-900">{user.jobPosition}</span>
                  </div>
                </div>
                <div className="grid grid-cols-1 gap-3 lg:grid-cols-2">
                  <div className="flex items-center gap-1.5 max-md:w-full">
                    <span className="text-medium min-w-36 text-2sm text-gray-600">{t_i18n('user_label_createdat')}</span>
                    <span className="text-2sm font-semibold text-gray-900">{user.createdAt}</span>
                  </div>
                  <div className="flex items-center gap-1.5 max-md:w-full">
                    <span className="text-medium min-w-36 text-2sm text-gray-600">{t_i18n('user_label_lastseen')}</span>
                    <span className="text-2sm font-semibold text-gray-900">{user.lastSeen}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {userModal && <UserModal userModal={userModal} modalData={modalData} onCloseModal={() => setUserModal(false)}></UserModal>}
      {alertModal && (
        <AlertModal alertModal={alertModal} alertModalData={alertModalData} onCloseModal={() => setAlertModal(false)}></AlertModal>
      )}
    </>
  );
}
