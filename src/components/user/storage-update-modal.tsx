'use client';
import { KTModal } from '@/metronic/core';
import ReactDOM from 'react-dom';
import { UserStorage } from '@/types/user';
import { ChangeEvent, FocusEvent, MouseEvent, useCallback, useEffect, useState } from 'react';
import { useLocale, useTranslations } from 'next-intl';
import { z } from 'zod';

interface ModalProps {
  userStorage: UserStorage;
  type: string;
  onModalHandler: (userStorage: UserStorage, type: string) => void;
}

interface PageProps {
  storageUpdateModal: boolean;
  storageUpdateModalData: ModalProps;
  onCloseModal: () => void;
}

/**
 * @brief Credential Modal 컴포넌트
 * @param param0
 * @returns
 */
export default function StorageUpdateModal({ storageUpdateModal, storageUpdateModalData, onCloseModal }: PageProps) {
  const locale = useLocale();
  const t_i18n = useTranslations('i18nData');
  const [type, setType] = useState<string>('created');
  const [userStorageData, setUserStorageData] = useState<UserStorage | null>({
    ser: 0,
    owner: '',
    description: '',
    name: '',
    type: '',
    credential: '',
    remotePath: '',
    capacity: 10,
    accessMode: 'ReadWriteMany',
    pvStatus: 'pending',
    pvcStatus: 'pending',
    createdAt: '',
    lastSeen: ''
  });
  const [errors, setErrors] = useState<StorageUpdateError>({});
  /**
   * @brief validate 정의
   */
  const schema = z.object({
    description: z.string().min(1, '개인저장소 설명을 입력하세요.'),
    remotePath: z.string().min(1, '저장소 디렉토리 경로를 입력하세요.'),
    accessMode: z.string().min(1, '저장소 접근 방식을 선택하세요.'),
    capacity: z.number({ message: '저장소 용량을 입력하세요.' })
  });

  //Modal 변경에 따라서 실행
  useEffect(() => {
    if (storageUpdateModal) onOpen();
    else onClose();
  }, [storageUpdateModal]);

  //modalData 변경에 따라서 실행
  useEffect(() => {
    if (storageUpdateModalData != null) {
      setType(storageUpdateModalData.type);
      setUserStorageData(storageUpdateModalData.userStorage);
    }
  }, []);

  /**
   * @brief validate
   * @param data
   * @param field
   * @returns
   */
  const validateForm = (data: UserStorage | any, field?: keyof StorageUpdateType): StorageUpdateError => {
    try {
      schema.parse(data);
      setErrors({});
      return field ? { [field]: null } : {};
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors = error.flatten().fieldErrors;
        field ? setErrors((prevData) => ({ ...prevData, [field]: newErrors[field] })) : setErrors(newErrors);
        return field ? { [field]: newErrors[field] || null } : newErrors;
      }
      return {};
    }
  };
  // Input Data 값 변경
  const onChangeInput = useCallback(
    (e: any) => {
      const { name, value, type } = e.target as HTMLInputElement;
      let newVal = null;
      if (type == 'number') {
        newVal = Number(value);
      } else {
        newVal = value;
      }
      setUserStorageData({ ...userStorageData, [name]: newVal });
    },
    [userStorageData]
  );
  /**
   * @brief Select 값 변경
   * @param e
   */
  const onChangeSelect = (e: ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.currentTarget || e.target;
    setUserStorageData({ ...userStorageData, [name]: value });
  };

  const onSubmit = () => {
    const newErorrs = validateForm(userStorageData);
    if (Object.keys(newErorrs).length === 0) {
      storageUpdateModalData.onModalHandler(userStorageData, type);
      onClose();
    }
  };

  const onOpen = () => {
    const modalEl = document.querySelector('#storageUpdateModal') as HTMLElement;
    const modalInt = KTModal.getInstance(modalEl);
    modalInt.on('hidden', () => {
      onCloseModal();
    });
    modalInt.show();
  };

  //Credential Modal 닫기
  const onClose = () => {
    storageUpdateModalData.onModalHandler(null, type);
    const modalEl = document.querySelector('#storageUpdateModal') as HTMLElement;
    const modalInt = KTModal.getInstance(modalEl);
    modalInt.hide();
  };

  const storageType = (type: string) => {
    if (type === 'dropbox') {
      return (
        <>
          <button
            className={`btn flex items-center gap-2.5 text-nowrap !border !border-dotted !border-[#8247ff] !bg-[#FAF8FF] hover:!border-[#8247ff] hover:!bg-[#FAF8FF]`}
            data-tab-toggle="true"
            data-value="dropbox"
          >
            <img src="/da/img/ico_dropbox40.png" className="h-[22px]" alt="" data-value="dropbox" />
            드롭박스
          </button>
        </>
      );
    } else if (type === 'drive') {
      return (
        <>
          <button
            className={`btn flex items-center gap-2.5 text-nowrap !border !border-dotted !border-[#8247ff] !bg-[#FAF8FF] hover:!border-[#8247ff] hover:!bg-[#FAF8FF]`}
            data-tab-toggle="true"
            data-value="dropbox"
          >
            <img src="/da/img/ico_gcp40.png" className="h-[22px]" alt="" data-value="dropbox" />
            구글 드라이버
          </button>
        </>
      );
    } else if (type === 's3') {
      return (
        <>
          <button
            className={`btn flex items-center gap-2.5 text-nowrap !border !border-dotted !border-[#8247ff] !bg-[#FAF8FF] hover:!border-[#8247ff] hover:!bg-[#FAF8FF]`}
            data-tab-toggle="true"
            data-value="dropbox"
          >
            <img src="/da/img/ico_aws_s340.png" className="h-[22px]" alt="" data-value="dropbox" />
            AWS S3
          </button>
        </>
      );
    } else {
      return (
        <>
          <span className="badge badge-pill badge-sm badge-outline text-nowrap !px-4 !text-[11px]">Unknown</span>
        </>
      );
    }
  };

  if (!storageUpdateModal) return null;
  return ReactDOM.createPortal(
    <>
      <div className="modal" data-modal="true" id="storageUpdateModal" data-modal-backdrop-static="true">
        <div className="modal-content top-[20%] max-w-[800px]">
          <div className="modal-header">
            <h3 className="modal-title">개인 저장소 정보 수정</h3>
            <button className="btn-icon btn btn-xs btn-light" onClick={() => onClose()}>
              <i className="ki-outline ki-cross"></i>
            </button>
          </div>
          <div className="modal-body">
            <div className="grid grid-cols-1 gap-5 p-4">
              <div className="flex flex-col flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
                <label className="form-label max-w-52">저장소 유형</label>
                <div className="flex w-full flex-col gap-2">{storageType(userStorageData?.type)}</div>
              </div>
              <div className="flex flex-col flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
                <label className="form-label max-w-52">저장소 설명</label>
                <div className="flex w-full flex-col gap-2">
                  <input className="input" name="description" type="text" value={userStorageData?.description} onChange={onChangeInput} />
                  {errors.description && (
                    <>
                      <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">{errors.description}</span>
                    </>
                  )}
                </div>
              </div>
              <div className="flex flex-col flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
                <label className="form-label w-full">
                  저장소 경로 (네트워크 드라이버의 {userStorageData?.type === 's3' ? '버킷/디렉토리' : '디렉토리'}를 입력하십시오.)
                </label>
                <div className="flex w-full flex-col gap-2">
                  <input
                    className="input"
                    name="remotePath"
                    type="text"
                    placeholder={userStorageData?.type === 's3' ? '버킷/디렉토리' : '디렉토리'}
                    value={userStorageData?.remotePath}
                    onChange={onChangeInput}
                  />
                  {errors.remotePath && (
                    <>
                      <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">{errors.remotePath}</span>
                    </>
                  )}
                </div>
              </div>
              <div className="flex flex-col flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
                <label className="form-label w-full">저장소 접근방식 (기본 ReadWriteMany)</label>
                <div className="flex w-full flex-col gap-2">
                  <select className="select w-40" name="accessMode" value={userStorageData?.accessMode} onChange={onChangeSelect}>
                    <option value="ReadWriteMany">ReadWriteMany</option>
                    <option value="ReadWriteOnce">ReadWriteOnce</option>
                  </select>
                  {errors.accessMode && (
                    <>
                      <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">{errors.accessMode}</span>
                    </>
                  )}
                </div>
              </div>
              <div className="flex flex-col flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
                <label className="form-label w-full">
                  저장소 용량 (네트워크 저장소 용량을 입력하십시오. 실제 용량보다 큰 값을 입력 하세오. 기본 10Gi)
                </label>
                <div className="flex w-full flex-col gap-2">
                  <div className="input w-40 text-right" data-toggle-password="true" data-toggle-password-permanent="true">
                    <input name="capacity" type="number" min="1" max="9999" value={userStorageData?.capacity} onChange={onChangeInput} /> Gi
                  </div>
                  {errors.capacity && (
                    <>
                      <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">{errors.capacity}</span>
                    </>
                  )}
                </div>
              </div>
              <div className="modal-footer justify-end">
                <div className="flex gap-4">
                  <button className="btn btn-light" onClick={(e: MouseEvent<HTMLButtonElement>) => onClose()}>
                    {t_i18n('but_cancel')}
                  </button>
                  <button
                    className="btn btn-primary"
                    onClick={(e: MouseEvent<HTMLButtonElement>) => {
                      e.preventDefault();
                      onSubmit();
                    }}
                  >
                    수정
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>,
    document.getElementById('global_modal')
  );
}
