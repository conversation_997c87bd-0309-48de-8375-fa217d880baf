'use client';

import { useLocale, useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { User, UserStorage, UserStorageRegister } from '@/types/user';
import { credentialUserStorage, deleteUserStorage, registerUserStorage } from '@/action/user-action';
import InputDragDrop from '@/components/user/input-dragdrop.tsx';
import StorageRegisterModal from '@/components/user/storage-register-modal.tsx';
import { FaQuestion } from 'react-icons/fa6';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
// @ts-ignore
import Session from 'react-session-api';

interface PageProps {
  user: User | null;
}
interface ModalProps {
  userStorageRegister: UserStorageRegister;
  type: string;
  onModalHandler: (userStorageCredential: UserStorageRegister, type: string) => void;
}
/**
 * @brief 개인 저장소
 * @param param0
 * @returns
 */
export default function UserPersonalStorage({ user }: PageProps) {
  const locale = useLocale();
  const t_i18n = useTranslations('i18nData');
  const [userData, setUserData] = useState<User>(null);
  const [file, setFile] = useState<File | null>(null);
  const [storageTokenModal, setStorageTokenModal] = useState<boolean>(false);
  const [storageTokenModalData, setStorageTokenModalData] = useState<ModalProps | null>(null);

  useEffect(() => {
    setUserData(user);
  }, []);

  const handleFileSelect = (file: File | null) => {
    if (file) {
      setFile(file);

      let fileReader = new FileReader();
      fileReader.onload = async () => {
        const pvs = parseConfig(fileReader.result.toString());
        const userStorage = {
          owner: userData.email,
          description: '',
          name: '',
          type: '',
          credential: ''
        };
        for (const pv of pvs) {
          userStorage.description = '';
          userStorage.name = '';
          userStorage.type = '';
          userStorage.credential = '';
          if (pv.name.trim() !== '' && pv.type.trim() !== '') {
            userStorage.description = pv.name;
            userStorage.name = pv.name;
            userStorage.type = pv.type;
            if (pv.type === 's3') {
              if (pv.access_key_id.trim() !== '' && pv.secret_access_key.trim() !== '' && pv.region.trim() !== '') {
                userStorage.credential = JSON.stringify({
                  access_key_id: pv.access_key_id,
                  secret_access_key: pv.secret_access_key,
                  region: pv.region
                });
              }
            } else {
              if (
                pv.token.access_token.trim() !== '' &&
                pv.token.refresh_token.trim() !== '' &&
                pv.token.token_type.trim() !== '' &&
                pv.token.expiry.trim() !== ''
              ) {
                userStorage.credential = JSON.stringify({
                  accessToken: pv.token.refresh_token,
                  refreshToken: pv.token.refresh_token,
                  expiryAt: pv.token.expiry,
                  tokenType: pv.token.token_type
                });
              }
            }
            if (userStorage.credential.trim() !== '') {
              const res = await registerUserStorage(userStorage);
              if (res.status === 200) {
                console.log(res);
              }
            } else {
              console.log(`Bad Input ${pv.name}`);
            }
          }
        }
        window.location.reload();
      };
      fileReader.readAsText(file);
    }
  };
  /**
   * Conf file parsing
   * @param text
   */
  const parseConfig = (text: string) => {
    const sections = text
      .trim()
      .split(/\[(.*?)\]/)
      .filter(Boolean);
    const result = [];

    for (let i = 0; i < sections.length; i += 2) {
      const sectionName = sections[i].trim();
      const content = sections[i + 1].trim().split('\n').filter(Boolean);
      const sectionObj = {
        name: sectionName,
        type: '',
        provider: '',
        access_key_id: '',
        secret_access_key: '',
        region: '',
        token: { access_token: '', refresh_token: '', token_type: '', expiry: '' }
      };

      content.forEach((line) => {
        const [key, value] = line.split('=').map((s) => s.trim());
        if (key === 'token') {
          try {
            // @ts-ignore
            sectionObj[key] = JSON.parse(value);
          } catch (e) {
            // @ts-ignore
            sectionObj[key] = value; // Fallback if JSON parsing fails
          }
        } else {
          // @ts-ignore
          sectionObj[key] = value;
        }
      });

      result.push(sectionObj);
    }
    return result;
  };

  /**
   * Token 수정 Modal 열기
   * @param index
   */
  const openTokenModal = () => {
    setStorageTokenModal(false);
    setStorageTokenModalData({
      userStorageRegister: {
        owner: '',
        description: '',
        type: 'dropbox',
        clientId: '',
        clientSecret: ''
      },
      type: 'register',
      onModalHandler: tokenHandler
    });
    setStorageTokenModal(true);
  };
  /**
   * @brief 수정 Modal에서 받은 데이터 처리
   * @param pv
   */
  const tokenHandler = async (userStorageCredential: UserStorageRegister) => {
    if (userStorageCredential) {
      const response = await credentialUserStorage(userStorageCredential);
      if (response.status == 200) {
        console.log(userStorageCredential);
        Session.set('userStorageCredential', userStorageCredential);
        window.location.href = response.redirectUrl;
      }
    }
  };
  return (
    <>
      <div className="flex flex-col">
        <div className="grid gap-5 lg:gap-7.5">
          <div className="card min-w-full">
            <div className="card-header">
              <h3 className="card-title">{t_i18n('user_label_storage_person_storage_register')}</h3>
              <Link
                locale={locale}
                href={`https://data-alliance.github.io/gai-platform-docs/`}
                className="btn btn-sm btn-link"
                target="_blank"
              >
                <FaQuestion size="1.3em" />
              </Link>
              <button className="btn btn-primary" onClick={(e) => openTokenModal()}>
                개인저장소등록
              </button>
            </div>

            <div className="card-body lg:py-7.5">
              <div className="flex flex-col gap-3">
                <div className="grid grid-cols-1">
                  <div className="flex w-full items-center gap-1.5">
                    <InputDragDrop
                      onChangeFile={handleFileSelect}
                      description={t_i18n('user_label_storage_upload_file')}
                      validExtensions={['conf']}
                    ></InputDragDrop>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {storageTokenModal && (
        <StorageRegisterModal
          storageRegisterModal={storageTokenModal}
          storageRegisterModalData={storageTokenModalData}
          onCloseModal={() => {
            setStorageTokenModal(false);
          }}
        ></StorageRegisterModal>
      )}
    </>
  );
}
