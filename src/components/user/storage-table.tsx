'use client';
import { useEffect, useState } from 'react';
import { TbTrash } from 'react-icons/tb';
import { useLocale, useTranslations } from 'next-intl';
import { UserStorage, UserStorageRegister } from '@/types/user';
import { credentialUserStorage, deleteUserStorage, registerUserStorage, updateUserStorage } from '@/action/user-action';
import { dateTime } from '@/utils';
import AlertModal from '@/components/modal/alert-modal';
import ConfirmModal from '@/components/modal/confirm-modal';
import StorageRegisterModal from '@/components/user/storage-register-modal.tsx';
import StorageUpdateModal from '@/components/user/storage-update-modal.tsx';

interface RegisterModalProps {
  onModalHandler: (userStorageRegister: UserStorageRegister) => void;
}
interface ModalProps {
  userStorage: UserStorage;
  type: string;
  onModalHandler: (userStorage: UserStorage, type: string) => void;
}

interface PageProps {
  owner: string;
  userStorages: UserStorage[];
}

/**
 * @brief Credential Table 컴포넌트
 * @param param  User Page에서 전달한 email, credentials 데이터
 * @returns
 */
export default function StorageTable({ owner, userStorages }: PageProps) {
  const locale = useLocale();
  const t_i18n = useTranslations('i18nData');

  const [storageRegisterModal, setStorageRegisterModal] = useState<boolean>(false);
  const [storageRegisterModalData, setStorageRegisterModalData] = useState<RegisterModalProps | null>(null);

  const [storageUpdateModal, setStorageUpdateModal] = useState<boolean>(false);
  const [storageUpdateModalData, setStorageUpdateModalData] = useState<ModalProps | null>(null);

  const [alertModal, setAlertModal] = useState<boolean>(false);
  const [alertModalData, setAlertModalData] = useState<any>();

  const [confirmModal, setConfirmModal] = useState<boolean>(false);
  const [confirmModalData, setConfirmModalData] = useState<any>();

  /**
   * 등록 Modal 열기
   * @param index
   */
  const openRegisterModal = () => {
    setStorageRegisterModal(false);
    setStorageRegisterModalData({
      onModalHandler: registerHandler
    });
    setStorageRegisterModal(true);
  };
  /**
   * @brief Modal에서 받은 데이터 처리
   * @param pv
   */
  const registerHandler = async (userStorage: UserStorageRegister) => {
    if (userStorage) {
      setStorageRegisterModal(false);
      if (userStorage.type === 's3') {
        const body = {
          owner: '',
          type: userStorage.type,
          description: userStorage.description,
          remotePath: userStorage.remotePath,
          capacity: userStorage.capacity,
          accessMode: userStorage.accessMode,
          accessKeyId: userStorage.accessKeyId,
          secretAccessKey: userStorage.secretAccessKey,
          region: userStorage.region
        };
        const response = await registerUserStorage(body);
        if (response.status != 200) {
          setAlertModalData({
            btnColor: 'btn-primary',
            title: t_i18n('dialog_title_waring'),
            content: '개인저장소 등록 실패.',
            okBtn: t_i18n('but_ok')
          });
          setAlertModal(true);
        } else {
          window.location.reload();
        }
      } else {
        const response = await credentialUserStorage({
          owner: '',
          description: userStorage.description,
          type: userStorage.type,
          remotePath: userStorage.remotePath.trim(),
          capacity: userStorage.capacity,
          accessMode: userStorage.accessMode
        });
        if (response.status != 200) {
          setAlertModalData({
            btnColor: 'btn-primary',
            title: t_i18n('dialog_title_waring'),
            content: '개인저장소 등록 실패.',
            okBtn: t_i18n('but_ok')
          });
          setAlertModal(true);
        } else {
          window.location.href = response.redirectUrl;
        }
      }
    }
  };
  /**
   * 수정 Modal 열기
   * @param index
   */
  const openUpdateModal = (index?: number) => {
    setStorageUpdateModal(false);
    setStorageUpdateModalData({
      userStorage: userStorages[index],
      type: 'update',
      onModalHandler: updateHandler
    });
    setStorageUpdateModal(true);
  };
  /**
   * @brief Modal에서 받은 데이터 처리
   * @param userStorage
   * @param type
   */
  const updateHandler = async (userStorage: UserStorage, type: string) => {
    if (userStorage) {
      setStorageRegisterModal(false);
      const response = await updateUserStorage({
        ser: userStorage.ser,
        owner: userStorage.owner,
        description: userStorage.description,
        type: userStorage.type,
        remotePath: userStorage.remotePath,
        accessMode: userStorage.accessMode,
        capacity: userStorage.capacity
      });
      if (response.status == 200) {
        window.location.reload();
      } else {
        let content = '';
        if (response.status == 401) {
          content = t_i18n('user_label_storage_result_message01');
        } else if (response.status == 404) {
          if (response.message === 'User not found') {
            content = t_i18n('user_label_storage_result_message02');
          } else if (response.message === 'User PV not found') {
            content = t_i18n('user_label_storage_result_message03');
          }
        } else if (response.status == 400) {
          if (response.message === 'Secret creation error') {
            content = t_i18n('user_label_storage_result_message04');
          } else if (response.message === 'Persistent Volume Claim creation error') {
            content = t_i18n('user_label_storage_result_message05');
          } else if (response.message === 'Persistent Volume creation error') {
            content = t_i18n('user_label_storage_result_message06');
          }
        } else if (response.status == 500) {
          content = t_i18n('user_label_storage_result_message07');
        }
        setAlertModalData({
          btnColor: 'btn-primary',
          title: t_i18n('dialog_title_waring'),
          content: content,
          okBtn: t_i18n('but_ok')
        });
        setAlertModal(true);
      }
    }
  };
  /**
   * 삭제 Modal 열기
   * @param color
   * @param index
   */
  const openDeleteModal = (color: string, index: number) => {
    const userStorage = userStorages[index];
    setConfirmModalData({
      btnColor: color,
      title: t_i18n('user_label_storage_delete'),
      content: t_i18n('user_label_storage_delete_message'),
      okBtn: t_i18n('but_ok'),
      data: userStorage,
      onConfirmHandler: deleteHandler
    });

    setConfirmModal(true);
  };
  /**
   * @brief Confirm Modal에서 받은 데이터 처리
   * @param userStorage
   */
  const deleteHandler = async (userStorage: UserStorage) => {
    const response = await deleteUserStorage(userStorage.ser);
    if (response.status != 200) {
      setAlertModalData({
        btnColor: 'btn-primary',
        title: t_i18n('dialog_title_waring'),
        content: t_i18n('user_label_storage_delete_failed'),
        okBtn: t_i18n('but_ok')
      });
      setAlertModal(true);
    } else {
      window.location.reload();
    }
  };

  const convertStorages = (type: string) => {
    if (type == 'drive') {
      return (
        <div className="flex items-center justify-center">
          <img src="/da/img/ico_gcp40.png" className="w-[22px]" alt="" />
        </div>
      );
    } else if (type == 'dropbox') {
      return (
        <div className="flex items-center justify-center">
          <img src="/da/img/ico_dropbox40.png" className="h-[22px]" alt="" />
        </div>
      );
    } else if (type == 's3') {
      return (
        <div className="flex items-center justify-center">
          <img src="/da/img/ico_aws_s340.png" className="h-[22px]" alt="" />
        </div>
      );
    } else {
      return <div className="flex h-full"></div>;
    }
  };

  const pvStatus = (st: string) => {
    if (st === 'pending') {
      return (
        <>
          <span className="badge-gray badge badge-pill badge-sm badge-outline text-nowrap !px-4 !text-[11px]">Pending</span>
        </>
      );
    } else if (st === 'bound') {
      return (
        <>
          <span className="badge badge-pill badge-sm badge-outline badge-success text-nowrap !px-4 !text-[11px]">Bound</span>
        </>
      );
    } else if (st === 'available') {
      return (
        <>
          <span className="badge badge-pill badge-sm badge-outline badge-success text-nowrap !px-4 !text-[11px]">Available</span>
        </>
      );
    } else if (st === 'released') {
      return (
        <>
          <span className="badge badge-pill badge-sm badge-outline badge-info text-nowrap !px-4 !text-[11px]">Released</span>
        </>
      );
    } else if (st === 'failed') {
      return (
        <>
          <span className="badge badge-pill badge-sm badge-outline badge-danger text-nowrap !px-4 !text-[11px]">Failed</span>
        </>
      );
    } else {
      return (
        <>
          <span className="badge badge-pill badge-sm badge-outline text-nowrap !px-4 !text-[11px]">Standby</span>
        </>
      );
    }
  };
  const pvcStatus = (st: string) => {
    if (st === 'pending') {
      return (
        <>
          <span className="badge-gray badge badge-pill badge-sm badge-outline text-nowrap !px-4 !text-[11px]">Pending</span>
        </>
      );
    } else if (st === 'bound') {
      return (
        <>
          <span className="badge badge-pill badge-sm badge-outline badge-success text-nowrap !px-4 !text-[11px]">Bound</span>
        </>
      );
    } else if (st === 'lost') {
      return (
        <>
          <span className="badge badge-pill badge-sm badge-outline badge-danger text-nowrap !px-4 !text-[11px]">Lost</span>
        </>
      );
    } else {
      return (
        <>
          <span className="badge badge-pill badge-sm badge-outline text-nowrap !px-4 !text-[11px]">Standby</span>
        </>
      );
    }
  };
  const expiry = (date: string) => {
    const d = date.substring(0, 10);
    const t = date.substring(11, 19);
    return d + ' ' + t;
  };
  return (
    <>
      <div className="flex flex-col">
        <div className="grid gap-5 lg:gap-7.5">
          <div className="card card-grid min-w-full">
            <div className="card-header">
              <div className="flex w-full items-center justify-between">
                <div className="flex flex-col">
                  <h3 className="card-title pb-2">{t_i18n('user_label_storage_list')}</h3>
                  <h3 className="pl-2 text-[13px] font-medium leading-[20px] text-gray-600">
                    {t_i18n('user_label_storage_table_header_message01')}
                  </h3>
                  <h3 className="pl-2 text-[13px] font-medium leading-[20px] text-gray-600">
                    {t_i18n('user_label_storage_table_header_message02')}
                    {'  '}
                    <a
                      className="link pl-2 underline decoration-dashed"
                      target="_blank"
                      href="https://data-alliance.github.io/gai-platform-docs/user-guide/workload/pv-user-guide/"
                    >
                      {t_i18n('user_label_storage_table_docs_link')}
                    </a>
                  </h3>
                </div>
                <div className="flex items-end gap-5">
                  <button className="btn btn-primary" onClick={(e) => openRegisterModal()}>
                    {t_i18n('user_label_storage_person_storage_register')}
                  </button>
                </div>
              </div>
            </div>
            <div className="card-body scrollable-x-auto">
              <table className="table text-center">
                <thead>
                  <tr>
                    <th className="min-w-20 text-nowrap">{t_i18n('user_label_storage_table_th_type')}</th>
                    <th className="min-w-32 text-nowrap">{t_i18n('user_label_storage_table_th_desc')}</th>
                    <th className="min-w-32 text-nowrap">{t_i18n('user_label_storage_table_th_remote_path')}</th>
                    <th className="min-w-32 text-nowrap">{t_i18n('user_label_storage_table_th_capacity')}</th>
                    <th className="min-w-32 text-nowrap">{t_i18n('user_label_storage_table_th_pv_status')}</th>
                    <th className="min-w-32 text-nowrap">{t_i18n('user_label_storage_table_th_pvc_status')}</th>
                    <th className="min-w-32 text-nowrap">{t_i18n('user_label_storage_table_th_created_at')}</th>
                    <th className="w-[30px]"></th>
                  </tr>
                </thead>
                <tbody>
                  {userStorages.length > 0 &&
                    userStorages.map((item, index) => (
                      <tr key={`pvTable_${index}`}>
                        <td className="h-[57px]">{convertStorages(item.type)}</td>
                        <td>{item.description}</td>
                        <td>{item.remotePath}</td>
                        <td>{item.capacity} Gi</td>
                        <td>{pvStatus(item.pvStatus)}</td>
                        <td>{pvcStatus(item.pvcStatus)}</td>
                        <td className="text-sm text-gray-700">{item.createdAt == null ? '--' : dateTime(item.createdAt)}</td>
                        <td className="text-left">
                          <div className="menu" data-menu="true">
                            <div
                              className="menu-item menu-item-dropdown"
                              data-menu-item-offset="0, 10px"
                              data-menu-item-placement="bottom-end"
                              data-menu-item-toggle="dropdown"
                              data-menu-item-trigger="click|lg:click"
                            >
                              <button className="menu-toggle btn-icon btn btn-sm btn-clear btn-light">
                                <i className="ki-filled ki-dots-vertical"></i>
                              </button>
                              <div className="menu-dropdown menu-default w-full max-w-[175px]" data-menu-dismiss="true">
                                {/*<div className="menu-item">*/}
                                {/*  <button type="button" className="menu-link" onClick={() => openUpdateModal(index)}>*/}
                                {/*    <span className="menu-icon">*/}
                                {/*      <FaRegHardDrive size="1.3em" />*/}
                                {/*    </span>*/}
                                {/*    <span className="menu-title">정보수정</span>*/}
                                {/*  </button>*/}
                                {/*</div>*/}
                                <div className="menu-item">
                                  <button type="button" className="menu-link" onClick={() => openDeleteModal('btn-danger', index)}>
                                    <span className="menu-icon">
                                      <TbTrash size="1.3em" />
                                    </span>
                                    <span className="menu-title">{t_i18n('but_delete')}</span>
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </td>
                      </tr>
                    ))}

                  {userStorages.length == 0 && (
                    <tr>
                      <td colSpan={8} className="h-[57px]">
                        <div className="flex flex-col items-center p-10">
                          <img src="/da/img/nocontent_common2.svg" className="w-[200px]" alt="no content" />
                          {t_i18n('msg_no_content')}
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {alertModal && (
        <AlertModal
          alertModal={alertModal}
          alertModalData={alertModalData}
          onCloseModal={() => {
            setAlertModal(false);
          }}
        ></AlertModal>
      )}

      {confirmModal && (
        <ConfirmModal
          confirmModal={confirmModal}
          confirmModalData={confirmModalData}
          onCloseModal={() => {
            setConfirmModal(false);
            setStorageRegisterModal(false);
            setStorageUpdateModal(false);
          }}
        ></ConfirmModal>
      )}
      {storageRegisterModal && (
        <StorageRegisterModal
          storageRegisterModal={storageRegisterModal}
          storageRegisterModalData={storageRegisterModalData}
          onCloseModal={() => {
            setStorageRegisterModal(false);
          }}
        ></StorageRegisterModal>
      )}
      {storageUpdateModal && (
        <StorageUpdateModal
          storageUpdateModal={storageUpdateModal}
          storageUpdateModalData={storageUpdateModalData}
          onCloseModal={() => {
            setConfirmModal(false);
            setStorageUpdateModal(false);
            setStorageRegisterModal(false);
          }}
        ></StorageUpdateModal>
      )}
    </>
  );
}
