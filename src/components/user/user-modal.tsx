'use client';
import { KTModal } from '@/metronic/core';

import ReactDOM from 'react-dom';

import { ChangeEvent, FocusEvent, MouseEvent, useEffect, useState } from 'react';
import { useLocale, useTranslations } from 'next-intl';
import { User } from '@/types/user';
import { z } from 'zod';

interface ModalProps {
  title: string;
  data: any;
  onModalHandler: (data: any) => void;
}

interface PageProps {
  userModal: boolean;
  modalData: ModalProps;
  onCloseModal: () => void;
}

class UserDto {
  email: string = '';
  name: string = '';
  phone: string = '';
  company: string = '';
  jobPosition: string = '';
}

/**
 * @brief User Modal 컴포넌트
 * @param param0
 * @returns
 */
export default function UserModal({ userModal, modalData, onCloseModal }: PageProps) {
  const locale = useLocale();
  const t_i18n = useTranslations('i18nData');
  const [eventId, setEventId] = useState<string>('');
  const [data, setData] = useState<UserDto>(new UserDto());

  const [errors, setErrors] = useState<UserError>({});
  /**
   * @brief validate 정의
   */
  const schema = z.object({
    name: z.string().min(1, t_i18n('signup_msg_name_required')),
    phone: z
      .string()
      .min(1, t_i18n('signup_msg_phone_required'))
      .regex(/^0\d{1,2}-?\d{3,4}-?\d{4}$/, t_i18n('signup_msg_phone_invalid'))
  });

  /**
   * @brief validate
   * @param data
   * @param field
   * @returns
   */
  const validateForm = (data: User | any, field?: keyof UserType) => {
    try {
      schema.parse(data);
      setErrors({});
      return field ? { [field]: null } : {};
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors = error.flatten().fieldErrors;

        field ? setErrors((prevData) => ({ ...prevData, [field]: newErrors[field] })) : setErrors(newErrors);

        return field ? { [field]: newErrors[field] || null } : newErrors;
      }
      return {};
    }
  };

  /**
   * @brief INPUT Fouce blur 이벤트 발생 시 validate
   * @param e
   */
  const onBlurHandler = (e: FocusEvent<HTMLInputElement>) => {
    const name = e.target.name;
    let value = e.target.value;

    if (name == 'phone') {
      const onlyNumbers = value.replace(/[^0-9]/g, '');

      if (onlyNumbers.length == 10) {
        const formatted = onlyNumbers.replace(/(\d{3})(\d{3})(\d{4})/, '$1-$2-$3');

        value = formatted;
      } else if (onlyNumbers.length == 11) {
        const formatted = onlyNumbers.replace(/(\d{3})(\d{4})(\d{4})/, '$1-$2-$3');

        value = formatted;
      }
      const phoneData = { ...data, [name]: value };
      setData((prevData) => phoneData);
    }

    const updateFormData = { ...data, [name]: value };
    validateForm(updateFormData, name as keyof UserType);
  };

  //userModal 변경에 따라서 실행
  useEffect(() => {
    if (userModal) onOpen();
    else onClose();
  }, [userModal]);

  //modalData 변경에 따라서 실행
  useEffect(() => {
    let phone = modalData.data.phone;
    if (modalData.data.phone == undefined || modalData.data.phone == null) {
      phone = '';
    } else {
      phone = modalData.data.phone;
      if (modalData.data.phone.indexOf('-') == -1) {
        phone = modalData.data.phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1-$2-$3');
      }
    }

    const updateFormData = {
      email: modalData.data.email,
      name: modalData.data.name,
      phone: phone,
      company: modalData.data.company,
      jobPosition: modalData.data.jobPosition
    };
    setData(updateFormData);
  }, []);

  //Input 컴포넌트 값 변경 시 value값 변경
  const onChangeHandler = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.currentTarget;
    setData((prevData) => {
      return { ...prevData, [name]: value };
    });
  };

  /**
   * @brief user-profile 컴퍼넌트로 값 전달
   */
  const onSubmit = () => {
    const newErrors = validateForm(data);

    if (Object.keys(newErrors).length == 0) {
      modalData.onModalHandler(data);
      onClose();
    }
  };

  /**
   * @brief User Modal 열기
   */
  const onOpen = () => {
    const modalEl = document.querySelector('#userModal') as HTMLElement;
    const modalInt = KTModal.getInstance(modalEl);
    const eId = modalInt.on('hidden', () => {
      onCloseModal();
    });
    setEventId(eId);
    modalInt.show();
  };

  /**
   * @brief User Modal 닫기
   */
  const onClose = () => {
    onCloseModal();

    const modalEl = document.querySelector('#userModal') as HTMLElement;
    const modalInt = KTModal.getInstance(modalEl);
    modalInt.off('hidden', eventId);
    modalInt.hide();
  };

  if (!modalData) return null;
  return ReactDOM.createPortal(
    <>
      <div className="modal" data-modal="true" id="userModal">
        <div className="modal-content top-[20%] max-w-[600px]">
          <div className="modal-header">
            <h3 className="modal-title">{t_i18n('user_profile_edit_title')}</h3>
            <button className="btn-icon btn btn-xs btn-light" onClick={() => onClose()}>
              <i className="ki-outline ki-cross"></i>
            </button>
          </div>
          <div className="modal-body">
            <div className="flex flex-col gap-6">
              <div className="flex items-center gap-1.5 max-md:w-full">
                <span className="text-medium min-w-36 text-2sm text-gray-600">{t_i18n('user_label_email')}</span>
                <input
                  id="input"
                  className="input"
                  data-modal-input-focus="true"
                  type="text"
                  name="email"
                  value={data?.email}
                  readOnly={true}
                />
              </div>
              <div className="flex items-center gap-1.5 max-md:w-full">
                <span className="text-medium min-w-36 text-2sm text-gray-600">{t_i18n('user_label_name')}</span>
                <input
                  id="input"
                  className="input"
                  data-modal-input-focus="true"
                  type="text"
                  name="name"
                  value={data?.name}
                  onChange={onChangeHandler}
                  onBlur={onBlurHandler}
                />
                {errors.name && (
                  <>
                    <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">{errors.name}</span>
                  </>
                )}
              </div>
              <div className="flex items-center gap-1.5 max-md:w-full">
                <span className="text-medium min-w-36 text-2sm text-gray-600">{t_i18n('user_label_cellphone')}</span>
                <div className="flex w-full flex-col gap-2">
                  <input
                    id="input"
                    className="input"
                    data-modal-input-focus="true"
                    type="text"
                    name="phone"
                    value={data?.phone == null ? '' : data?.phone}
                    onChange={onChangeHandler}
                    onBlur={onBlurHandler}
                  />
                  {errors.phone && (
                    <>
                      <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">{errors.phone[0]}</span>
                    </>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-1.5 max-md:w-full">
                <span className="text-medium min-w-36 text-2sm text-gray-600">{t_i18n('user_label_company')}</span>
                <input
                  id="input"
                  className="input"
                  data-modal-input-focus="true"
                  type="text"
                  name="company"
                  value={data?.company}
                  onChange={onChangeHandler}
                />
              </div>
              <div className="flex items-center gap-1.5 max-md:w-full">
                <span className="text-medium min-w-36 text-2sm text-gray-600">{t_i18n('user_label_position')}</span>
                <input
                  id="input"
                  className="input"
                  data-modal-input-focus="true"
                  type="text"
                  name="jobPosition"
                  value={data?.jobPosition}
                  onChange={onChangeHandler}
                />
              </div>
            </div>
          </div>
          <div className="modal-footer justify-end">
            <div className="flex gap-4">
              <button className="btn btn-light" onClick={() => onClose()}>
                {t_i18n('but_cancel')}
              </button>
              <button
                className="btn btn-primary"
                onClick={(e: MouseEvent<HTMLButtonElement>) => {
                  onSubmit();
                }}
              >
                {t_i18n('but_modify')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </>,
    document.getElementById('global_modal')
  );
}
