'use client';
import { Credential } from '@/types/credential';
import { useEffect, useState } from 'react';
import CredentialModal from './credential-modal';
import { deleteCredential, registerCredential, updateCredential } from '@/action/user-action';
import AlertModal from '@/components/modal/alert-modal';
import ConfirmModal from '@/components/modal/confirm-modal';
import { TbTrash, TbPencil } from 'react-icons/tb';

import { useLocale, useTranslations } from 'next-intl';
import { FaCloud, FaDocker } from 'react-icons/fa6';
import { SiAmazon, SiNaver } from 'react-icons/si';

interface ModalProps {
  credential: Credential;
  type: string;
  onModalHandler: (type: string, credentail: Credential) => void;
}

interface PageProps {
  email: string;
  credentials: Credential[];
}

/**
 * @brief Credential Table 컴포넌트
 * @param param  User Page에서 전달한 email, credentials 데이터
 * @returns
 */
export default function CredentailTable({ email, credentials }: PageProps) {
  const locale = useLocale();
  const t_i18n = useTranslations('i18nData');

  const clipboardCopyHandler = async (index: number) => {
    await navigator.clipboard.writeText(credentials[index].secKey);
  };
  const [credentialModal, setCredentialModal] = useState<boolean>(false);
  const [credentialModalData, setCredentialModalData] = useState<ModalProps | null>(null);

  const [alertModal, setAlertModal] = useState<boolean>(false);
  const [alertModalData, setAlertModalData] = useState<any>();

  const [confirmModal, setConfirmModal] = useState<boolean>(false);
  const [confirmModalData, setConfirmModalData] = useState<any>();

  //Credential Modal 열기
  const openModal = (type: string, index?: number) => {
    setCredentialModal(false);

    if (type == 'edit') {
      const credentail = credentials[index];

      setCredentialModalData({
        credential: credentail,
        type: type,
        onModalHandler: modalHandler
      });
    } else {
      const credentail: Credential = {
        owner: email,
        category: 'repo',
        cloud: '',
        accKey: '',
        secKey: '',
        region: '',
        registryId: ''
      };
      setCredentialModalData({
        credential: credentail,
        type: type,
        onModalHandler: modalHandler
      });
    }

    setCredentialModal(true);
  };

  //삭제버튼 클릭 시 Confirm Modal 열기
  const openConfirmModal = (color: string, index: number) => {
    const credential = credentials[index];
    setConfirmModalData({
      btnColor: 'btn-primary',
      title: t_i18n('user_credential_delete_title'),
      content: t_i18n('user_credential_delete_msg_confirm'),
      okBtn: t_i18n('but_ok'),
      data: credential,
      onConfirmHandler: deleteHandler
    });

    setConfirmModal(true);
  };

  /**
   * @brief Credential Modal에서 받은 데이터 처리
   * @param type
   * @param credentail
   */
  const modalHandler = async (type: string, credentail: Credential) => {
    setCredentialModal(false);
    if (type == 'register') {
      register(credentail);
    } else if (type == 'edit') {
      update(credentail);
    }
  };

  /**
   * @brief credetial 등록
   * @param credentail
   */
  const register = async (credentail: Credential) => {
    const response = await registerCredential(credentail);
    if (response.status != 200) {
      setAlertModalData({
        btnColor: 'btn-primary',
        title: t_i18n('dialog_title_waring'),
        content: t_i18n('user_credential_register_msg_failed'),
        okBtn: t_i18n('but_ok')
      });
      setAlertModal(true);
    } else {
      window.location.reload();
    }
  };

  /**
   * @brief credetial 수정
   * @param credentail
   */
  const update = async (credentail: Credential) => {
    const response = await updateCredential(credentail);
    if (response.status != 200) {
      setAlertModalData({
        btnColor: 'btn-primary',
        title: t_i18n('dialog_title_waring'),
        content: t_i18n('user_credential_modify_msg_failed'),
        okBtn: t_i18n('but_ok')
      });
      setAlertModal(true);
    } else {
      window.location.reload();
    }
  };

  /**
   * @brief Confirm Modal에서 받은 데이터 처리
   * @param type
   * @param credentail
   */
  const deleteHandler = async (data: any) => {
    const response = await deleteCredential(data.category, data.cloud);
    if (response.status != 200) {
      // setTimeout(() => {
      setAlertModalData({
        btnColor: 'btn-primary',
        title: t_i18n('dialog_title_waring'),
        content: t_i18n('user_credential_delete_msg_failed'),
        okBtn: t_i18n('but_ok')
      });
      setAlertModal(true);
      // }, 300);
    } else {
      window.location.reload();
    }
  };

  const convertCategory = (category: string) => {
    if (category == 'csp') {
      return (
        <div className="flex items-center justify-center">
          <FaCloud></FaCloud>
        </div>
      );
    } else if (category == 'repo') {
      return (
        <div className="flex items-center justify-center">
          <FaDocker className="tab-active:border-b-success"></FaDocker>
        </div>
      );
    } else {
      return <div className="flex h-full"></div>;
    }
  };

  const convertCloud = (category: string, cloud: string) => {
    if (cloud == 'aws') {
      return (
        <div className="flex items-center justify-center">
          <img src="/da/img/ico_aws_ecr40.png" className="w-[22px]" alt="" />
        </div>
      );
    } else if (cloud == 'docker') {
      return (
        <div className="flex items-center justify-center">
          <img src="/da/img/ico_dockerhub_blue.svg" className="h-[22px]" alt="" />
        </div>
      );
    } else if (cloud == 'github') {
      return (
        <div className="flex items-center justify-center">
          <img src="/da/img/ico_github.svg" className="h-[22px]" alt="" />
        </div>
      );
    } else if (cloud == 'redhat') {
      return (
        <div className="flex items-center justify-center">
          <img src="/da/img/ico_redhat.svg" className="h-[22px]" alt="" />
        </div>
      );
    } else if (cloud == 'huggingface') {
      return (
        <div className="flex items-center justify-center">
          <img src="/da/img/ico_huggingface.svg" className="h-[22px]" alt="" />
        </div>
      );
    } else {
      return <div className="flex h-full"></div>;
    }
  };

  return (
    <>
      <div className="flex flex-col">
        <div className="grid gap-5 lg:gap-7.5">
          <div className="card card-grid min-w-full">
            <div className="card-header">
              <h3 className="card-title">{t_i18n('user_profile_user_credentials')}</h3>
              <div className="flex gap-5">
                <button
                  className="btn btn-sm btn-primary"
                  onClick={() => {
                    openModal('register');
                  }}
                >
                  {t_i18n('but_register_credential')}
                </button>
              </div>
            </div>
            <div className="card-body scrollable-x-auto">
              <table className="table text-center">
                <thead>
                  <tr>
                    <th className="min-w-20 text-nowrap">{t_i18n('user_credential_label_repository')}</th>
                    <th className="min-w-32 text-nowrap">
                      {t_i18n('user_credential_label_access_key')}/{t_i18n('user_credential_label_user_name')}
                    </th>
                    <th className="min-w-32 text-nowrap">
                      {t_i18n('user_credential_label_secret_key')}/{t_i18n('user_credential_label_user_password')}
                    </th>
                    <th className="w-[30px]"></th>
                  </tr>
                </thead>
                <tbody>
                  {credentials.length > 0 &&
                    credentials.map((item, index) => (
                      <tr key={`credentialTable_${index}`}>
                        <td className="h-[57px]">{convertCloud(item.category, item.cloud)}</td>
                        <td>{item.accKey}</td>
                        <td className="text-sm text-gray-700">
                          *******************
                          <button
                            className="btn-icon btn btn-sm btn-clear text-gray-500 hover:text-primary-active"
                            onClick={() => {
                              clipboardCopyHandler(index);
                            }}
                          >
                            <i className="ki-filled ki-copy"></i>
                          </button>
                        </td>
                        <td className="text-left">
                          <div className="menu" data-menu="true">
                            <div
                              className="menu-item menu-item-dropdown"
                              data-menu-item-offset="0, 10px"
                              data-menu-item-placement="bottom-end"
                              data-menu-item-toggle="dropdown"
                              data-menu-item-trigger="click|lg:click"
                            >
                              <button className="menu-toggle btn-icon btn btn-sm btn-clear btn-light">
                                <i className="ki-filled ki-dots-vertical"></i>
                              </button>
                              <div className="menu-dropdown menu-default w-full max-w-[175px]" data-menu-dismiss="true">
                                <div className="menu-item">
                                  <button type="button" className="menu-link" onClick={() => openModal('edit', index)}>
                                    <span className="menu-icon">
                                      <TbPencil size="1.3em" />
                                    </span>
                                    <span className="menu-title">{t_i18n('but_edit')}</span>
                                  </button>
                                </div>

                                <div className="menu-item">
                                  <button type="button" className="menu-link" onClick={() => openConfirmModal('btn-danger', index)}>
                                    <span className="menu-icon">
                                      <TbTrash size="1.3em" />
                                    </span>
                                    <span className="menu-title">{t_i18n('but_delete')}</span>
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </td>
                      </tr>
                    ))}

                  {credentials.length == 0 && (
                    <tr>
                      <td colSpan={5} className="h-[57px]">
                        <div className="flex flex-col items-center p-10">
                          <img src="/da/img/nocontent_common2.svg" className="w-[200px]" alt="no content" />
                          {t_i18n('msg_no_content')}
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      {credentialModal && (
        <CredentialModal
          credentialModal={credentialModal}
          credentialModalData={credentialModalData}
          onCloseModal={() => setCredentialModal(false)}
        ></CredentialModal>
      )}

      {alertModal && (
        <AlertModal
          alertModal={alertModal}
          alertModalData={alertModalData}
          onCloseModal={() => {
            setAlertModal(false);
          }}
        ></AlertModal>
      )}

      {confirmModal && (
        <ConfirmModal
          confirmModal={confirmModal}
          confirmModalData={confirmModalData}
          onCloseModal={() => {
            setConfirmModal(false);
          }}
        ></ConfirmModal>
      )}
    </>
  );
}
