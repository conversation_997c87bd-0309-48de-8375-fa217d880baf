'use client';
import { Paging } from '@/types/paging';
import { isEmpty } from 'lodash-es';
interface PageProps {
  email: string;
  name: string;
}

/**
 * @brief user profile header 컴포넌트
 * @param param0
 * @returns
 */
export function UserProfileHeading({ email, name }: PageProps) {
  return (
    <>
      <div className="container-fixed">
        <div className="flex flex-wrap items-center justify-between gap-5 pb-7.5 lg:items-end">
          <div className="flex flex-col justify-center gap-2">
            <h1 className="text-xl font-semibold leading-none text-gray-900">{email}</h1>
            <div className="flex flex-wrap items-center gap-1.5 font-medium">
              <span className="text-md text-gray-600">{name}</span>
              <span className="me-2 text-md font-semibold text-gray-800"></span>
            </div>
          </div>
          <div className="flex items-center gap-2.5">
            {/* <a className="btn btn-sm btn-primary" href="#">
              Add User
            </a> */}
          </div>
        </div>
      </div>
    </>
  );
}
