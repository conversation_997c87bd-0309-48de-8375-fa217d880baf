'use client';
import { KTModal } from '@/metronic/core';

import ReactDOM from 'react-dom';

import { Credential } from '@/types/credential';
import { FocusEvent, MouseEvent, useCallback, useEffect, useState } from 'react';
import { Fa<PERSON>loud, <PERSON>aDock<PERSON>, FaAws, FaA } from 'react-icons/fa6';

import { useLocale, useTranslations } from 'next-intl';
import { z } from 'zod';

interface ModalProps {
  credential: Credential;
  type: string;

  onModalHandler: (type: string, credential: Credential) => void;
}

interface PageProps {
  credentialModal: boolean;
  credentialModalData: ModalProps;
  onCloseModal: () => void;
}

/**
 * @brief Credential Modal 컴포넌트
 * @param param0
 * @returns
 */
export default function CredentialModal({ credentialModal, credentialModalData, onCloseModal }: PageProps) {
  const locale = useLocale();
  const t_i18n = useTranslations('i18nData');

  const [credentialData, setCredentialData] = useState<Credential | null>({
    owner: '',
    category: 'repo',
    cloud: '',
    accKey: '',
    secKey: '',
    region: '',
    registryId: ''
  });
  const [passwordType, setPasswordType] = useState('password');
  const [passwordEyeIcon, setPasswordEyeIcon] = useState('ki-outline ki-eye');

  const [errors, setErrors] = useState<CredentialError>({});

  const [accKeyLabel, setAccKeyLabel] = useState('');
  const [secKeyLabel, setSecKeyLabel] = useState('');

  const [isDisabled, setDisabled] = useState<boolean>(true);

  /**
   * @brief validate 정의
   */
  const schema = z.object({
    accKey: z.string().min(1, t_i18n('user_credential_register_msg_access_key_required')),
    secKey: z.string().min(1, t_i18n('user_credential_register_msg_secret_key_required')),
    // category: z.string().min(1, t_i18n('user_credential_register_msg_access_key_required')),
    cloud: z.string().min(1, t_i18n('user_credential_register_msg_access_key_required')),
    region: z.string().refine((value) => credentialData.cloud === 'aws' && value.trim().length > 0, {
      message: t_i18n('user_credential_register_msg_region_required')
    }),
    registryId: z.string().refine((value) => credentialData.cloud === 'aws' && value.trim().length > 0, {
      message: t_i18n('user_credential_register_msg_registryid_required')
    })
  });

  /**
   * @brief validate
   * @param data
   * @param field
   * @returns
   */
  const validateForm = (data: Credential | any, field?: keyof CredentialType): CredentialError => {
    try {
      schema.parse(data);
      setErrors({});
      return field ? { [field]: null } : {};
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors = error.flatten().fieldErrors;

        field ? setErrors((prevData) => ({ ...prevData, [field]: newErrors[field] })) : setErrors(newErrors);

        return field ? { [field]: newErrors[field] || null } : newErrors;
      }
      return {};
    }
  };

  //credentialModal 변경에 따라서 실행
  useEffect(() => {
    if (credentialModalData.type === 'register') {
      setDisabled(false);
    } else {
      setDisabled(true);
    }
    if (credentialModal) onOpen();
    else onClose();
  }, [credentialModal]);

  //modalData 변경에 따라서 실행
  useEffect(() => {
    if (credentialModalData != null) {
      setCredentialData(credentialModalData.credential);
      changeLabel(credentialModalData.credential.category, credentialModalData.credential.cloud);
    }
  }, []);

  // Category 값 변경 시 credentialData에 값 변경
  const onChangeCategory = useCallback(
    (e: any) => {
      if (credentialData != undefined && credentialData != null) {
        const value = e.target.getAttribute('data-value');
        const cloud = value == 'csp' ? 'aws' : 'docker';
        setCredentialData({ ...credentialData, category: value, cloud: cloud });
        changeLabel(value, cloud);
      }
    },
    [credentialData]
  );

  // Cloud 값 변경 시 credentialData에 값 변경
  const onChangeCloud = useCallback(
    (e: any) => {
      if (credentialData != undefined && credentialData != null) {
        const value = e.target.getAttribute('data-value');
        setCredentialData({ ...credentialData, cloud: value });
        changeLabel(credentialData.category, value);
      }
    },
    [credentialData]
  );

  /**
   * @brief input label 변경
   * @param category
   * @param cloud
   */
  const changeLabel = (category: string, cloud: string) => {
    if (cloud == 'docker') {
      setAccKeyLabel(t_i18n('user_credential_label_user_name'));
      setSecKeyLabel(t_i18n('user_credential_label_user_password_pat'));
    } else if (cloud == 'github') {
      setAccKeyLabel(t_i18n('user_credential_label_user_name'));
      setSecKeyLabel(t_i18n('user_credential_label_user_token'));
    } else if (cloud == 'redhat') {
      setAccKeyLabel(t_i18n('user_credential_label_user_name'));
      setSecKeyLabel(t_i18n('user_credential_label_user_passwordtoken'));
    } else if (cloud == 'huggingface') {
      setAccKeyLabel(t_i18n('user_credential_label_user_name'));
      setSecKeyLabel(t_i18n('user_credential_label_user_token'));
    } else if (cloud == 'aws') {
      setAccKeyLabel(t_i18n('user_credential_label_access_key_id'));
      setSecKeyLabel(t_i18n('user_credential_label_secret_key_id'));
    }
  };

  //key 값 변경 시 credentialData에 값 변경
  const onChangeKey = useCallback(
    (e: any) => {
      const { name, value } = e.target as HTMLInputElement;
      setCredentialData({ ...credentialData, [name]: value });
    },
    [credentialData]
  );

  // Input type password , text에 따라서 값 보이기 및 아이콘 변경
  const onPasswordEye = useCallback(
    (e: any) => {
      if (passwordType == 'password') {
        setPasswordType('text');
        setPasswordEyeIcon('ki-outline ki-eye-slash');
      } else {
        setPasswordType('password');
        setPasswordEyeIcon('ki-outline ki-eye');
      }
    },
    [passwordType]
  );

  /**
   * @brief INPUT Fouce blur 이벤트 발생 시 validate
   * @param e
   */
  const onBlurHandler = (e: FocusEvent<HTMLInputElement>) => {
    const name = e.target.name;
    const value = e.target.value;
    const updateFormData = { ...credentialData, [name]: value };
    validateForm(updateFormData, name as keyof CredentialType);
  };

  //Credential Table 컴포넌트로 값 전달
  const onSubmit = () => {
    const newErorrs = validateForm(credentialData);
    // console.log(newErorrs);
    credentialModalData.onModalHandler(credentialModalData.type, credentialData);
    onClose();
  };

  //Credential Modal 열기
  const onOpen = () => {
    const modalEl = document.querySelector('#credentialModal') as HTMLElement;
    const modalInt = KTModal.getInstance(modalEl);
    modalInt.on('hidden', () => {
      onCloseModal();
    });
    modalInt.show();
  };

  //Credential Modal 닫기
  const onClose = () => {
    if (credentialModalData.type === 'workload') {
      credentialModalData.onModalHandler(credentialModalData.type, null);
    }
    const modalEl = document.querySelector('#credentialModal') as HTMLElement;
    const modalInt = KTModal.getInstance(modalEl);
    modalInt.hide();
  };

  if (!credentialModal) return null;
  return ReactDOM.createPortal(
    <>
      <div className="modal" data-modal="true" id="credentialModal" data-modal-backdrop-static="true">
        <div className="modal-content top-[20%] max-w-[800px]">
          <div className="modal-header">
            <h3 className="modal-title">
              {credentialModalData.type == 'register' || credentialModalData.type == 'workload'
                ? t_i18n('user_credential_register_title')
                : t_i18n('user_credential_modify_title')}
            </h3>
            <button className="btn-icon btn btn-xs btn-light" onClick={() => onClose()}>
              <i className="ki-outline ki-cross"></i>
            </button>
          </div>
          <div className="modal-body">
            <div className="grid grid-cols-1 gap-5 p-4">
              <div className="flex flex-col flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
                <label className="form-label max-w-52">{t_i18n('user_credential_label_repository')}</label>
                <div className="flex flex-col gap-2 md:flex-row">
                  <div className="mt-3 flex gap-2" data-tabs="true">
                    <button
                      // className={'btn btn-secondary ' + (credentialData?.cloud == 'huggingface' ? 'active tab-active:btn-primary' : '')}
                      className={`btn flex items-center gap-2.5 text-nowrap !border !border-dotted ${credentialData?.cloud == 'huggingface' ? '!border-[#8247ff] !bg-[#FAF8FF]' : '!border-[#DBDFE9] !bg-[#FFFFFF]'} hover:!border-[#8247ff] hover:!bg-[#FAF8FF]`}
                      data-tab-toggle="true"
                      data-value="huggingface"
                      onClick={onChangeCloud}
                      disabled={credentialData.cloud == 'huggingface' ? false : isDisabled}
                    >
                      <img src="/da/img/ico_huggingface.svg" className="h-[22px]" alt="" />
                      {t_i18n('repo_huggingface')}
                    </button>
                  </div>
                  <div className="mt-3 flex gap-2" data-tabs="true">
                    <button
                      className={`btn flex items-center gap-2.5 text-nowrap !border !border-dotted ${credentialData?.cloud == 'github' ? '!border-[#8247ff] !bg-[#FAF8FF]' : '!border-[#DBDFE9] !bg-[#FFFFFF]'} hover:!border-[#8247ff] hover:!bg-[#FAF8FF]`}
                      data-tab-toggle="true"
                      data-value="github"
                      onClick={onChangeCloud}
                      disabled={credentialData.cloud == 'github' ? false : isDisabled}
                    >
                      <img src="/da/img/ico_github.svg" className="h-[22px]" alt="" />
                      {t_i18n('repo_github')}
                    </button>
                  </div>
                  <div className="mt-3 flex gap-2" data-tabs="true">
                    <button
                      // className={'btn btn-secondary ' + (credentialData?.cloud == 'redhat' ? 'active tab-active:btn-primary' : '')}
                      className={`btn flex items-center gap-2.5 text-nowrap !border !border-dotted ${credentialData?.cloud == 'redhat' ? '!border-[#8247ff] !bg-[#FAF8FF]' : '!border-[#DBDFE9] !bg-[#FFFFFF]'} hover:!border-[#8247ff] hover:!bg-[#FAF8FF]`}
                      data-tab-toggle="true"
                      data-value="redhat"
                      onClick={onChangeCloud}
                      disabled={credentialData.cloud == 'redhat' ? false : isDisabled}
                    >
                      <img src="/da/img/ico_redhat.svg" className="h-[22px]" alt="" />
                      {t_i18n('repo_redhat')}
                    </button>
                  </div>

                  <div className="mt-3 flex gap-2" data-tabs="true">
                    <button
                      className={`btn flex items-center gap-2.5 text-nowrap !border !border-dotted ${credentialData?.cloud == 'aws' ? '!border-[#8247ff] !bg-[#FAF8FF]' : '!border-[#DBDFE9] !bg-[#FFFFFF]'} hover:!border-[#8247ff] hover:!bg-[#FAF8FF]`}
                      data-tab-toggle="true"
                      data-value="aws"
                      onClick={onChangeCloud}
                      disabled={credentialData.cloud == 'aws' ? false : isDisabled}
                    >
                      <img src="/da/img/ico_aws_ecr40.png" className="h-[22px]" alt="" />
                      {t_i18n('repo_awsecr')}
                    </button>
                  </div>
                  <div className="mt-3 flex gap-2" data-tabs="true">
                    <button
                      // className={'btn btn-secondary ' + (credentialData?.cloud == 'docker' ? 'active tab-active:btn-primary' : '')}
                      className={`btn flex items-center gap-2.5 text-nowrap !border !border-dotted ${credentialData?.cloud == 'docker' ? '!border-[#8247ff] !bg-[#FAF8FF]' : '!border-[#DBDFE9] !bg-[#FFFFFF]'} hover:!border-[#8247ff] hover:!bg-[#FAF8FF]`}
                      data-tab-toggle="true"
                      data-value="docker"
                      onClick={onChangeCloud}
                      disabled={credentialData.cloud == 'docker' ? false : isDisabled}
                    >
                      <img src="/da/img/ico_dockerhub_blue.svg" className="h-[22px]" alt="" />
                      {t_i18n('repo_dockerhub')}
                    </button>
                  </div>
                </div>
              </div>
              {credentialData.cloud !== '' && (
                <>
                  <div className="flex flex-col flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
                    <label className="form-label max-w-52">{accKeyLabel}</label>
                    <div className="flex w-full flex-col gap-2">
                      <input
                        className="input"
                        name="accKey"
                        // placeholder={t_i18n('user_credential_register_msg_access_key_required')}
                        type="text"
                        value={credentialData?.accKey}
                        onChange={onChangeKey}
                        onBlur={onBlurHandler}
                      />

                      {errors.accKey && (
                        <>
                          <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">{errors.accKey}</span>
                        </>
                      )}
                    </div>
                  </div>
                  <div className="flex flex-col flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
                    <label className="form-label max-w-56">{secKeyLabel}</label>
                    <div className="flex w-full flex-col gap-2">
                      <div className="input" data-toggle-password="true" data-toggle-password-permanent="true">
                        <input
                          name="secKey"
                          // placeholder={t_i18n('user_credential_register_msg_secret_key_required')}
                          type={passwordType}
                          value={credentialData?.secKey}
                          onChange={onChangeKey}
                          onBlur={onBlurHandler}
                        />
                        <div className="btn-icon btn" onClick={onPasswordEye}>
                          <i className={passwordEyeIcon}></i>
                        </div>
                      </div>

                      {errors.secKey && (
                        <>
                          <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">{errors.secKey}</span>
                        </>
                      )}
                    </div>
                  </div>

                  {credentialData?.cloud === 'aws' && (
                    <>
                      <div className="flex flex-col flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
                        <label className="form-label max-w-52">{t_i18n('user_credential_label_region')}</label>
                        <div className="flex w-full flex-col gap-2">
                          <div className="input">
                            <input name="region" type="text" value={credentialData?.region} onChange={onChangeKey} onBlur={onBlurHandler} />
                          </div>

                          {errors.region && (
                            <>
                              <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">{errors.region}</span>
                            </>
                          )}
                        </div>
                      </div>

                      <div className="flex flex-col flex-wrap items-baseline gap-2.5 lg:flex-nowrap">
                        <label className="form-label max-w-56">{t_i18n('user_credential_label_registryid')}</label>
                        <div className="flex w-full flex-col gap-2">
                          <div className="input">
                            <input
                              name="registryId"
                              type="text"
                              value={credentialData?.registryId}
                              onChange={onChangeKey}
                              onBlur={onBlurHandler}
                            />
                          </div>

                          {errors.registryId && (
                            <>
                              <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">{errors.registryId}</span>
                            </>
                          )}
                        </div>
                      </div>
                    </>
                  )}
                </>
              )}
              <div className="modal-footer justify-end">
                <div className="flex gap-4">
                  <button className="btn btn-light" onClick={(e: MouseEvent<HTMLButtonElement>) => onClose()}>
                    {t_i18n('but_cancel')}
                  </button>
                  <button
                    className="btn btn-primary"
                    disabled={credentialData.cloud === ''}
                    onClick={(e: MouseEvent<HTMLButtonElement>) => {
                      e.preventDefault();
                      onSubmit();
                    }}
                  >
                    {credentialModalData.type == 'register' || credentialModalData.type == 'workload'
                      ? t_i18n('but_register')
                      : t_i18n('but_modify')}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>,
    document.getElementById('global_modal')
  );
}
