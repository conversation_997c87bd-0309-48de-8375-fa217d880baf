'use client';

import { deleteUserAlar<PERSON>, getAlarmDetail, getUserAlarmCount, getUserAlarmList } from '@/action/user-alert-action';
import { UserAlarm, UserAlarmResponse } from '@/types/user-alarm';
import { convertDateTime, dateTime, dateTime2 } from '@/utils';
import { useTranslations } from 'next-intl';
import React, { useEffect, useState } from 'react';
import { Paging } from '@/types/paging';
import AlertModal from '../modal/alert-modal';
import Head from 'next/head';
import '@/styles/quill.snow.css';
import Link from 'next/link';
import { TbTrash } from 'react-icons/tb';
/**
 * @brief 사용자 헤더에 알림 내역을 보여주는 컴포넌트
 * @returns
 */
export default function UserAlarmComp() {
  const t_i18n = useTranslations('i18nData');

  const [alertModal, setAlertModal] = useState<boolean>(false);
  const [alertModalData, setAlertModalData] = useState<any>();

  const [pagingData, setPageData] = useState<Paging | null>(null);

  const [isAlertOpen, setAlertOpen] = useState(false);
  const [selectedDetail, setSelectedDetail] = useState<UserAlarm | null>();

  const [startNum, setStartNum] = useState<number>(0);
  const scaleNum: number = 10;

  const [noReadAlarmCount, setNoReadAlarmCount] = useState<number>(0);
  const [alarmList, setAlarmList] = useState<UserAlarm[]>([]);

  const closeAlert = () => {
    setSelectedDetail(null);
    setAlertOpen(false);
    getAlarmCount();
  };

  useEffect(() => {
    getAlarmCount();
  }, []);

  useEffect(() => {
    if (isAlertOpen) getAlarmList();
  }, [startNum]);

  useEffect(() => {
    if (isAlertOpen) {
      getAlarmList();
    } else {
      setSelectedDetail(null);
    }
  }, [isAlertOpen]);

  /**
   * @brief 알림 카운트
   */
  const getAlarmCount = async () => {
    const response: UserAlarmResponse = await getUserAlarmCount();

    if (response.status === 200) {
      setNoReadAlarmCount(response.count.noReadCount ?? 0);
    } else {
      setNoReadAlarmCount(0);
    }
  };

  /**
   * @brief 알림 목록 조회
   */
  const getAlarmList = async () => {
    const params = { startNum: startNum, scaleNum: scaleNum };

    const response: UserAlarmResponse = await getUserAlarmList(params);

    if (response.status === 200) {
      setAlarmList(response.alarms);
      setPageData(response.paging ?? null);
    } else {
      setAlarmList([]);
      setPageData(null);
    }
  };

  /**
   * @brief 페이지 이동
   * @param val
   */
  const pageMove = (val: number) => {
    setStartNum(val);
  };

  /**
   * @brief 사용자 알림 삭제
   */
  const deleteAlarms = async (ser: number) => {
    const response: UserAlarmResponse = await deleteUserAlarms([ser]);

    if (response.status === 200) {
      setStartNum(0);
      getAlarmList();
    } else {
      setAlertModalData({
        btnColor: 'btn-primary',
        title: t_i18n('dialog_title_waring'),
        content: t_i18n('user_notifications_delete_error_msg'),
        okBtn: t_i18n('but_ok')
      });
      setAlertModal(true);
    }
  };

  /**
   * @brief 알림 상세보기
   * @returns
   */
  const renderAlertDetail = async (ser: number) => {
    const response: UserAlarmResponse = await getAlarmDetail(ser);

    if (response.status === 200) {
      setSelectedDetail(response.alarm);
    } else {
      setSelectedDetail(null);
    }
  };

  return (
    <>
      <div className="relative min-w-6">
        <div className="inline-flex cursor-pointer -space-x-2" onClick={() => setAlertOpen(!isAlertOpen)}>
          <img src="/da/img/ico-bell.svg" alt="알림 아이콘" />

          {noReadAlarmCount > 0 && (
            <>
              <span className="relative inline-flex size-4 shrink-0 items-center justify-center rounded-full bg-danger text-xs font-semibold leading-none text-danger-inverse ring-2 ring-danger-light">
                {noReadAlarmCount > 9 ? '+9' : noReadAlarmCount}
              </span>
            </>
          )}
        </div>
        {isAlertOpen && (
          <div className="absolute right-0 top-full z-50 mt-2 w-[480px] rounded border bg-white shadow-lg max-sm:!fixed max-sm:top-0 max-sm:!mt-0 max-sm:w-full">
            <div className="flex items-center justify-between p-4">
              <p className="font-bold">
                {selectedDetail && (
                  <button className="mr-3" onClick={() => setSelectedDetail(null)}>
                    ≪
                  </button>
                )}
                {t_i18n('user_notifications_title')}
              </p>
              <button onClick={closeAlert}>✕</button>
            </div>
            <hr />
            <div className="h-[500px] overflow-y-auto overflow-x-hidden">
              {selectedDetail ? (
                <div className="p-4">
                  <div className="mb-6">
                    <h2 className="font-semibold">{selectedDetail.title}</h2>

                    <p className="text-xs text-gray-500">
                      {/* {selectedDetail.sendDate} */}
                      {convertDateTime(new Date(selectedDetail.sendDate.replace(' ', 'T') + 'Z'))}
                    </p>
                  </div>
                  <div className="ql-container ql-snow" style={{ border: '0px' }}>
                    <div className="ql-editor mb-4" dangerouslySetInnerHTML={{ __html: selectedDetail.content }}></div>
                  </div>

                  {selectedDetail.link && (
                    <div>
                      링크 : <a href={`${selectedDetail.link}`} target="_blank" className="text-blue-400">{`${selectedDetail.link}`}</a>
                    </div>
                  )}
                </div>
              ) : (
                <div>
                  {alarmList.length > 0 &&
                    alarmList.map((item, index) => (
                      <div key={`alarm_key_${index}`} className="flex flex-col gap-4 border-b p-4">
                        <div className="flex items-center justify-between gap-2 font-semibold">
                          <p>
                            {item.link !== null && (
                              <a href={item.link} target="_blank" className="hover:text-blue-400" rel="noopener noreferrer">
                                {item.title}
                              </a>
                            )}
                            {item.link === null && `${item.title}`}
                          </p>
                          <button type="button" className="menu-link btn grow-0 hover:text-red-500" onClick={() => deleteAlarms(item.ser)}>
                            <span className="menu-icon">
                              <TbTrash size="1.1em" />
                            </span>
                          </button>
                        </div>
                        {item.contentDetailYn !== 'Y' && (
                          <div className="" dangerouslySetInnerHTML={{ __html: item.content.replaceAll('\n', '<br/>') }}></div>
                        )}
                        <div className="flex items-end justify-between">
                          <div className="text-xs text-gray-500">
                            {convertDateTime(new Date(item.sendDate.replace(' ', 'T') + 'Z'))}
                            {/* {new Date(item.sendDate.replace(' ', 'T') + 'Z').toString()} */}
                          </div>

                          {item.contentDetailYn === 'Y' && (
                            <button onClick={() => renderAlertDetail(item.ser)} className="w-fit rounded-md bg-purple-600 px-4 py-1">
                              <p className="text-xs text-white">{t_i18n('user_notifications_view_details')}</p>
                            </button>
                          )}
                        </div>
                      </div>
                    ))}
                  {pagingData && (
                    <div className="flex justify-center py-2">
                      <div className="order-1 flex items-center gap-4 md:order-2">
                        <div className="pagination" data-datatable-pagination="true">
                          <div className="pagination">
                            <button
                              className="btn disabled:cursor-not-allowed"
                              disabled={pagingData.isPrevPage !== 'true'}
                              onClick={() => pageMove(0)}
                            >
                              <i className="ki-outline ki-double-left"></i>
                            </button>
                            <button
                              className="btn disabled:cursor-not-allowed"
                              disabled={pagingData.isPrevPage !== 'true'}
                              onClick={() => pageMove(pagingData.prevNum)}
                            >
                              <i className="ki-outline ki-left"></i>
                            </button>
                            {pagingData.pages.map((item, index) => (
                              <button
                                key={`paginate_${index}`}
                                className={`${item.pageNum == pagingData.currentPageNum ? 'btn-primary' : ''} btn btn-outline`}
                                onClick={() => pageMove(item.startNum)}
                              >
                                {item.pageNum}
                              </button>
                            ))}
                            <button
                              className="btn disabled:cursor-not-allowed"
                              disabled={pagingData.isNextPage !== 'true'}
                              onClick={() => pageMove(pagingData.nextNum)}
                            >
                              <i className="ki-outline ki-right"></i>
                            </button>
                            <button
                              className="btn disabled:cursor-not-allowed"
                              disabled={pagingData.isNextPage !== 'true'}
                              onClick={() => pageMove(pagingData.lastNum)}
                            >
                              <i className="ki-outline ki-double-right"></i>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {selectedDetail ? (
              <div className="border-t py-2 pr-2 text-right">
                <button className="rounded-md border border-gray-300 bg-white px-4 py-1" onClick={() => setSelectedDetail(null)}>
                  <p className="text-xs text-black">{t_i18n('user_notifications_view_list')}</p>
                </button>
              </div>
            ) : (
              <div className="py-4 text-center">
                <p className="text-xs text-gray-500">{t_i18n('user_notification_history_info')}</p>
              </div>
            )}
          </div>
        )}
      </div>
      {alertModal && (
        <AlertModal
          alertModal={alertModal}
          alertModalData={alertModalData}
          onCloseModal={() => {
            setAlertModal(false);
          }}
        ></AlertModal>
      )}
    </>
  );
}
