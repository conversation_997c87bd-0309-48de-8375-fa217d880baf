import { ChangeEvent, useState } from 'react';
import { FaPlus } from 'react-icons/fa';
import { ToastContainer, toast, Bounce } from 'react-toastify';

interface InputDragdrop {
  onChangeFile: (file: File | null) => void;
  description?: string;
  validExtensions?: string[];
}

export default function InputDragDrop({ onChangeFile, description = '파일 첨부', validExtensions = ['*'] }: InputDragdrop) {
  // 사용자가 파일을 드래드 중임을 상태로 관리 UI 변경을 위해 사용
  const [dragOver, setDragOver] = useState<boolean>(false);

  // 드래그 중인 요소가 목표 지점 진입할때
  const handleDragEnter = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOver(true);
  };

  // 드래그 중인 요소가 목표 지점을 벗어날때
  const handleDragLeave = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOver(false);
  };

  // 드래그 중인 요소가 목표 지점에 위치할때
  const handleDragOver = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  // 드래그 중인 요소가 목표 지점에서 드롭될때
  const handleDrop = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOver(false);

    // 드래그되는 데이터 정보와 메서드를 제공하는 dataTransfer 객체 사용
    if (e.dataTransfer) {
      const file = e.dataTransfer.files[0];
      validFile(file);
    }
  };

  // Drag & Drop이 아닌 클릭 이벤트로 업로드되는 기능도 추가
  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files ? e.target.files[0] : null;
    validFile(file);
    // input 요소의 값 초기화
    e.target.value = '';
  };

  const validFile = (file: File) => {
    if (file && isValidExtension(file)) {
      onChangeFile(file);
    } else {
      console.log(file);
      toast.warn(`지원하지 않는 파일 형식입니다. (${validExtensions.join(', ')})로 등록해주세요.`);
      onChangeFile(null);
    }
  };

  const isValidExtension = (file: File) => {
    const fileName = file.name;
    const fileNameSplit = fileName.split('.');
    const fileExtension = fileNameSplit[fileNameSplit.length - 1];
    return validExtensions.includes(fileExtension);
  };
  return (
    <>
      <div className="flex w-full flex-col items-center justify-center">
        <div className="w-full">
          <label
            className={`h-32 w-full flex-col gap-3 border ${dragOver ? 'border-blue-500 bg-blue-100 font-semibold text-blue-500' : 'border-gray-300'} flex cursor-pointer items-center justify-center rounded-md`}
            htmlFor="fileUpload"
            // Label에 드래그 앤 드랍 이벤트 추가
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          >
            {description}
            <div className="pointer-events-none h-9 w-9">
              <FaPlus className="w-6" />
            </div>
          </label>
          <input id="fileUpload" type="file" className="hidden" onChange={handleChange}></input>
        </div>
      </div>
      <ToastContainer
        position="bottom-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick={false}
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
        transition={Bounce}
      />
    </>
  );
}
