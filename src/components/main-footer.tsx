'use client';

import { useLocale, useTranslations } from 'next-intl';
import Link from 'next/link';

export default function Footer() {
  const locale = useLocale();
  const t_i18n = useTranslations('i18nData');
  return (
    <footer className="footer">
      <div className="border-t border-[#F1F1F4]"></div>
      <div className="container-fixed px-8 py-8">
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <div className="flex flex-col gap-7">
            <div className="">
              <img src={'/da/img/logo-gcube-light.svg'} alt="data-alliance logo" />
            </div>
            <div className="flex flex-col gap-0.5">
              <span className="text-[14px] font-medium leading-[22px] text-gray-600">
                {t_i18n('landing_footer_da')} | {t_i18n('landing_footer_ceo')}
              </span>
              <span className="gap-1 text-[14px] font-medium leading-[22px] text-gray-600">
                {t_i18n('landing_footer_brn')} ㅣ {t_i18n('landing_footer_mon')}
              </span>
              <span className="text-gray-600">{t_i18n('landing_footer_address')}</span>
              <span className="text-gray-600">+82-2-6354-3282</span>
            </div>
            <div className="flex flex-col gap-0.5">
              <Link href={`/${locale}/contactus`}>
                <span className="text-[14px] font-medium leading-[14px]">Contact Us</span>
              </Link>
            </div>
            <div className="flex gap-0.5">
              <Link href={`/${locale}/terms`}>
                <span className="text-[14px] font-medium leading-[14px]">{t_i18n('auth_terms')}</span>
              </Link>
              |
              <Link href={`/${locale}/terms`}>
                <span className="text-[14px] font-medium leading-[14px]">{t_i18n('auth_privacy')} </span>
              </Link>
            </div>
            <div className="flex gap-0.5">
              <span className="text-[14px] font-medium leading-[22px] text-gray-600">
                COPYRIGHT(c) 2024 by DATAALLIANCE. ALL RIGHTS RESERVED.
              </span>
            </div>
          </div>

          <div className="flex flex-col gap-7">
            <div className="flex justify-center gap-4 lg:!justify-end">
              <Link href="https://x.com/gcube286954" target="_blank">
                <img src="/da/img/social_x.png" alt="" />
              </Link>
              <Link href="https://discord.com/channels/1275281767045140511" target="_blank">
                <img src="/da/img/social_discord.png" alt="" />
              </Link>
              <Link href="https://www.youtube.com/@gucube.everyone" target="_blank">
                <img src="/da/img/social_youTube.png" alt="" />
              </Link>
              {/* <img src="/da/img/social_instagram.png" alt="" />
                <img src="/da/img/social_linkedIn.png" alt="" /> */}
            </div>
            <div className="flex justify-center gap-10 lg:!justify-end">
              <div className="">
                <Link locale={locale} href={`/${locale}/pricetable`}>
                  <span className="text-[14px] font-medium leading-[22px] text-gray-900">Pricing</span>
                </Link>
              </div>
              <div className="">
                <Link locale={locale} href={`/${locale}/docs`}>
                  <span className="text-[14px] font-medium leading-[22px] text-gray-900">Docs</span>
                </Link>
              </div>
              <div className="flex flex-col">
                <Link locale={locale} href={`/${locale}/faq`}>
                  <span className="text-[14px] font-medium leading-[22px] text-gray-900">Support</span>
                </Link>
                <Link locale={locale} href={`/${locale}/faq`}>
                  <span className="text-[14px] font-medium leading-[22px] text-gray-700">FAQ</span>
                </Link>
                <Link locale={locale} href={`/${locale}/contactus`}>
                  <span className="text-[14px] font-medium leading-[22px] text-gray-700">Contact Us</span>
                </Link>
              </div>
              <div className="">
                <Link locale={locale} href={`https://blog.naver.com/gcube-official`} target="_blank">
                  <span className="text-[14px] font-medium leading-[22px] text-gray-900">Blog</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
