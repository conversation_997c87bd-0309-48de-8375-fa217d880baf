'use client';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';

/**
 * @brief 페이지 400 에러 페이지
 * @returns
 */
interface PageProps {
  isRouter?: boolean;
  routerUrl?: string;
}
export default function Page400({ isRouter = false, routerUrl }: PageProps) {
  const t_i18n = useTranslations('i18nData');
  const router = useRouter();
  return (
    <>
      <div className="container-fixed pb-[60px] pt-[100px]">
        <div className="flex h-full grow items-center justify-center">
          <div className="flex flex-col items-center">
            <div className="mb-16">
              <img alt="image" className="max-h-[160px] dark:hidden" src="/media/illustrations/19.svg" />
            </div>
            <span className="badge badge-outline badge-primary mb-3">400 Error</span>
            <h3 className="mb-2 text-center text-2.5xl font-semibold text-gray-900">{t_i18n('http_bad_request')}</h3>
            <div
              className="mb-10 text-center text-md font-medium text-gray-600"
              dangerouslySetInnerHTML={{ __html: t_i18n('http_bad_request_msg') }}
            ></div>

            {isRouter && (
              <a
                className="btn btn-primary flex justify-center"
                onClick={() => {
                  window.location.href = routerUrl;
                }}
              >
                Back to Page
              </a>
            )}
            {isRouter === false && (
              <a
                className="btn btn-primary flex justify-center"
                onClick={() => {
                  router.back();
                }}
              >
                Back to Page
              </a>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
