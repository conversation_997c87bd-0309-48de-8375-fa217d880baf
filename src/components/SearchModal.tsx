export default function SearchModal() {
  return (
    <div className="modal" data-modal="true" id="search_modal">
      <div className="modal-content top-[15%] max-w-[600px]">
        <div className="modal-header px-5 py-4">
          <i className="ki-filled ki-magnifier text-xl text-gray-700"></i>
          <input
            className="input ml-2.5 border-none bg-transparent px-0 shadow-none"
            name="query"
            placeholder="Tap to start search"
            type="text"
            defaultValue=""
          />
          <button className="btn-icon btn btn-sm btn-clear btn-light shrink-0" data-modal-dismiss="true">
            <i className="ki-filled ki-cross"></i>
          </button>
        </div>
        <div className="modal-body p-0 pb-5">
          <div className="tabs mb-2.5 justify-between px-5" data-tabs="true">
            <div className="flex items-center gap-5">
              <button className="active tab py-5" data-tab-toggle="#search_modal_mixed">
                Mixed
              </button>
              <button className="tab py-5" data-tab-toggle="#search_modal_settings">
                Settings
              </button>
              <button className="tab py-5" data-tab-toggle="#search_modal_integrations">
                Integrations
              </button>
              <button className="tab py-5" data-tab-toggle="#search_modal_users">
                Users
              </button>
              <button className="tab py-5" data-tab-toggle="#search_modal_docs">
                Docs
              </button>
              <button className="tab py-5" data-tab-toggle="#search_modal_empty">
                Empty
              </button>
              <button className="tab py-5" data-tab-toggle="#search_modal_no-results">
                No Results
              </button>
            </div>
            <div className="menu -mt-px" data-menu="true">
              <div
                className="menu-item"
                data-menu-item-offset="0, 10px"
                data-menu-item-placement="bottom-end"
                data-menu-item-toggle="dropdown"
                data-menu-item-trigger="click|lg:hover"
              >
                <button className="menu-toggle btn-icon btn btn-sm btn-clear btn-light">
                  <i className="ki-filled ki-setting-2"></i>
                </button>
                <div className="menu-dropdown menu-default w-full max-w-[175px]" data-menu-dismiss="true">
                  <div className="menu-item">
                    <a className="menu-link" href="#">
                      <span className="menu-icon">
                        <i className="ki-filled ki-document"></i>
                      </span>
                      <span className="menu-title">View</span>
                    </a>
                  </div>
                  <div
                    className="menu-item"
                    data-menu-item-offset="-15px, 0"
                    data-menu-item-placement="right-start"
                    data-menu-item-toggle="dropdown"
                    data-menu-item-trigger="click|lg:hover"
                  >
                    <div className="menu-link">
                      <span className="menu-icon">
                        <i className="ki-filled ki-notification-status"></i>
                      </span>
                      <span className="menu-title">Export</span>
                      <span className="menu-arrow">
                        <i className="ki-filled ki-right text-3xs"></i>
                      </span>
                    </div>
                    <div className="menu-dropdown menu-default w-full max-w-[175px]">
                      <div className="menu-item">
                        <a className="menu-link" href="html/demo1/account/home/<USER>">
                          <span className="menu-icon">
                            <i className="ki-filled ki-sms"></i>
                          </span>
                          <span className="menu-title">Email</span>
                        </a>
                      </div>
                      <div className="menu-item">
                        <a className="menu-link" href="html/demo1/account/home/<USER>">
                          <span className="menu-icon">
                            <i className="ki-filled ki-message-notify"></i>
                          </span>
                          <span className="menu-title">SMS</span>
                        </a>
                      </div>
                      <div className="menu-item">
                        <a className="menu-link" href="html/demo1/account/home/<USER>">
                          <span className="menu-icon">
                            <i className="ki-filled ki-notification-status"></i>
                          </span>
                          <span className="menu-title">Push</span>
                        </a>
                      </div>
                    </div>
                  </div>
                  <div className="menu-item">
                    <a className="menu-link" href="#">
                      <span className="menu-icon">
                        <i className="ki-filled ki-pencil"></i>
                      </span>
                      <span className="menu-title">Edit</span>
                    </a>
                  </div>
                  <div className="menu-item">
                    <a className="menu-link" href="#">
                      <span className="menu-icon">
                        <i className="ki-filled ki-trash"></i>
                      </span>
                      <span className="menu-title">Delete</span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="scrollable-y-auto" data-scrollable="true" data-scrollable-max-height="auto" data-scrollable-offset="300px">
            <div className="" id="search_modal_mixed">
              <div className="flex flex-col gap-2.5">
                <div>
                  <div className="pb-1.5 pl-5 pt-2.5 text-xs font-medium text-gray-600">Settings</div>
                  <div className="menu menu-default flex-col p-0">
                    <div className="menu-item">
                      <a className="menu-link" href="#">
                        <span className="menu-icon">
                          <i className="ki-filled ki-badge"></i>
                        </span>
                        <span className="menu-title">Public Profile</span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a className="menu-link" href="#">
                        <span className="menu-icon">
                          <i className="ki-filled ki-setting-2"></i>
                        </span>
                        <span className="menu-title">My Account</span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a className="menu-link" href="#">
                        <span className="menu-icon">
                          <i className="ki-filled ki-message-programming"></i>
                        </span>
                        <span className="menu-title">Devs Forum</span>
                      </a>
                    </div>
                  </div>
                </div>
                <div className="border-b border-b-gray-200"></div>
                <div>
                  <div className="pb-1.5 pl-5 pt-2.5 text-xs font-medium text-gray-600">Integrations</div>
                  <div className="menu menu-default flex-col p-0">
                    <div className="menu-item">
                      <div className="jistify-between menu-link flex items-center gap-2">
                        <div className="flex grow items-center gap-2">
                          <div className="flex size-10 shrink-0 items-center justify-center rounded-full border border-gray-200 bg-gray-100">
                            <img alt="" className="size-6 shrink-0" src="/media/brand-logos/jira.svg" />
                          </div>
                          <div className="flex flex-col gap-0.5">
                            <a className="text-2sm font-semibold text-gray-900 hover:text-primary-active" href="#">
                              Jira
                            </a>
                            <span className="text-2xs font-medium text-gray-600">Project management</span>
                          </div>
                        </div>
                        <div className="flex shrink-0 justify-end">
                          <div className="flex -space-x-2">
                            <div className="flex">
                              <img
                                className="relative size-6 shrink-0 rounded-full ring-1 ring-light-light hover:z-5"
                                src="/media/avatars/300-4.png"
                                alt=""
                              />
                            </div>
                            <div className="flex">
                              <img
                                className="relative size-6 shrink-0 rounded-full ring-1 ring-light-light hover:z-5"
                                src="/media/avatars/300-1.png"
                                alt=""
                              />
                            </div>
                            <div className="flex">
                              <img
                                className="relative size-6 shrink-0 rounded-full ring-1 ring-light-light hover:z-5"
                                src="/media/avatars/300-2.png"
                                alt=""
                              />
                            </div>
                            <div className="flex">
                              <span className="relative inline-flex size-6 shrink-0 items-center justify-center rounded-full bg-success text-3xs font-semibold leading-none text-success-inverse ring-1 ring-success-light hover:z-5">
                                +3
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="menu-item">
                      <div className="jistify-between menu-link flex items-center gap-2">
                        <div className="flex grow items-center gap-2">
                          <div className="flex size-10 shrink-0 items-center justify-center rounded-full border border-gray-200 bg-gray-100">
                            <img alt="" className="size-6 shrink-0" src="/media/brand-logos/inferno.svg" />
                          </div>
                          <div className="flex flex-col gap-0.5">
                            <a className="text-2sm font-semibold text-gray-900 hover:text-primary-active" href="#">
                              Inferno
                            </a>
                            <span className="text-2xs font-medium text-gray-600">Real-time photo sharing app</span>
                          </div>
                        </div>
                        <div className="flex shrink-0 justify-end">
                          <div className="flex -space-x-2">
                            <div className="flex">
                              <img
                                className="relative size-6 shrink-0 rounded-full ring-1 ring-light-light hover:z-5"
                                src="/media/avatars/300-14.png"
                                alt=""
                              />
                            </div>
                            <div className="flex">
                              <img
                                className="relative size-6 shrink-0 rounded-full ring-1 ring-light-light hover:z-5"
                                src="/media/avatars/300-12.png"
                                alt=""
                              />
                            </div>
                            <div className="flex">
                              <img
                                className="relative size-6 shrink-0 rounded-full ring-1 ring-light-light hover:z-5"
                                src="/media/avatars/300-9.png"
                                alt=""
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="border-b border-b-gray-200"></div>
                <div>
                  <div className="pb-1.5 pl-5 pt-2.5 text-xs font-medium text-gray-600">Users</div>
                  <div className="menu menu-default flex-col p-0">
                    <div className="grid gap-1">
                      <div className="menu-item">
                        <div className="menu-link flex justify-between gap-2">
                          <div className="flex items-center gap-2.5">
                            <img alt="" className="size-9 shrink-0 rounded-full" src="/media/avatars/300-3.png" />
                            <div className="flex flex-col">
                              <a className="mb-px text-sm font-semibold text-gray-900 hover:text-primary-active" href="#">
                                Tyler Hero
                              </a>
                              <span className="text-2sm font-normal text-gray-500"><EMAIL> connections</span>
                            </div>
                          </div>
                          <div className="flex items-center gap-2.5">
                            <div className="badge badge-pill badge-outline badge-success gap-1.5">
                              <span className="badge badge-dot badge-success size-1.5"></span>
                              In Office
                            </div>
                            <button className="btn-icon btn btn-sm btn-clear btn-light">
                              <i className="ki-filled ki-dots-vertical"></i>
                            </button>
                          </div>
                        </div>
                      </div>
                      <div className="menu-item">
                        <div className="menu-link flex justify-between gap-2">
                          <div className="flex items-center gap-2.5">
                            <img alt="" className="size-9 shrink-0 rounded-full" src="/media/avatars/300-1.png" />
                            <div className="flex flex-col">
                              <a className="mb-px text-sm font-semibold text-gray-900 hover:text-primary-active" href="#">
                                Esther Howard
                              </a>
                              <span className="text-2sm font-normal text-gray-500"><EMAIL> connections</span>
                            </div>
                          </div>
                          <div className="flex items-center gap-2.5">
                            <div className="badge badge-pill badge-outline badge-danger gap-1.5">
                              <span className="badge badge-dot badge-danger size-1.5"></span>
                              On Leave
                            </div>
                            <button className="btn-icon btn btn-sm btn-clear btn-light">
                              <i className="ki-filled ki-dots-vertical"></i>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="hidden" id="search_modal_settings">
              <div className="menu menu-default flex-col p-0">
                <div className="pb-1.5 pl-5 pt-2.5 text-xs font-medium text-gray-600">Shortcuts</div>
                <div className="menu-item">
                  <a className="menu-link" href="#">
                    <span className="menu-icon">
                      <i className="ki-filled ki-home-2"></i>
                    </span>
                    <span className="menu-title">Go to Dashboard</span>
                  </a>
                </div>
                <div className="menu-item">
                  <a className="menu-link" href="#">
                    <span className="menu-icon">
                      <i className="ki-filled ki-badge"></i>
                    </span>
                    <span className="menu-title">Public Profile</span>
                  </a>
                </div>
                <div className="menu-item">
                  <a className="menu-link" href="#">
                    <span className="menu-icon">
                      <i className="ki-filled ki-profile-circle"></i>
                    </span>
                    <span className="menu-title">My Profile</span>
                  </a>
                </div>
                <div className="menu-item">
                  <a className="menu-link" href="#">
                    <span className="menu-icon">
                      <i className="ki-filled ki-setting-2"></i>
                    </span>
                    <span className="menu-title">My Account</span>
                  </a>
                </div>
                <div className="menu-item">
                  <a className="menu-link" href="#">
                    <span className="menu-icon">
                      <i className="ki-filled ki-message-programming"></i>
                    </span>
                    <span className="menu-title">Devs Forum</span>
                  </a>
                </div>
                <div className="pb-1.5 pl-5 pt-2.5 text-xs font-medium text-gray-600">Actions</div>
                <div className="menu-item">
                  <a className="menu-link" href="#">
                    <span className="menu-icon">
                      <i className="ki-filled ki-user"></i>
                    </span>
                    <span className="menu-title">Create User</span>
                  </a>
                </div>
                <div className="menu-item">
                  <a className="menu-link" href="#">
                    <span className="menu-icon">
                      <i className="ki-filled ki-user-edit"></i>
                    </span>
                    <span className="menu-title">Create Team</span>
                  </a>
                </div>
                <div className="menu-item">
                  <a className="menu-link" href="#">
                    <span className="menu-icon">
                      <i className="ki-filled ki-subtitle"></i>
                    </span>
                    <span className="menu-title">Change Plan</span>
                  </a>
                </div>
                <div className="menu-item">
                  <a className="menu-link" href="#">
                    <span className="menu-icon">
                      <i className="ki-filled ki-setting"></i>
                    </span>
                    <span className="menu-title">Setup Branding</span>
                  </a>
                </div>
              </div>
            </div>
            <div className="hidden" id="search_modal_integrations">
              <div className="menu menu-default flex-col p-0">
                <div className="menu-item">
                  <div className="jistify-between menu-link flex items-center gap-2">
                    <div className="flex grow items-center gap-2">
                      <div className="flex size-10 shrink-0 items-center justify-center rounded-full border border-gray-200 bg-gray-100">
                        <img alt="" className="size-6 shrink-0" src="/media/brand-logos/jira.svg" />
                      </div>
                      <div className="flex flex-col gap-0.5">
                        <a className="text-2sm font-semibold text-gray-900 hover:text-primary-active" href="#">
                          Jira
                        </a>
                        <span className="text-2xs font-medium text-gray-600">Project management</span>
                      </div>
                    </div>
                    <div className="flex shrink-0 justify-end">
                      <div className="flex -space-x-2">
                        <div className="flex">
                          <img
                            className="relative size-6 shrink-0 rounded-full ring-1 ring-light-light hover:z-5"
                            src="/media/avatars/300-4.png"
                            alt=""
                          />
                        </div>
                        <div className="flex">
                          <img
                            className="relative size-6 shrink-0 rounded-full ring-1 ring-light-light hover:z-5"
                            src="/media/avatars/300-1.png"
                            alt=""
                          />
                        </div>
                        <div className="flex">
                          <img
                            className="relative size-6 shrink-0 rounded-full ring-1 ring-light-light hover:z-5"
                            src="/media/avatars/300-2.png"
                            alt=""
                          />
                        </div>
                        <div className="flex">
                          <span className="relative inline-flex size-6 shrink-0 items-center justify-center rounded-full bg-success text-3xs font-semibold leading-none text-success-inverse ring-1 ring-success-light hover:z-5">
                            +3
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="menu-item">
                  <div className="jistify-between menu-link flex items-center gap-2">
                    <div className="flex grow items-center gap-2">
                      <div className="flex size-10 shrink-0 items-center justify-center rounded-full border border-gray-200 bg-gray-100">
                        <img alt="" className="size-6 shrink-0" src="/media/brand-logos/inferno.svg" />
                      </div>
                      <div className="flex flex-col gap-0.5">
                        <a className="text-2sm font-semibold text-gray-900 hover:text-primary-active" href="#">
                          Inferno
                        </a>
                        <span className="text-2xs font-medium text-gray-600">Real-time photo sharing app</span>
                      </div>
                    </div>
                    <div className="flex shrink-0 justify-end">
                      <div className="flex -space-x-2">
                        <div className="flex">
                          <img
                            className="relative size-6 shrink-0 rounded-full ring-1 ring-light-light hover:z-5"
                            src="/media/avatars/300-14.png"
                            alt=""
                          />
                        </div>
                        <div className="flex">
                          <img
                            className="relative size-6 shrink-0 rounded-full ring-1 ring-light-light hover:z-5"
                            src="/media/avatars/300-12.png"
                            alt=""
                          />
                        </div>
                        <div className="flex">
                          <img
                            className="relative size-6 shrink-0 rounded-full ring-1 ring-light-light hover:z-5"
                            src="/media/avatars/300-9.png"
                            alt=""
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="menu-item">
                  <div className="jistify-between menu-link flex items-center gap-2">
                    <div className="flex grow items-center gap-2">
                      <div className="flex size-10 shrink-0 items-center justify-center rounded-full border border-gray-200 bg-gray-100">
                        <img alt="" className="size-6 shrink-0" src="/media/brand-logos/evernote.svg" />
                      </div>
                      <div className="flex flex-col gap-0.5">
                        <a className="text-2sm font-semibold text-gray-900 hover:text-primary-active" href="#">
                          Evernote
                        </a>
                        <span className="text-2xs font-medium text-gray-600">Notes management app</span>
                      </div>
                    </div>
                    <div className="flex shrink-0 justify-end">
                      <div className="flex -space-x-2">
                        <div className="flex">
                          <img
                            className="relative size-6 shrink-0 rounded-full ring-1 ring-light-light hover:z-5"
                            src="/media/avatars/300-6.png"
                            alt=""
                          />
                        </div>
                        <div className="flex">
                          <img
                            className="relative size-6 shrink-0 rounded-full ring-1 ring-light-light hover:z-5"
                            src="/media/avatars/300-3.png"
                            alt=""
                          />
                        </div>
                        <div className="flex">
                          <img
                            className="relative size-6 shrink-0 rounded-full ring-1 ring-light-light hover:z-5"
                            src="/media/avatars/300-1.png"
                            alt=""
                          />
                        </div>
                        <div className="flex">
                          <img
                            className="relative size-6 shrink-0 rounded-full ring-1 ring-light-light hover:z-5"
                            src="/media/avatars/300-8.png"
                            alt=""
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="menu-item">
                  <div className="jistify-between menu-link flex items-center gap-2">
                    <div className="flex grow items-center gap-2">
                      <div className="flex size-10 shrink-0 items-center justify-center rounded-full border border-gray-200 bg-gray-100">
                        <img alt="" className="size-6 shrink-0" src="/media/brand-logos/gitlab.svg" />
                      </div>
                      <div className="flex flex-col gap-0.5">
                        <a className="text-2sm font-semibold text-gray-900 hover:text-primary-active" href="#">
                          Gitlab
                        </a>
                        <span className="text-2xs font-medium text-gray-600">Notes management app</span>
                      </div>
                    </div>
                    <div className="flex shrink-0 justify-end">
                      <div className="flex -space-x-2">
                        <div className="flex">
                          <img
                            className="relative size-6 shrink-0 rounded-full ring-1 ring-light-light hover:z-5"
                            src="/media/avatars/300-18.png"
                            alt=""
                          />
                        </div>
                        <div className="flex">
                          <img
                            className="relative size-6 shrink-0 rounded-full ring-1 ring-light-light hover:z-5"
                            src="/media/avatars/300-17.png"
                            alt=""
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="menu-item">
                  <div className="jistify-between menu-link flex items-center gap-2">
                    <div className="flex grow items-center gap-2">
                      <div className="flex size-10 shrink-0 items-center justify-center rounded-full border border-gray-200 bg-gray-100">
                        <img alt="" className="size-6 shrink-0" src="/media/brand-logos/google-webdev.svg" />
                      </div>
                      <div className="flex flex-col gap-0.5">
                        <a className="text-2sm font-semibold text-gray-900 hover:text-primary-active" href="#">
                          Google webdev
                        </a>
                        <span className="text-2xs font-medium text-gray-600">Building web expierences</span>
                      </div>
                    </div>
                    <div className="flex shrink-0 justify-end">
                      <div className="flex -space-x-2">
                        <div className="flex">
                          <img
                            className="relative size-6 shrink-0 rounded-full ring-1 ring-light-light hover:z-5"
                            src="/media/avatars/300-14.png"
                            alt=""
                          />
                        </div>
                        <div className="flex">
                          <img
                            className="relative size-6 shrink-0 rounded-full ring-1 ring-light-light hover:z-5"
                            src="/media/avatars/300-20.png"
                            alt=""
                          />
                        </div>
                        <div className="flex">
                          <img
                            className="relative size-6 shrink-0 rounded-full ring-1 ring-light-light hover:z-5"
                            src="/media/avatars/300-21.png"
                            alt=""
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="menu-item px-4 pt-2">
                  <a className="btn btn-sm btn-light justify-center" href="#">
                    Go to Apps
                  </a>
                </div>
              </div>
            </div>
            <div className="hidden" id="search_modal_users">
              <div className="menu menu-default flex-col p-0">
                <div className="grid gap-1">
                  <div className="menu-item">
                    <div className="menu-link flex justify-between gap-2">
                      <div className="flex items-center gap-2.5">
                        <img alt="" className="size-9 shrink-0 rounded-full" src="/media/avatars/300-3.png" />
                        <div className="flex flex-col">
                          <a className="mb-px text-sm font-semibold text-gray-900 hover:text-primary-active" href="#">
                            Tyler Hero
                          </a>
                          <span className="text-2sm font-normal text-gray-500"><EMAIL> connections</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2.5">
                        <div className="badge badge-pill badge-outline badge-success gap-1.5">
                          <span className="badge badge-dot badge-success size-1.5"></span>
                          In Office
                        </div>
                        <button className="btn-icon btn btn-sm btn-clear btn-light">
                          <i className="ki-filled ki-dots-vertical"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                  <div className="menu-item">
                    <div className="menu-link flex justify-between gap-2">
                      <div className="flex items-center gap-2.5">
                        <img alt="" className="size-9 shrink-0 rounded-full" src="/media/avatars/300-1.png" />
                        <div className="flex flex-col">
                          <a className="mb-px text-sm font-semibold text-gray-900 hover:text-primary-active" href="#">
                            Esther Howard
                          </a>
                          <span className="text-2sm font-normal text-gray-500"><EMAIL> connections</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2.5">
                        <div className="badge badge-pill badge-outline badge-danger gap-1.5">
                          <span className="badge badge-dot badge-danger size-1.5"></span>
                          On Leave
                        </div>
                        <button className="btn-icon btn btn-sm btn-clear btn-light">
                          <i className="ki-filled ki-dots-vertical"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                  <div className="menu-item">
                    <div className="menu-link flex justify-between gap-2">
                      <div className="flex items-center gap-2.5">
                        <img alt="" className="size-9 shrink-0 rounded-full" src="/media/avatars/300-11.png" />
                        <div className="flex flex-col">
                          <a className="mb-px text-sm font-semibold text-gray-900 hover:text-primary-active" href="#">
                            Jacob Jones
                          </a>
                          <span className="text-2sm font-normal text-gray-500"><EMAIL> connections</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2.5">
                        <div className="badge badge-pill badge-outline badge-primary gap-1.5">
                          <span className="badge badge-dot badge-primary size-1.5"></span>
                          Remote
                        </div>
                        <button className="btn-icon btn btn-sm btn-clear btn-light">
                          <i className="ki-filled ki-dots-vertical"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                  <div className="menu-item">
                    <div className="menu-link flex justify-between gap-2">
                      <div className="flex items-center gap-2.5">
                        <img alt="" className="size-9 shrink-0 rounded-full" src="/media/avatars/300-5.png" />
                        <div className="flex flex-col">
                          <a className="mb-px text-sm font-semibold text-gray-900 hover:text-primary-active" href="#">
                            TLeslie Alexander
                          </a>
                          <span className="text-2sm font-normal text-gray-500"><EMAIL> connections</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2.5">
                        <div className="badge badge-pill badge-outline badge-success gap-1.5">
                          <span className="badge badge-dot badge-success size-1.5"></span>
                          In Office
                        </div>
                        <button className="btn-icon btn btn-sm btn-clear btn-light">
                          <i className="ki-filled ki-dots-vertical"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                  <div className="menu-item">
                    <div className="menu-link flex justify-between gap-2">
                      <div className="flex items-center gap-2.5">
                        <img alt="" className="size-9 shrink-0 rounded-full" src="/media/avatars/300-2.png" />
                        <div className="flex flex-col">
                          <a className="mb-px text-sm font-semibold text-gray-900 hover:text-primary-active" href="#">
                            Cody Fisher
                          </a>
                          <span className="text-2sm font-normal text-gray-500"><EMAIL> connections</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2.5">
                        <div className="badge badge-pill badge-outline badge-primary gap-1.5">
                          <span className="badge badge-dot badge-primary size-1.5"></span>
                          Remote
                        </div>
                        <button className="btn-icon btn btn-sm btn-clear btn-light">
                          <i className="ki-filled ki-dots-vertical"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                  <div className="menu-item px-4 pt-2">
                    <a className="btn btn-sm btn-light justify-center" href="#">
                      Go to Users
                    </a>
                  </div>
                </div>
              </div>
            </div>
            <div className="hidden" id="search_modal_docs">
              <div className="menu menu-default flex-col p-0">
                <div className="grid">
                  <div className="menu-item">
                    <div className="menu-link flex items-center">
                      <div className="flex grow items-center gap-2.5">
                        <img src="/media/file-types/pdf.svg" alt="" />
                        <div className="flex flex-col">
                          <span className="mb-px cursor-pointer text-sm font-semibold text-gray-900 hover:text-primary">
                            Project-pitch.pdf
                          </span>
                          <span className="text-xs font-medium text-gray-500">4.7 MB 26 Sep 2024 3:20 PM</span>
                        </div>
                      </div>
                      <button className="btn-icon btn btn-sm btn-clear btn-light">
                        <i className="ki-filled ki-dots-vertical"></i>
                      </button>
                    </div>
                  </div>
                  <div className="menu-item">
                    <div className="menu-link flex items-center">
                      <div className="flex grow items-center gap-2.5">
                        <img src="/media/file-types/doc.svg" alt="" />
                        <div className="flex flex-col">
                          <span className="mb-px cursor-pointer text-sm font-semibold text-gray-900 hover:text-primary">
                            Report-v1.docx
                          </span>
                          <span className="text-xs font-medium text-gray-500">2.3 MB 1 Oct 2024 12:00 PM</span>
                        </div>
                      </div>
                      <button className="btn-icon btn btn-sm btn-clear btn-light">
                        <i className="ki-filled ki-dots-vertical"></i>
                      </button>
                    </div>
                  </div>
                  <div className="menu-item">
                    <div className="menu-link flex items-center">
                      <div className="flex grow items-center gap-2.5">
                        <img src="/media/file-types/javascript.svg" alt="" />
                        <div className="flex flex-col">
                          <span className="mb-px cursor-pointer text-sm font-semibold text-gray-900 hover:text-primary">
                            Framework-App.js
                          </span>
                          <span className="text-xs font-medium text-gray-500">0.8 MB 17 Oct 2024 6:46 PM</span>
                        </div>
                      </div>
                      <button className="btn-icon btn btn-sm btn-clear btn-light">
                        <i className="ki-filled ki-dots-vertical"></i>
                      </button>
                    </div>
                  </div>
                  <div className="menu-item">
                    <div className="menu-link flex items-center">
                      <div className="flex grow items-center gap-2.5">
                        <img src="/media/file-types/ai.svg" alt="" />
                        <div className="flex flex-col">
                          <span className="mb-px cursor-pointer text-sm font-semibold text-gray-900 hover:text-primary">
                            Framework-App.js
                          </span>
                          <span className="text-xs font-medium text-gray-500">0.8 MB 17 Oct 2024 6:46 PM</span>
                        </div>
                      </div>
                      <button className="btn-icon btn btn-sm btn-clear btn-light">
                        <i className="ki-filled ki-dots-vertical"></i>
                      </button>
                    </div>
                  </div>
                  <div className="menu-item">
                    <div className="menu-link flex items-center">
                      <div className="flex grow items-center gap-2.5">
                        <img src="/media/file-types/php.svg" alt="" />
                        <div className="flex flex-col">
                          <span className="mb-px cursor-pointer text-sm font-semibold text-gray-900 hover:text-primary">
                            appController.js
                          </span>
                          <span className="text-xs font-medium text-gray-500">0.1 MB 21 Nov 2024 3:20 PM</span>
                        </div>
                      </div>
                      <button className="btn-icon btn btn-sm btn-clear btn-light">
                        <i className="ki-filled ki-dots-vertical"></i>
                      </button>
                    </div>
                  </div>
                  <div className="menu-item px-4 pt-2.5">
                    <a className="btn btn-sm btn-light justify-center" href="#">
                      Go to Users
                    </a>
                  </div>
                </div>
              </div>
            </div>
            <div className="hidden" id="search_modal_empty">
              <div className="flex flex-col gap-5 py-9 text-center">
                <div className="flex justify-center">
                  <img alt="image" className="max-h-[113px] dark:hidden" src="/media/illustrations/33.svg" />
                  <img alt="image" className="max-h-[113px] light:hidden" src="/media/illustrations/33-dark.svg" />
                </div>
                <div className="flex flex-col gap-1.5">
                  <h3 className="text-center text-base font-semibold text-gray-900">Looking for something..</h3>
                  <span className="text-center text-2sm font-medium text-gray-600">
                    Initiate your digital experience with
                    <br />
                    our intuitive dashboard
                  </span>
                </div>
                <div className="flex justify-center">
                  <a className="btn btn-sm btn-light flex justify-center" href="#">
                    View Projects
                  </a>
                </div>
              </div>
            </div>
            <div className="hidden" id="search_modal_no-results">
              <div className="flex flex-col gap-5 py-9 text-center">
                <div className="flex justify-center">
                  <img alt="image" className="max-h-[113px] dark:hidden" src="/media/illustrations/33.svg" />
                  <img alt="image" className="max-h-[113px] light:hidden" src="/media/illustrations/33-dark.svg" />
                </div>
                <div className="flex flex-col gap-1.5">
                  <h3 className="text-center text-base font-semibold text-gray-900">No Results Found</h3>
                  <span className="text-center text-2sm font-medium text-gray-600">Refine your query to discover relevant items</span>
                </div>
                <div className="flex justify-center">
                  <a className="btn btn-sm btn-light flex justify-center" href="#">
                    View Projects
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
