'use client';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';

/**
 * @brief 페이지 5xx 에러 페이지
 * @returns
 */
export default function Page5xx() {
  const router = useRouter();
  const t_i18n = useTranslations('i18nData');
  return (
    <>
      <div className="container-fixed pb-[60px] pt-[100px]">
        <div className="flex h-full grow items-center justify-center">
          <div className="flex flex-col items-center">
            <div className="mb-16">
              <img alt="image" className="max-h-[160px] dark:hidden" src="/media/illustrations/20.svg" />
            </div>
            <span className="badge badge-outline badge-primary mb-3">500 Error</span>
            <h3 className="mb-2 text-center text-2.5xl font-semibold text-gray-900">{t_i18n('http_internal_server_error')}</h3>
            <div
              className="mb-10 text-center text-md font-medium text-gray-600"
              dangerouslySetInnerHTML={{ __html: t_i18n('http_internal_server_error_msg') }}
            ></div>
            <a
              className="btn btn-primary flex justify-center"
              onClick={() => {
                router.back();
              }}
            >
              Back to Page
            </a>
          </div>
        </div>
      </div>
    </>
  );
}
