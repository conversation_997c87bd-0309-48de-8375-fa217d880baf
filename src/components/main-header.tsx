'use client';
import AccountMenu from './account-menu';
import { getUser } from '@/action/user-action';

import { useSession } from 'next-auth/react';
import MainMenu from './main-menu';

import { useLocale, useTranslations } from 'next-intl';
import LocaleSwitch from './locale-switch';
import { use, useEffect, useState } from 'react';
import { useParams, usePathname } from 'next/navigation';
import { User, UserResponse } from '@/types/user';
import { getPointBase, getPointStatus } from '@/action/point-action';
import { PointStatus } from '@/types/point-status';
import { BsExclamationTriangle } from 'react-icons/bs';
import { comma } from '@/utils';
import Link from 'next/link';
import SessionModal from './modal/session-modal';

import UserAlarmComp from './user/user-alarm';
import NoticeAlertBanner from './banner/notice-alert-banner';

interface PageProps {
  user: User;
}
/**
 * @brief Header 컴포넌트
 * @returns
 */
export default function Header() {
  const locale = useLocale();
  const t_i18n = useTranslations('i18nData');

  const { data: session } = useSession();

  const [user, setUser] = useState<User>(null);
  const pathname = usePathname(); // 동적 경로 파라미터 가져오기

  const [pointStatus, setPointStatus] = useState<PointStatus>(null);
  const [checkSession, setCheckSession] = useState<boolean>(true);

  const terminalPagePattern = /^\/(ko|en)\/demand\/workload\/[^/]+\/pod\/[^/]+(\/terminal)?$/;

  useEffect(() => {
    const init = async () => {
      const userResponse: UserResponse = await getUser(session.user.email);

      if (userResponse.status == 200) {
        setUser(userResponse.user);
        setCheckSession(true);
      } else {
        setCheckSession(false);
      }

      const pointRequest: any = {
        startTime: '',
        endTime: ''
      };

      const pointStatusResponse = await getPointStatus(pointRequest);

      if (pointStatusResponse.status == 200) {
        setPointStatus(pointStatusResponse.points);
        // setPointStatus({ ...pointStatusResponse.points, availablePoint: -12 });
      }
    };
    init();
  }, []);

  if (!checkSession) {
    return <SessionModal sessionModal={true}></SessionModal>;
  }

  if (user != null) {
    //컨테이너 로그 / 컨테이너 터미널 / 컨테이너 SSH 새창으로 열었을 때, Main Header를 안보여주기 위함
    if (terminalPagePattern.test(pathname)) {
      return <></>;
    }
    return (
      <header className="left-0 right-0 top-0 z-10 flex shrink-0 items-center justify-center lg:p-5" id="header">
        <div
          className="item-center card flex w-full flex-col flex-nowrap !rounded-none !border-[#DBDFE9] lg:!max-w-[1440px] lg:flex-row lg:!rounded-[8px]"
          id="header_container"
        >
          {/* <MainHeaderCookie /> */}

          <div className="flex h-[68px] w-full flex-nowrap items-center justify-between p-3.5">
            <div className="flex shrink-0 items-center gap-1">
              <Link locale={locale} href={`/${locale}/gate`}>
                <img alt="" className="inline-block h-[30px]" src={`/da/img/ico_gcube_beta.svg`} />
              </Link>
            </div>
            {pathname.indexOf('gate') == -1 && (
              <div className="hidden lg:!block">
                <MainMenu user={user}></MainMenu>
              </div>
            )}
            <div className="flex items-center justify-center gap-1.5">
              <div className="flex flex-nowrap items-center gap-1 text-[12px] leading-[12px]">
                {pathname.indexOf('demand') > -1 && (pointStatus?.isAlarm || pointStatus?.isBlocked) && !user.isDedicated && (
                  <>
                    <div className="badge badge-outline badge-warning hidden lg:!block">{t_i18n('workload_point_payment_required')}</div>
                    <div className="badge badge-outline badge-warning block lg:!hidden" data-tooltip="#default_tooltip">
                      <BsExclamationTriangle />
                      <div className="tooltip" id="default_tooltip">
                        {t_i18n('workload_point_payment_required')}
                      </div>
                    </div>
                  </>
                )}

                {pathname.indexOf('provision') > -1 && (
                  <>
                    <img src="/da/img/ico_point.svg" className="w-[14px]" alt="" />
                    <span>
                      <Link href={`/${locale}/provision/point`}>
                        {comma(pointStatus?.availablePoint < 0 ? 0 : pointStatus?.availablePoint)}
                      </Link>
                    </span>
                  </>
                )}
                {pathname.indexOf('demand') > -1 && !user.isDedicated && (
                  <>
                    <img src="/da/img/ico_point.svg" className="w-[14px]" alt="" />
                    <span>
                      <Link href={`/${locale}/demand/point`}>
                        {comma(pointStatus?.availablePoint < 0 ? 0 : pointStatus?.availablePoint)}
                      </Link>
                    </span>
                  </>
                )}
              </div>
              {user != null && (
                <div className="ml-4 flex items-center gap-2">
                  <UserAlarmComp />
                  <AccountMenu user={user}></AccountMenu>
                </div>
              )}
              <LocaleSwitch isPoint={false}></LocaleSwitch>
            </div>
          </div>
          {pathname.indexOf('gate') == -1 && (
            <div className="block flex h-[56px] w-full justify-center border-t border-[#DBDFE9] px-5 py-2 lg:!hidden">
              <MainMenu user={user}></MainMenu>
            </div>
          )}
        </div>
      </header>
    );
  }
}
