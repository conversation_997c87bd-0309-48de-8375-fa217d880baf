'use client';
import { useTheme } from 'next-themes';
import { ChangeEvent } from 'react';
export default function ThemeSwitch() {
  const { theme, setTheme } = useTheme();
  const changeTheme = (e: ChangeEvent) => {
    const { value } = e.target as HTMLInputElement;

    if (value == 'light') setTheme('dark');
    else setTheme('light');
  };

  return (
    <>
      <div className="menu-link">
        <span className="menu-icon">
          <i className="ki-filled ki-moon"></i>
        </span>
        <label className="switch switch-sm">
          <input name="check" type="checkbox" checked={theme == 'dark' ? true : false} value={theme} onChange={changeTheme} />
        </label>
      </div>
    </>
  );
}
