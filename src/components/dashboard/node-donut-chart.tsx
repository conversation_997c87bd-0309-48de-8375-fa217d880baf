'use client';

import { NodeStateCount } from '@/types/node';
import { useEffect, useState } from 'react';

import { useTranslations } from 'next-intl';
import dynamic from 'next/dynamic';

const ApexChartn = dynamic(() => import('react-apexcharts'), { ssr: false });

interface PageProps {
  data: NodeStateCount[];
}
export default function NodeDonutChart({ data }: PageProps) {
  const t_i18n = useTranslations('i18nData');

  const [series, setSeries] = useState<number[]>([0, 0, 0, 0]);
  const [isComplete, setComplete] = useState<boolean>(false);
  const [isNodata, setNodata] = useState<boolean>(false);

  useEffect(() => {
    const dataInit = async () => {
      data.forEach((item, index) => {
        if (item.state == 'open') {
          setSeries((prevData) => {
            const newData = [...prevData];
            newData[0] = item.count;
            return newData;
          });
        } else if (item.state == 'provi') {
          setSeries((prevData) => {
            const newData = [...prevData];
            newData[1] = item.count;
            return newData;
          });
        } else if (item.state == 'run') {
          setSeries((prevData) => {
            const newData = [...prevData];
            newData[2] = item.count;
            return newData;
          });
        } else if (item.state == 'fail') {
          setSeries((prevData) => {
            const newData = [...prevData];
            newData[3] = item.count;
            return newData;
          });
        }
        // else if (item.state == 'stop') {
        //   setSeries((prevData) => {
        //     // return { ...prevData, [4]: item.count };
        //     const newData = [...prevData];
        //     newData[4] = item.count;
        //     return newData;
        //   });
        // } else if (item.state == 'create') {
        //   setSeries((prevData) => {
        //     // return { ...prevData, [5]: item.count };
        //     const newData = [...prevData];
        //     newData[5] = item.count;
        //     return newData;
        //   });
        // }
      });

      setComplete(true);
    };
    if (data.length > 0) {
      //데이터 세팅
      dataInit();
    } else {
      setComplete(true);
    }
  }, [data]);

  useEffect(() => {
    if (isComplete) {
      const checkList = series.filter((item) => item > 0);
      if (checkList.length > 0) {
        setNodata(false);
      } else {
        setNodata(true);
        setSeries([1]);
      }
    }
  }, [isComplete]);

  const option: any = {
    chart: {
      id: 'apexchart-node',
      type: 'donut'
    },
    // plotOptions: {
    //   pie: {
    //     donut: {
    //       size: '50%' // 도넛의 크기 조정
    //     }
    //   }
    // },
    labels: [
      t_i18n('node_status_open'),
      t_i18n('node_status_provision'),
      t_i18n('node_status_running'),
      t_i18n('node_status_fail')
      // t_i18n('node_status_stop'),
      // t_i18n('node_status_created')
    ],

    dataLabels: {
      formatter: (value: any) => {
        const label = value.toFixed(2);
        if (!isNodata) {
          if (label.indexOf('.') !== -1) {
            return label.replace(/\.?0+$/, '') + '%';
          }

          return label + '%';
        } else {
          return '';
        }
      }
    },

    tooltip: {
      enabled: true,
      y: {
        formatter: (value: any) => {
          return value;
        },
        title: {
          formatter: (seriesName: string) => {
            return seriesName ? seriesName.toString() : '';
          }
        }
      }
    },
    legend: {
      show: true,
      // position: 'bottom',
      // horizontalAlign: 'center',
      // floating: false,
      height: '100%',
      formatter: (label: any, opt: any) => {
        const percent = opt.w.globals.seriesPercent[opt.seriesIndex] as number;

        if (!isNodata) {
          if (percent > 0) {
            const value = Number(percent).toFixed(2);

            if (value.indexOf('.') !== -1) {
              return label + ' - ' + value.replace(/\.?0+$/, '') + '%';
            }

            return label + '%';
          } else {
            return label + ' - 0%';
          }
        } else {
          return label + ' - 0%';
        }
      }
    },

    // colors: ['#78829D', '#8247FF', '#17C653', '#F8285A', '#F6B100', '#1B84FF']
    colors: ['#78829D', '#8247FF', '#17C653', '#F8285A']
  };

  const optionNo: any = {
    chart: {
      id: 'apexchart-node',
      type: 'donut'
    },

    labels: [
      t_i18n('node_status_open'),
      t_i18n('node_status_provision'),
      t_i18n('node_status_running'),
      t_i18n('node_status_fail')
      // t_i18n('node_status_stop'),
      // t_i18n('node_status_created')
    ],

    dataLabels: {
      formatter: (value: any) => {
        const label = value.toFixed(2);
        if (!isNodata) {
          if (label.indexOf('.') !== -1) {
            return label.replace(/\.?0+$/, '') + '%';
          }

          return label + '%';
        } else {
          return '';
        }
      }
    },

    tooltip: {
      enabled: false
    },
    legend: {
      show: true,
      // position: 'bottom',
      // horizontalAlign: 'center',
      // floating: false,
      height: '100%',
      formatter: (label: any, opt: any) => {
        const percent = opt.w.globals.seriesPercent[opt.seriesIndex] as number;
        if (!isNodata) {
          if (percent > 0) {
            const value = Number(percent).toFixed(2);

            if (value.indexOf('.') !== -1) {
              return label + ' - ' + value.replace(/\.?0+$/, '') + '%';
            }

            return label + '%';
          } else {
            return label + ' - 0%';
          }
        } else {
          return label + ' - 0%';
        }
      }
    },

    plotOptions: {
      pie: {
        // donut: {
        //   size: '65%', // 도넛 크기
        //   background: 'transparent', // 도넛 내부 배경색
        //   borderColor: '#000000', // 테두리 색상 (검정색으로 예시)
        //   borderWidth: 2 // 테두리 두께
        // }
        stroke: {
          show: true,
          width: 0 // 내부 구분선 제거
        }
      }
    },

    stroke: {
      show: true,
      curve: 'smooth',
      lineCap: 'butt',
      colors: '#C4CADA',
      width: 1,
      dashArray: 0
    },

    fill: {
      colors: ['#FFFFFF'] // 데이터가 없을 때 흰색으로 채우기
    },

    states: {
      normal: {
        filter: {
          type: 'none',
          value: 0
        }
      },
      hover: {
        filter: {
          type: 'none',
          value: 0
        }
      },
      active: {
        filter: {
          type: 'none',
          value: 0
        }
      }
    },

    // colors: ['#78829D', '#8247FF', '#17C653', '#F8285A', '#F6B100', '#1B84FF']
    colors: ['#78829D', '#8247FF', '#17C653', '#F8285A']
  };

  return (
    <>
      {isComplete && !isNodata && <ApexChartn id="apexChartn" type="donut" options={option} series={series} width={'370px'}></ApexChartn>}
      {isComplete && isNodata && <ApexChartn id="apexChartn" type="donut" options={optionNo} series={[1]} width={'370px'}></ApexChartn>}
    </>
  );
}
