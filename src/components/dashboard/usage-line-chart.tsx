'use client';

import { Usage } from '@/types/usage';
import { convertTimeStampToDateTime, utcTime } from '@/utils';
import { useEffect, useState } from 'react';
// import ApexChart from 'react-apexcharts';
import { useTranslations } from 'next-intl';
import dynamic from 'next/dynamic';

const ApexChart = dynamic(() => import('react-apexcharts'), { ssr: false });
interface PageProps {
  data: Usage[];
}
interface ChartUsageType {
  name: string;
  data: number[];
}

export default function UsageLineChart({ data }: PageProps) {
  const t_i18n = useTranslations('i18nData');

  const [chartLabel, setChartLabel] = useState<string[]>([]);
  const [chartData, setChartData] = useState<Usage[]>([]);
  const [isComplete, setComplete] = useState<boolean>(false);
  const [min] = useState<number>(0);
  const [max, setMax] = useState<number | undefined>(undefined);

  const [gpuData, setGpuData] = useState<number[]>([]);
  const [vramData, setVramData] = useState<number[]>([]);
  const [cpuData, setCpuData] = useState<number[]>([]);
  const [memoryData, setMemoryData] = useState<number[]>([]);
  const [diskData, setDiskData] = useState<number[]>([]);
  const [diskOccuData, setDiskOccuData] = useState<number[]>([]);
  const [netRx, setNetRx] = useState<number[]>([]);
  const [netTx, setNetTx] = useState<number[]>([]);

  const [targetLabel, setTargetLabel] = useState<string>('');
  const [targetData, setTargetData] = useState<ChartUsageType[]>([]);

  //컴포넌트 로딩 후 최초 1회 실행
  // 차트 데이터 세팅
  useEffect(() => {
    setChartData(data);
  }, []);

  useEffect(() => {
    if (isComplete) {
      setTargetLabel('gpu');
    }
  }, [isComplete]);

  useEffect(() => {
    const initChart = async () => {
      setChartLabel([]);
      setGpuData([]);
      setVramData([]);
      setCpuData([]);
      setMemoryData([]);
      setDiskData([]);
      setDiskOccuData([]);
      setNetRx([]);
      setNetTx([]);
      if (chartData != undefined && chartData != null) {
        chartData.map((item, index) => {
          const date = utcTime(item.timeInterval);

          setChartLabel((prevData) => [...prevData, date]);
          setGpuData((prevData) => [...prevData, item.gpuUsage]);
          setVramData((prevData) => [...prevData, item.vramUsage]);
          setCpuData((prevData) => [...prevData, item.cpuUsage]);
          setMemoryData((prevData) => [...prevData, item.memUsage]);
          setDiskData((prevData) => [...prevData, item.diskUsage]);
          setDiskOccuData((prevData) => [...prevData, item.diskOccu]);
          setNetRx((prevData) => [...prevData, item.netRx]);
          setNetTx((prevData) => [...prevData, item.netTx]);
        });
        setComplete(true);
      }
    };
    initChart();
  }, [chartData]);

  //yaxis Max값 설정
  useEffect(() => {
    if (targetLabel == 'cpu') {
      setMax(100);
      setTargetData((prevData) => {
        return [{ name: 'cpu', data: [...cpuData] }];
      });
    } else if (targetLabel == 'gpu') {
      setTargetData(() => [{ name: 'gpu', data: [...gpuData] }]);
      setMax(100);
    } else if (targetLabel == 'disk') {
      setTargetData(() => [{ name: 'disk', data: diskData }]);
      setMax(100);
    } else if (targetLabel == 'vram') {
      setTargetData(() => [{ name: 'vram', data: vramData }]);
      setMax(undefined);
    } else if (targetLabel == 'memory') {
      setTargetData(() => [{ name: 'memory', data: memoryData }]);
      setMax(undefined);
    } else if (targetLabel == 'network') {
      setTargetData(() => [
        { name: 'In', data: netRx },
        { name: 'Out', data: netTx }
      ]);
      setMax(undefined);
    }
  }, [targetLabel]);

  const option: any = {
    chart: {
      id: 'apexchart-resource'
    },
    xaxis: {
      categories: chartLabel,
      type: 'datetime'
    },
    yaxis: {
      show: true,
      min: min,
      max: max,
      labels: {
        formatter: (value: any) => {
          return Math.floor(value);
        }
      }
    },
    dataLabels: {
      enabled: false,
      formatter: (value: any) => {
        if (value % 1 === 0) {
          return Math.floor(value);
        } else {
          return parseFloat(value.toFixed(3));
        }
      }
    },

    tooltip: {
      x: {
        format: 'yyyy-MM-dd HH:mm:ss'
      },
      y: {
        formatter: (value: any) => {
          let unit = '';
          if (targetLabel == 'cpu') {
            unit = '%';
          } else if (targetLabel == 'gpu') {
            unit = '%';
          } else if (targetLabel == 'disk') {
            unit = 'MB';
          } else if (targetLabel == 'vram') {
            unit = 'MB';
          } else if (targetLabel == 'memory') {
            unit = 'MB';
          } else if (targetLabel == 'network') {
            unit = 'Kbps';
          }

          if (value % 1 === 0) {
            return Math.floor(value);
          } else {
            if (targetLabel == 'vram' || targetLabel == 'memory' || targetLabel == 'disk') {
              return Math.round(value);
            } else {
              return parseFloat(value.toFixed(3));
            }
          }
        },
        title: {
          formatter: (seriesName: string) => seriesName
        }
      }
    },
    fill: {
      type: 'gradient',
      gradient: {
        shadeIntensity: 1,
        opacityFrom: 0.7,
        opacityTo: 0.9,
        stops: [0, 100]
      }
    }
  };

  return (
    <>
      <div className="flex flex-col gap-5 md:flex-row md:items-center">
        <div className="flex flex-col gap-2 md:w-[200px] md:max-w-[200px]">
          <button className={`${targetLabel == 'gpu' ? 'active btn-primary' : 'btn-secondary'} btn`} onClick={() => setTargetLabel('gpu')}>
            {t_i18n('resource_gpu')} (%)
          </button>
          <button
            className={`${targetLabel == 'vram' ? 'active btn-primary' : 'btn-secondary'} btn`}
            onClick={() => setTargetLabel('vram')}
          >
            {t_i18n('resource_gpu_memory')} (MB)
          </button>
          <button className={`${targetLabel == 'cpu' ? 'active btn-primary' : 'btn-secondary'} btn`} onClick={() => setTargetLabel('cpu')}>
            {t_i18n('resource_cpu')} (%)
          </button>
          <button
            className={`${targetLabel == 'memory' ? 'active btn-primary' : 'btn-secondary'} btn`}
            onClick={() => setTargetLabel('memory')}
          >
            {t_i18n('resource_memory')} (MB)
          </button>
          <button
            className={`${targetLabel == 'disk' ? 'active btn-primary' : 'btn-secondary'} btn`}
            onClick={() => setTargetLabel('disk')}
          >
            {t_i18n('resource_disk')} (MB)
          </button>
          <button
            className={`${targetLabel == 'network' ? 'active btn-primary' : 'btn-secondary'} btn`}
            onClick={() => setTargetLabel('network')}
          >
            {t_i18n('resource_network')} (Kbps)
          </button>
        </div>
        <div className="flex grow">
          {isComplete && <ApexChart className="h-full w-full" type="area" options={option} series={targetData} height={360} />}
        </div>
      </div>
    </>
  );
}
