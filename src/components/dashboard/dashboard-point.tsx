import { getPointStatus } from '@/action/point-action';
import { Point, PointResponse } from '@/types/point';
import { comma } from '@/utils';
import { FaArrowDown, FaArrowUp, FaMinus, FaPlus } from 'react-icons/fa6';
import { getLocale, getTranslations } from 'next-intl/server';
interface PageProps {
  startTime: string;
  endTime: string;
}
export default async function DashboardPointPage({ startTime, endTime }: PageProps) {
  const t_i18n = await getTranslations('i18nData');

  const pointRequest: any = {
    startTime: startTime,
    endTime: endTime
  };
  const pointResponse: PointResponse = await getPointStatus(pointRequest);

  const points: Point = pointResponse.points;
  let point = points.income - points.spend;
  return (
    <>
      <div className="grid">
        <div className="col-span-1">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">{t_i18n('user_point_status')}</h3>
            </div>
            <div className="card-body">
              <div className="grid grid-cols-1 lg:grid-cols-3">
                <div className="flex flex-col items-center py-1.5 lg:flex-row lg:gap-4 lg:px-10">
                  <div className="grid flex-1 grid-cols-1 place-content-center gap-2 text-center">
                    <span className="text-xl font-semibold leading-none text-gray-900">{t_i18n('user_point_available')}</span>
                    <span className="flex items-center justify-center gap-2 text-xl font-medium text-blue-600">
                      {comma(points.totalAvailable)}
                    </span>
                  </div>
                  <span className="my-1 hidden h-full border-r border-r-gray-300 lg:block"></span>
                  <span className="mt-2 block w-[70%] border-b border-b-gray-300 lg:hidden"></span>
                </div>

                <div className="flex flex-col items-center py-1.5 lg:flex-row lg:gap-4 lg:px-10">
                  <div className="grid flex-1 grid-cols-1 place-content-center gap-2 text-center">
                    <span className="text-xl font-semibold leading-none text-gray-900">{t_i18n('user_point_income_total')}</span>
                    <span className="flex items-center justify-center gap-2 text-xl font-medium text-green-600">
                      <FaArrowUp />
                      {comma(points.totalIncome)}
                    </span>
                  </div>
                  <span className="my-1 hidden h-full border-r border-r-gray-300 lg:block"></span>
                  <span className="mt-2 block w-[70%] border-b border-b-gray-300 lg:hidden"></span>
                </div>

                <div className="flex flex-col items-center py-1.5 lg:flex-row lg:gap-4 lg:px-10">
                  <div className="grid flex-1 grid-cols-1 place-content-center gap-2 text-center">
                    <span className="text-xl font-semibold leading-none text-gray-900">{t_i18n('user_point_spend_total')}</span>
                    <span className="flex items-center justify-center gap-2 text-xl font-medium text-red-600">
                      <FaArrowDown />
                      {comma(points.totalSpend)}
                    </span>
                  </div>
                  <span className="mt-2 block w-[70%] border-b border-b-gray-300 lg:hidden"></span>
                </div>
              </div>
              <div className="grid grid-cols-1 lg:grid-cols-3 lg:pt-3">
                <div className="flex flex-col items-center py-1.5 lg:flex-row lg:gap-4 lg:px-10">
                  <div className="grid flex-1 grid-cols-1 place-content-center gap-2 text-center">
                    <span className="text-xl font-semibold leading-none text-gray-900">{t_i18n('user_point_month')}</span>
                    {point == 0 ? (
                      <span className="flex items-center justify-center gap-2 text-xl font-medium text-gray-600">{comma(point)}</span>
                    ) : point > 0 ? (
                      <span className="flex items-center justify-center gap-2 text-xl font-medium text-green-600">
                        <FaPlus />
                        {comma(point)}
                      </span>
                    ) : (
                      <span className="flex items-center justify-center gap-2 text-xl font-medium text-red-600">
                        <FaMinus />
                        {comma(point * -1)}
                      </span>
                    )}
                  </div>
                  <span className="my-1 hidden h-full border-r border-r-gray-300 lg:block"></span>
                  <span className="mt-2 block w-[70%] border-b border-b-gray-300 lg:hidden"></span>
                </div>
                <div className="flex flex-col items-center py-1.5 lg:flex-row lg:gap-4 lg:px-10">
                  <div className="grid flex-1 grid-cols-1 place-content-center gap-2 text-center">
                    <span className="text-xl font-semibold leading-none text-gray-900">{t_i18n('user_point_income_month')}</span>
                    <span className="flex items-center justify-center gap-2 text-xl font-medium text-green-600">
                      <FaArrowUp />
                      {comma(points.income)}
                    </span>
                  </div>
                  <span className="my-1 hidden h-full border-r border-r-gray-300 lg:block"></span>
                  <span className="mt-2 block w-[70%] border-b border-b-gray-300 lg:hidden"></span>
                </div>
                <div className="flex flex-col items-center py-1.5 lg:flex-row lg:gap-4 lg:px-10">
                  <div className="grid flex-1 grid-cols-1 place-content-center gap-2 text-center">
                    <span className="text-xl font-semibold leading-none text-gray-900">{t_i18n('user_point_spend_month')}</span>
                    <span className="flex items-center justify-center gap-2 text-xl font-medium text-red-600">
                      {/* <FaArrowDown />
                      {comma(points.spend)} */}

                      {points.spend == 0 ? (
                        <span className="flex items-center justify-center gap-2 text-xl font-medium text-gray-600">{comma(point)}</span>
                      ) : points.spend > 0 ? (
                        <span className="flex items-center justify-center gap-2 text-xl font-medium text-green-600">
                          <FaArrowUp />
                          {comma(points.spend)}
                        </span>
                      ) : (
                        <span className="flex items-center justify-center gap-2 text-xl font-medium text-red-600">
                          <FaArrowDown />
                          {comma(points.spend * -1)}
                        </span>
                      )}
                    </span>
                  </div>
                  <span className="mt-2 block w-[70%] border-b border-b-gray-300 lg:hidden"></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
