import { getNodeStateCount } from '@/action/node-action';
import { NodeStateCount } from '@/types/node';
import NodeDonutChart from './node-donut-chart';
import { getWorkloadStateCount } from '@/action/workload-action';
import { WorkloadStateCount } from '@/types/workload';
import WorkloadDonutChart from './workload-donut-chart';
import PodDonutChart from './pod-donut-chart';
import { getPods } from '@/action/pod-action';
import { getTranslations } from 'next-intl/server';
import { Pods, PodStateCount } from '@/types/pods';
export default async function DashboardClusterStatusPage() {
  const t_i18n = await getTranslations('i18nData');

  const podsListData = await getPods();
  const podsData: PodStateCount[] = podsListData.pods;

  const nodeStateData = await getNodeStateCount();
  const nodeData: NodeStateCount[] = nodeStateData.states;

  // const workloadStateData = await getWorkloadStateCount();
  // const workloadData: WorkloadStateCount[] = workloadStateData.states;
  return (
    <>
      <div className="flex flex-col">
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">{t_i18n('dashboard_cluster_status')}</h3>
          </div>
          <div className="card-body !px-0">
            <div className="grid grid-cols-1 lg:grid-cols-2">
              <div className="flex flex-col flex-wrap py-1.5">
                <div className="flex flex-nowrap">
                  <h3 className="pl-32 text-xl font-bold">{t_i18n('dashboard_node_status')}</h3>
                </div>
                {/* <div className="flex">{nodeData != null && nodeData.length > 0 && <NodeDonutChart data={nodeData}></NodeDonutChart>}</div> */}
              </div>
              {/* <div className="flex flex-col flex-wrap py-1.5">
                <div className="flex flex-nowrap">
                  <h3 className="pl-32 text-xl font-bold">{t_i18n('dashboard_workload_status')}</h3>
                </div>
                <div className="flex">
                  {workloadData != null && workloadData.length > 0 && <WorkloadDonutChart data={workloadData}></WorkloadDonutChart>}
                </div>
              </div> */}
              <div className="flex flex-col flex-wrap py-1.5">
                <div className="flex flex-nowrap">
                  <h3 className="pl-32 text-xl font-bold">{t_i18n('dashboard_pod_status')}</h3>
                </div>
                {/* <div className="flex">{podsData != null && podsData.length > 0 && <PodDonutChart data={podsData}></PodDonutChart>}</div> */}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
