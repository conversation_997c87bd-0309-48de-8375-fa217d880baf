'use client';

import { WorkloadStateCount } from '@/types/workload';
import { useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';

// import ApexChart from 'react-apexcharts';
import dynamic from 'next/dynamic';
const ApexChartw = dynamic(() => import('react-apexcharts'), { ssr: false });

interface PageProps {
  data: WorkloadStateCount[];
}

export default function WorkloadDonutChart({ data }: PageProps) {
  const t_i18n = useTranslations('i18nData');

  const [series, setSeries] = useState<number[]>([0, 0, 0]); // DEPLOY, FINISH, OPEN,
  const [isComplete, setComplete] = useState<boolean>(false);
  const [isNodata, setNodata] = useState<boolean>(false);

  useEffect(() => {
    const dataInit = async () => {
      data.forEach((item, index) => {
        if (item.state == 'deploy') {
          setSeries((prevData) => {
            const newData = [...prevData];
            newData[0] = item.count;
            return newData;
          });
        } else if (item.state == 'finish') {
          setSeries((prevData) => {
            const newData = [...prevData];
            newData[1] = item.count;
            return newData;
          });
        } else if (item.state == 'open') {
          setSeries((prevData) => {
            const newData = [...prevData];
            newData[2] = item.count;
            return newData;
          });
        }
      });

      setComplete(true);
    };

    if (data.length > 0) {
      dataInit();
    } else {
      setComplete(true);
    }
  }, [data]);

  useEffect(() => {
    if (isComplete) {
      const checkList = series.filter((item) => item > 0);

      if (checkList.length > 0) {
        setNodata(false);
      } else {
        setNodata(true);
        setSeries([1]);
      }
    }
  }, [isComplete]);

  const option: any = {
    chart: {
      id: 'apexchart-workload',
      type: 'donut'
    },

    labels: [t_i18n('workload_status_deploy'), t_i18n('workload_status_finish'), t_i18n('workload_status_open')],

    dataLabels: {
      formatter: (value: any) => {
        const label = value.toFixed(2);
        if (!isNodata) {
          if (label.indexOf('.') !== -1) {
            return label.replace(/\.?0+$/, '') + '%';
          }

          return label + '%';
        } else {
          return '';
        }
      }
    },

    tooltip: {
      enabled: true,
      y: {
        formatter: (value: any) => {
          return value;
        },
        title: {
          formatter: (seriesName: string) => seriesName
        }
      }
    },

    legend: {
      show: true,
      // position: 'bottom',
      // horizontalAlign: 'center',
      // floating: false,
      height: '100%',
      formatter: (label: any, opt: any) => {
        const percent = opt.w.globals.seriesPercent[opt.seriesIndex];

        if (!isNodata) {
          if (percent > 0) {
            const value = Number(percent).toFixed(2);

            if (value.indexOf('.') !== -1) {
              return label + ' - ' + value.replace(/\.?0+$/, '') + '%';
            }

            return label + '%';
          } else {
            return label + ' - 0%';
          }
        } else {
          return label + ' - 0%';
        }
      }
    },

    colors: ['#8247FF', '#1B84FF', '#78829D'] // DEPLOY, FINISH, OPEN
  };

  const optionNo: any = {
    chart: {
      id: 'apexchart-workload',
      type: 'donut'
    },

    labels: [t_i18n('workload_status_deploy'), t_i18n('workload_status_finish'), t_i18n('workload_status_open')],

    dataLabels: {
      formatter: (value: any) => {
        const label = value.toFixed(2);
        if (!isNodata) {
          if (label.indexOf('.') !== -1) {
            return label.replace(/\.?0+$/, '') + '%';
          }

          return label + '%';
        } else {
          return '';
        }
      }
    },

    tooltip: {
      enabled: false
    },

    legend: {
      show: true,
      // position: 'bottom',
      // horizontalAlign: 'center',
      // floating: false,
      height: '100%',
      formatter: (label: any, opt: any) => {
        const percent = opt.w.globals.seriesPercent[opt.seriesIndex];

        if (!isNodata) {
          if (percent > 0) {
            const value = Number(percent).toFixed(2);

            if (value.indexOf('.') !== -1) {
              return label + ' - ' + value.replace(/\.?0+$/, '') + '%';
            }

            return label + '%';
          } else {
            return label + ' - 0%';
          }
        } else {
          return label + ' - 0%';
        }
      }
    },

    plotOptions: {
      pie: {
        stroke: {
          show: true,
          width: 0 // 내부 구분선 제거
        }
      }
    },

    stroke: {
      show: true,
      curve: 'smooth',
      lineCap: 'butt',
      colors: '#C4CADA',
      width: 1,
      dashArray: 0
    },

    fill: {
      colors: ['#FFFFFF'] // 데이터가 없을 때 흰색으로 채우기
    },

    states: {
      normal: {
        filter: {
          type: 'none',
          value: 0
        }
      },
      hover: {
        filter: {
          type: 'none',
          value: 0
        }
      },
      active: {
        filter: {
          type: 'none',
          value: 0
        }
      }
    },

    colors: ['#8247FF', '#1B84FF', '#78829D'] // DEPLOY, FINISH, OPEN
  };

  return (
    <>
      {isComplete && !isNodata && <ApexChartw id="apexChartw" type="donut" options={option} series={series} width={'370px'}></ApexChartw>}
      {isComplete && isNodata && <ApexChartw id="apexChartw" type="donut" options={optionNo} series={series} width={'370px'}></ApexChartw>}
    </>
  );
}
