import { UsageRequest, UsageResponse } from '@/types/usage';
import UsageLineChart from './usage-line-chart';
import { getSession } from '@/auth';
import { getAVGUsage } from '@/action/usage-action';
import { getTranslations } from 'next-intl/server';
interface PageProps {
  startTime: string;
  endTime: string;
}
export default async function DashboardClusterResourcePage({ startTime, endTime }: PageProps) {
  const t_i18n = await getTranslations('i18nData');

  const usageRequest: UsageRequest = {
    owner: '',
    node: '',
    namespace: '',
    isPod: false,
    pod: '',
    interval: 600,
    startTime: startTime,
    endTime: endTime
  };

  const usageData: UsageResponse = await getAVGUsage(usageRequest);

  return (
    <>
      <div className="grid1 grid">
        <div className="col-span-1">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">{t_i18n('dashboard_cluster_resource')}</h3>
            </div>
            <div className="card-body">
              <div className="grid">{/* <UsageLineChart data={usageData.usages}></UsageLineChart> */}</div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
