'use client';

import { PodStateCount } from '@/types/pods';

import { useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';

import dynamic from 'next/dynamic';
const ApexChartp = dynamic(() => import('react-apexcharts'), { ssr: false });

interface PageProps {
  data: PodStateCount[];
}

export default function PodDonutChart({ data }: PageProps) {
  const t_i18n = useTranslations('i18nData');

  const [series, setSeries] = useState<number[]>([0, 0, 0, 0, 0]);
  const [isComplete, setComplete] = useState<boolean>(false);
  const [isNodata, setNodata] = useState<boolean>(false);

  useEffect(() => {
    const dataInit = async () => {
      data.forEach((item, index) => {
        if (item.state.toLowerCase() === 'pending') {
          setSeries((prevData) => {
            const newData = [...prevData];
            newData[0] = item.count;
            return newData;
          });
        } else if (item.state.toLowerCase() === 'running') {
          setSeries((prevData) => {
            const newData = [...prevData];
            newData[1] = item.count;
            return newData;
          });
        } else if (item.state.toLowerCase() === 'succeeded') {
          setSeries((prevData) => {
            const newData = [...prevData];
            newData[2] = item.count;
            return newData;
          });
        } else if (item.state.toLowerCase() === 'failed') {
          setSeries((prevData) => {
            const newData = [...prevData];
            newData[3] = item.count;
            return newData;
          });
        } else if (item.state.toLowerCase() === 'terminating') {
          setSeries((prevData) => {
            const newData = [...prevData];
            newData[4] = item.count;
            return newData;
          });
        }
      });

      setComplete(true);
    };

    if (data.length > 0) {
      dataInit();
    } else {
      setComplete(true);
    }
  }, [data]);

  useEffect(() => {
    if (isComplete) {
      const checkList = series.filter((item) => item > 0);

      if (checkList.length > 0) {
        setNodata(false);
      } else {
        setNodata(true);

        // setSeries((prevData) => [1]);
      }
    }
  }, [isComplete]);

  const podOption: any = {
    chart: {
      id: 'apexchart-pod',
      type: 'donut'
    },

    labels: [
      t_i18n('pod_status_pending'),
      t_i18n('pod_status_running'),
      t_i18n('pod_status_succeeded'),
      t_i18n('pod_status_failed'),
      t_i18n('pod_status_terminating')
      // t_i18n('pod_status_unknows'),
    ],

    dataLabels: {
      formatter: (value: any) => {
        const label = value.toFixed(2);
        if (!isNodata) {
          if (label.indexOf('.') !== -1) {
            return label.replace(/\.?0+$/, '') + '%';
          }

          return label + '%';
        } else {
          return '';
        }
      }
    },

    tooltip: {
      enabled: true,
      y: {
        formatter: (value: any) => {
          return value;
        },
        title: {
          formatter: (seriesName: string) => seriesName
        }
      }
    },

    legend: {
      show: true,
      // position: 'bottom',
      // horizontalAlign: 'center',
      // floating: false,
      height: '100%',
      formatter: (label: any, opt: any) => {
        const percent = opt.w.globals.seriesPercent[opt.seriesIndex];

        if (!isNodata) {
          if (percent > 0) {
            const value = Number(percent).toFixed(2);

            if (value.indexOf('.') !== -1) {
              return label + ' - ' + value.replace(/\.?0+$/, '') + '%';
            }

            return label + '%';
          } else {
            return label + ' - 0%';
          }
        } else {
          return label + ' - 0%';
        }
      }
    },

    noData: {
      text: 'No Data' // "No Data" 메시지 대신 빈 문자열로 설정
    },

    // states: {
    //   hover: {
    //     filter: {
    //       type: 'lighten', // hover 시 색상을 밝게 설정
    //       value: 0.1 // 밝게 변경할 정도
    //     }
    //   }
    // },

    colors: ['#78829D', '#17C653', '#8247FF', '#F8285A', '#1B84FF']
  };

  const podOptionNo: any = {
    chart: {
      id: 'apexchart-pod',
      type: 'donut'
    },

    labels: [
      t_i18n('pod_status_pending'),
      t_i18n('pod_status_running'),
      t_i18n('pod_status_succeeded'),
      t_i18n('pod_status_failed'),
      t_i18n('pod_status_terminating')
    ],

    dataLabels: {
      formatter: (value: any) => {
        const label = value.toFixed(2);
        if (!isNodata) {
          if (label.indexOf('.') !== -1) {
            return label.replace(/\.?0+$/, '') + '%';
          }

          return label + '%';
        } else {
          return '';
        }
      }
    },

    tooltip: {
      enabled: false
    },

    legend: {
      show: true,
      // position: 'bottom',
      // horizontalAlign: 'center',
      // floating: false,
      height: '220px',
      formatter: (label: any, opt: any) => {
        const percent = opt.w.globals.seriesPercent[opt.seriesIndex];

        if (!isNodata) {
          if (percent > 0) {
            const value = Number(percent).toFixed(2);

            if (value.indexOf('.') !== -1) {
              return label + ' - ' + value.replace(/\.?0+$/, '') + '%';
            }

            return label + '%';
          } else {
            return label + ' - 0%';
          }
        } else {
          return label + ' - 0%';
        }
      }
    },

    plotOptions: {
      pie: {
        stroke: {
          show: true,
          width: 0 // 내부 구분선 제거
        }
      }
    },

    stroke: {
      show: true,
      curve: 'smooth',
      lineCap: 'butt',
      colors: '#C4CADA',
      width: 1,
      dashArray: 0
    },

    fill: {
      colors: ['#FFFFFF'] // 데이터가 없을 때 흰색으로 채우기
    },

    states: {
      normal: {
        filter: {
          type: 'none',
          value: 0
        }
      },
      hover: {
        filter: {
          type: 'none',
          value: 0
        }
      },
      active: {
        filter: {
          type: 'none',
          value: 0
        }
      }
    },

    colors: ['#78829D', '#17C653', '#8247FF', '#F8285A', '#1B84FF']
  };

  return (
    <>
      {!isNodata && <ApexChartp id="apexChartp" type="donut" options={podOption} series={series} width={'370px'}></ApexChartp>}
      {isNodata && <ApexChartp id="apexChartp" type="donut" options={podOptionNo} series={[1]} width={'370px'}></ApexChartp>}
    </>
  );
}
