'use client';

import { KTTooltip } from '@/metronic/core';
import Script from 'next/script';
import { useRef } from 'react';

export default function KakaoChannelButton() {
  const tooltip = useRef(null);

  const onLoad = () => {
    window.Kakao.init('0c735d49a02594a64a8489ae1e44371e');

    if (window.Kakao.isInitialized()) {
      const tooltipEl = document.querySelector('#gcube-chat-button') as HTMLElement;
      tooltip.current = KTTooltip.getInstance(tooltipEl);

      tooltip.current.show();
    }
  };

  const chatChannel = () => {
    if (window.Kakao.isInitialized()) {
      window.Kakao.Channel.chat({
        channelPublicId: '_jBeTn'
      });
    }
  };

  const closeTooltip = () => {
    tooltip.current.hide();
  };

  return (
    <>
      <div id="gcube-chat-button-container" className="gcube-chat-button-container">
        <button
          data-tooltip="#gcube_chat_tooltip_content"
          id="gcube-chat-button"
          className="gcube-chat-button rounded-lg"
          onClick={() => {
            chatChannel();
          }}
        ></button>
      </div>

      <div
        className="tooltip rounded-xl border border-gray-200 bg-light p-3 text-xs font-normal text-gray-700 shadow-default"
        id="gcube_chat_tooltip_content"
      >
        전문가에게 무엇이든 물어보세요.{'  '}
        <button
          onClick={(e) => {
            e.preventDefault();
            closeTooltip();
          }}
        >
          <i className="ki-filled ki-cross"></i>
        </button>
      </div>
      <Script
        src="https://t1.kakaocdn.net/kakao_js_sdk/2.7.4/kakao.min.js"
        integrity="sha384-DKYJZ8NLiK8MN4/C5P2dtSmLQ4KwPaoqAfyA/DfmEc1VDxu4yyC7wy6K1Hs90nka"
        crossOrigin="anonymous"
        onLoad={onLoad}
      ></Script>
    </>
  );
}
