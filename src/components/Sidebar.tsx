import Link from 'next/link';

function Sidebar() {
  return (
    <div
      className="sidebar fixed bottom-0 top-0 z-20 hidden shrink-0 flex-col items-stretch border-r border-r-gray-200 bg-light dark:border-r-coal-100 dark:bg-coal-600 lg:flex"
      data-drawer="true"
      data-drawer-class="drawer drawer-start top-0 bottom-0"
      data-drawer-enable="true|lg:false"
      id="sidebar"
    >
      <div className="sidebar-header relative hidden shrink-0 items-center justify-between px-3 lg:flex lg:px-6" id="sidebar_header">
        <a className="dark:hidden" href="html/demo1.html">
          <img className="default-logo min-h-[22px] max-w-none" src="/media/app/default-logo.svg" alt="" />
          <img className="small-logo min-h-[22px] max-w-none" src="/media/app/mini-logo.svg" alt="" />
        </a>
        <a className="hidden dark:block" href="html/demo1.html">
          <img className="default-logo min-h-[22px] max-w-none" src="/media/app/default-logo-dark.svg" alt="" />
          <img className="small-logo min-h-[22px] max-w-none" src="/media/app/mini-logo.svg" alt="" />
        </a>
        <button
          className="toggle btn-icon btn btn-icon-md absolute left-full top-2/4 size-[30px] -translate-x-2/4 -translate-y-2/4 rounded-lg border border-gray-200 bg-light text-gray-500 hover:text-gray-700 dark:border-gray-300"
          data-toggle="body"
          data-toggle-class="sidebar-collapse"
          id="sidebar_toggle"
        >
          <i className="ki-filled ki-black-left-line transition-all duration-300 toggle-active:rotate-180"></i>
        </button>
      </div>
      <div className="sidebar-content flex shrink-0 grow py-5 pr-2" id="sidebar_content">
        <div
          className="scrollable-y-hover flex shrink-0 grow pl-2 pr-1 lg:pl-5 lg:pr-3"
          data-scrollable="true"
          data-scrollable-dependencies="#sidebar_header"
          data-scrollable-height="auto"
          data-scrollable-offset="0px"
          data-scrollable-wrappers="#sidebar_content"
          id="sidebar_scrollable"
        >
          <div className="menu flex grow flex-col gap-0.5" data-menu="true" data-menu-accordion-expand-all="false" id="sidebar_menu">
            <div className="menu-item">
              <div
                className="menu-link flex grow cursor-pointer items-center gap-[10px] border border-transparent py-[6px] pl-[10px] pr-[10px]"
                tabIndex={0}
              >
                <span className="menu-icon w-[20px] items-start text-gray-500 dark:text-gray-400">
                  <i className="ki-filled ki-element-11 text-lg"></i>
                </span>
                <span className="menu-title text-sm font-semibold text-gray-700 menu-item-active:text-primary menu-link-hover:!text-primary">
                  <a
                    className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                    href="/users/list"
                    tabIndex={0}
                  >
                    Dashboards
                  </a>
                </span>
                <span className="menu-arrow ml-1 mr-[-10px] w-[20px] shrink-0 justify-end text-gray-400">
                  <i className="ki-filled ki-plus text-2xs menu-item-show:hidden"></i>
                  <i className="ki-filled ki-minus hidden text-2xs menu-item-show:inline-flex"></i>
                </span>
              </div>
            </div>
            <div className="menu-item pb-px pt-2.25">
              <span className="menu-heading pl-[10px] pr-[10px] text-2sm font-semibold uppercase text-gray-500">Users</span>
            </div>
            <div className="menu-item">
              <div
                className="menu-link flex grow cursor-pointer items-center gap-[10px] border border-transparent py-[6px] pl-[10px] pr-[10px]"
                tabIndex={0}
              >
                <span className="menu-icon w-[20px] items-start text-gray-500 dark:text-gray-400">
                  <i className="ki-filled ki-profile-circle text-lg"></i>
                </span>
                <span className="menu-title text-sm font-semibold text-gray-700 menu-item-active:text-primary menu-link-hover:!text-primary">
                  <a
                    className="menu-link grow items-center gap-[5px] border border-transparent hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                    href={'/user/list?startNum=0&scaleNum=15'}
                    tabIndex={0}
                  >
                    User Management
                  </a>
                </span>
              </div>
            </div>
            <div className="menu-item" data-menu-item-toggle="accordion" data-menu-item-trigger="click">
              <div
                className="menu-link flex grow cursor-pointer items-center gap-[10px] border border-transparent py-[6px] pl-[10px] pr-[10px]"
                tabIndex={0}
              >
                <span className="menu-icon w-[20px] items-start text-gray-500 dark:text-gray-400">
                  <i className="ki-filled ki-setting-2 text-lg"></i>
                </span>
                <span className="menu-title text-sm font-semibold text-gray-700 menu-item-active:text-primary menu-link-hover:!text-primary">
                  My Account
                </span>
                <span className="menu-arrow ml-1 mr-[-10px] w-[20px] shrink-0 justify-end text-gray-400">
                  <i className="ki-filled ki-plus text-2xs menu-item-show:hidden"></i>
                  <i className="ki-filled ki-minus hidden text-2xs menu-item-show:inline-flex"></i>
                </span>
              </div>
              <div className="menu-accordion relative gap-0.5 pl-[10px] before:absolute before:bottom-0 before:left-[20px] before:top-0 before:border-l before:border-gray-200">
                <div className="menu-item" data-menu-item-toggle="accordion" data-menu-item-trigger="click">
                  <div
                    className="menu-link grow cursor-pointer gap-[14px] border border-transparent py-[8px] pl-[10px] pr-[10px]"
                    tabIndex={0}
                  >
                    <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                    <span className="menu-title mr-1 text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                      Account Home
                    </span>
                    <span className="menu-arrow ml-1 mr-[-10px] w-[20px] shrink-0 justify-end text-gray-400">
                      <i className="ki-filled ki-plus text-2xs menu-item-show:hidden"></i>
                      <i className="ki-filled ki-minus hidden text-2xs menu-item-show:inline-flex"></i>
                    </span>
                  </div>
                  <div className="menu-accordion relative gap-0.5 pl-[22px] before:absolute before:bottom-0 before:left-[32px] before:top-0 before:border-l before:border-gray-200">
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/home/<USER>"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Get Started
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/home/<USER>"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          User Profile
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/home/<USER>"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Company Profile
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/home/<USER>"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Settings - With Sidebar
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/home/<USER>"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Settings - Enterprise
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/home/<USER>"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Settings - Plain
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/home/<USER>"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Settings - Modal
                        </span>
                      </a>
                    </div>
                  </div>
                </div>
                <div className="menu-item" data-menu-item-toggle="accordion" data-menu-item-trigger="click">
                  <div
                    className="menu-link grow cursor-pointer gap-[14px] border border-transparent py-[8px] pl-[10px] pr-[10px]"
                    tabIndex={0}
                  >
                    <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                    <span className="menu-title mr-1 text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                      Billing
                    </span>
                    <span className="menu-arrow ml-1 mr-[-10px] w-[20px] shrink-0 justify-end text-gray-400">
                      <i className="ki-filled ki-plus text-2xs menu-item-show:hidden"></i>
                      <i className="ki-filled ki-minus hidden text-2xs menu-item-show:inline-flex"></i>
                    </span>
                  </div>
                  <div className="menu-accordion relative gap-0.5 pl-[22px] before:absolute before:bottom-0 before:left-[32px] before:top-0 before:border-l before:border-gray-200">
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/billing/basic.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Billing - Basic
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/billing/enterprise.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Billing - Enterprise
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/billing/plans.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Plans
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/billing/history.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Billing History
                        </span>
                      </a>
                    </div>
                  </div>
                </div>
                <div className="menu-item" data-menu-item-toggle="accordion" data-menu-item-trigger="click">
                  <div
                    className="menu-link grow cursor-pointer gap-[14px] border border-transparent py-[8px] pl-[10px] pr-[10px]"
                    tabIndex={0}
                  >
                    <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                    <span className="menu-title mr-1 text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                      Security
                    </span>
                    <span className="menu-arrow ml-1 mr-[-10px] w-[20px] shrink-0 justify-end text-gray-400">
                      <i className="ki-filled ki-plus text-2xs menu-item-show:hidden"></i>
                      <i className="ki-filled ki-minus hidden text-2xs menu-item-show:inline-flex"></i>
                    </span>
                  </div>
                  <div className="menu-accordion relative gap-0.5 pl-[22px] before:absolute before:bottom-0 before:left-[32px] before:top-0 before:border-l before:border-gray-200">
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/security/get-started.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Get Started
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/security/overview.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Security Overview
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/security/allowed-ip-addresses.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Allowed IP Addresses
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/security/privacy-settings.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Privacy Settings
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/security/device-management.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Device Management
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/security/backup-and-recovery.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Backup &amp; Recovery
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/security/current-sessions.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Current Sessions
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/security/security-log.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Security Log
                        </span>
                      </a>
                    </div>
                  </div>
                </div>
                <div className="menu-item" data-menu-item-toggle="accordion" data-menu-item-trigger="click">
                  <div
                    className="menu-link grow cursor-pointer gap-[14px] border border-transparent py-[8px] pl-[10px] pr-[10px]"
                    tabIndex={0}
                  >
                    <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                    <span className="menu-title mr-1 text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                      Members &amp; Roles
                    </span>
                    <span className="menu-arrow ml-1 mr-[-10px] w-[20px] shrink-0 justify-end text-gray-400">
                      <i className="ki-filled ki-plus text-2xs menu-item-show:hidden"></i>
                      <i className="ki-filled ki-minus hidden text-2xs menu-item-show:inline-flex"></i>
                    </span>
                  </div>
                  <div className="menu-accordion relative gap-0.5 pl-[22px] before:absolute before:bottom-0 before:left-[32px] before:top-0 before:border-l before:border-gray-200">
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/members/team-starter.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Teams Starter
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/members/teams.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Teams
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/members/team-info.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Team Info
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/members/members-starter.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Members Starter
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/members/team-members.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Team Members
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/members/import-members.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Import Members
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/members/roles.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Roles
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/members/permissions-toggle.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Permissions - Toggler
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/members/permissions-check.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Permissions - Check
                        </span>
                      </a>
                    </div>
                  </div>
                </div>
                <div className="menu-item">
                  <a
                    className="menu-link grow items-center gap-[14px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                    href="html/demo1/account/integrations.html"
                    tabIndex={0}
                  >
                    <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                    <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                      Integrations
                    </span>
                  </a>
                </div>
                <div className="menu-item">
                  <a
                    className="menu-link grow items-center gap-[14px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                    href="html/demo1/account/notifications.html"
                    tabIndex={0}
                  >
                    <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                    <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                      Notifications
                    </span>
                  </a>
                </div>
                <div className="menu-item">
                  <a
                    className="menu-link grow items-center gap-[14px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                    href="html/demo1/account/api-keys.html"
                    tabIndex={0}
                  >
                    <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                    <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                      API Keys
                    </span>
                  </a>
                </div>
                <div className="menu-item flex-col-reverse" data-menu-item-toggle="accordion" data-menu-item-trigger="click">
                  <div
                    className="menu-link grow cursor-pointer gap-[14px] border border-transparent py-[8px] pl-[10px] pr-[10px]"
                    tabIndex={0}
                  >
                    <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                    <span className="menu-title text-2sm font-medium text-gray-500 dark:text-gray-400">
                      <span className="hidden menu-item-show:!flex">Show less</span>
                      <span className="flex menu-item-show:hidden">Show 3 more</span>
                    </span>
                    <span className="menu-arrow ml-1 mr-[-10px] w-[20px] shrink-0 justify-end text-gray-400">
                      <i className="ki-filled ki-plus text-2xs menu-item-show:hidden"></i>
                      <i className="ki-filled ki-minus hidden text-2xs menu-item-show:inline-flex"></i>
                    </span>
                  </div>
                  <div className="menu-accordion gap-0.5">
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[14px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/appearance.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Appearance
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[14px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/invite-a-friend.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Invite a Friend
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[14px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/account/activity.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Activity
                        </span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="menu-item" data-menu-item-toggle="accordion" data-menu-item-trigger="click">
              <div
                className="menu-link flex grow cursor-pointer items-center gap-[10px] border border-transparent py-[6px] pl-[10px] pr-[10px]"
                tabIndex={0}
              >
                <span className="menu-icon w-[20px] items-start text-gray-500 dark:text-gray-400">
                  <i className="ki-filled ki-users text-lg"></i>
                </span>
                <span className="menu-title text-sm font-semibold text-gray-700 menu-item-active:text-primary menu-link-hover:!text-primary">
                  Network
                </span>
                <span className="menu-arrow ml-1 mr-[-10px] w-[20px] shrink-0 justify-end text-gray-400">
                  <i className="ki-filled ki-plus text-2xs menu-item-show:hidden"></i>
                  <i className="ki-filled ki-minus hidden text-2xs menu-item-show:inline-flex"></i>
                </span>
              </div>
              <div className="menu-accordion relative gap-0.5 pl-[10px] before:absolute before:bottom-0 before:left-[20px] before:top-0 before:border-l before:border-gray-200">
                <div className="menu-item">
                  <a
                    className="menu-link grow items-center gap-[14px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                    href="html/demo1/network/get-started.html"
                    tabIndex={0}
                  >
                    <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                    <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                      Get Started
                    </span>
                  </a>
                </div>
                <div className="menu-item" data-menu-item-toggle="accordion" data-menu-item-trigger="click">
                  <div
                    className="menu-link grow cursor-pointer gap-[14px] border border-transparent py-[8px] pl-[10px] pr-[10px]"
                    tabIndex={0}
                  >
                    <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                    <span className="menu-title mr-1 text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                      User Cards
                    </span>
                    <span className="menu-arrow ml-1 mr-[-10px] w-[20px] shrink-0 justify-end text-gray-400">
                      <i className="ki-filled ki-plus text-2xs menu-item-show:hidden"></i>
                      <i className="ki-filled ki-minus hidden text-2xs menu-item-show:inline-flex"></i>
                    </span>
                  </div>
                  <div className="menu-accordion relative gap-0.5 pl-[22px] before:absolute before:bottom-0 before:left-[32px] before:top-0 before:border-l before:border-gray-200">
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/network/user-cards/mini-cards.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Mini Cards
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/network/user-cards/team-crew.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Team Crew
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/network/user-cards/author.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Author
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/network/user-cards/nft.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          NFT
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/network/user-cards/social.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Social
                        </span>
                      </a>
                    </div>
                  </div>
                </div>
                <div className="menu-item" data-menu-item-toggle="accordion" data-menu-item-trigger="click">
                  <div
                    className="menu-link grow cursor-pointer gap-[14px] border border-transparent py-[8px] pl-[10px] pr-[10px]"
                    tabIndex={0}
                  >
                    <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                    <span className="menu-title mr-1 text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                      User Table
                    </span>
                    <span className="menu-arrow ml-1 mr-[-10px] w-[20px] shrink-0 justify-end text-gray-400">
                      <i className="ki-filled ki-plus text-2xs menu-item-show:hidden"></i>
                      <i className="ki-filled ki-minus hidden text-2xs menu-item-show:inline-flex"></i>
                    </span>
                  </div>
                  <div className="menu-accordion relative gap-0.5 pl-[22px] before:absolute before:bottom-0 before:left-[32px] before:top-0 before:border-l before:border-gray-200">
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/network/user-table/team-crew.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Team Crew
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/network/user-table/app-roster.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          App Roster
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/network/user-table/market-authors.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Market Authors
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/network/user-table/saas-users.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          SaaS Users
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/network/user-table/store-clients.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Store Clients
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/network/user-table/visitors.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Visitors
                        </span>
                      </a>
                    </div>
                  </div>
                </div>
                <div className="menu-item">
                  <div
                    className="menu-label grow items-center gap-[14px] border border-transparent py-[8px] pl-[10px] pr-[10px]"
                    tabIndex={0}
                  >
                    <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                    <span className="menu-title text-2sm font-medium text-gray-700">Cooperations</span>
                    <span className="menu-badge mr-[-10px]">
                      <span className="badge badge-xs">Soon</span>
                    </span>
                  </div>
                </div>
                <div className="menu-item">
                  <div
                    className="menu-label grow items-center gap-[14px] border border-transparent py-[8px] pl-[10px] pr-[10px]"
                    tabIndex={0}
                  >
                    <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                    <span className="menu-title text-2sm font-medium text-gray-700">Leads</span>
                    <span className="menu-badge mr-[-10px]">
                      <span className="badge badge-xs">Soon</span>
                    </span>
                  </div>
                </div>
                <div className="menu-item">
                  <div
                    className="menu-label grow items-center gap-[14px] border border-transparent py-[8px] pl-[10px] pr-[10px]"
                    tabIndex={0}
                  >
                    <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                    <span className="menu-title text-2sm font-medium text-gray-700">Donators</span>
                    <span className="menu-badge mr-[-10px]">
                      <span className="badge badge-xs">Soon</span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div className="menu-item" data-menu-item-toggle="accordion" data-menu-item-trigger="click">
              <div
                className="menu-link flex grow cursor-pointer items-center gap-[10px] border border-transparent py-[6px] pl-[10px] pr-[10px]"
                tabIndex={0}
              >
                <span className="menu-icon w-[20px] items-start text-gray-500 dark:text-gray-400">
                  <i className="ki-filled ki-security-user text-lg"></i>
                </span>
                <span className="menu-title text-sm font-semibold text-gray-700 menu-item-active:text-primary menu-link-hover:!text-primary">
                  Authentication
                </span>
                <span className="menu-arrow ml-1 mr-[-10px] w-[20px] shrink-0 justify-end text-gray-400">
                  <i className="ki-filled ki-plus text-2xs menu-item-show:hidden"></i>
                  <i className="ki-filled ki-minus hidden text-2xs menu-item-show:inline-flex"></i>
                </span>
              </div>
              <div className="menu-accordion relative gap-0.5 pl-[10px] before:absolute before:bottom-0 before:left-[20px] before:top-0 before:border-l before:border-gray-200">
                <div className="menu-item" data-menu-item-toggle="accordion" data-menu-item-trigger="click">
                  <div
                    className="menu-link grow cursor-pointer gap-[14px] border border-transparent py-[8px] pl-[10px] pr-[10px]"
                    tabIndex={0}
                  >
                    <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                    <span className="menu-title mr-1 text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                      Classic
                    </span>
                    <span className="menu-arrow ml-1 mr-[-10px] w-[20px] shrink-0 justify-end text-gray-400">
                      <i className="ki-filled ki-plus text-2xs menu-item-show:hidden"></i>
                      <i className="ki-filled ki-minus hidden text-2xs menu-item-show:inline-flex"></i>
                    </span>
                  </div>
                  <div className="menu-accordion relative gap-0.5 pl-[22px] before:absolute before:bottom-0 before:left-[32px] before:top-0 before:border-l before:border-gray-200">
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/authentication/classic/sign-in.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Sign In
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/authentication/classic/sign-up.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Sign Up
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/authentication/classic/2fa.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          2FA
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/authentication/classic/check-email.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Check Email
                        </span>
                      </a>
                    </div>
                    <div className="menu-item" data-menu-item-toggle="accordion" data-menu-item-trigger="click">
                      <div
                        className="menu-link grow cursor-pointer gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px]"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title mr-1 text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Reset Password
                        </span>
                        <span className="menu-arrow ml-1 mr-[-10px] w-[20px] shrink-0 justify-end text-gray-400">
                          <i className="ki-filled ki-plus text-2xs menu-item-show:hidden"></i>
                          <i className="ki-filled ki-minus hidden text-2xs menu-item-show:inline-flex"></i>
                        </span>
                      </div>
                      <div className="menu-accordion relative gap-0.5 pl-[22px] before:absolute before:bottom-0 before:left-[32px] before:top-0 before:border-l before:border-gray-200">
                        <div className="menu-item">
                          <a
                            className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                            href="html/demo1/authentication/classic/reset-password/enter-email.html"
                            tabIndex={0}
                          >
                            <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                            <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                              Enter Email
                            </span>
                          </a>
                        </div>
                        <div className="menu-item">
                          <a
                            className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                            href="html/demo1/authentication/classic/reset-password/check-email.html"
                            tabIndex={0}
                          >
                            <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                            <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                              Check Email
                            </span>
                          </a>
                        </div>
                        <div className="menu-item">
                          <a
                            className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                            href="html/demo1/authentication/classic/reset-password/change-password.html"
                            tabIndex={0}
                          >
                            <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                            <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                              Change Password
                            </span>
                          </a>
                        </div>
                        <div className="menu-item">
                          <a
                            className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                            href="html/demo1/authentication/classic/reset-password/password-changed.html"
                            tabIndex={0}
                          >
                            <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                            <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                              Password is Changed
                            </span>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="menu-item" data-menu-item-toggle="accordion" data-menu-item-trigger="click">
                  <div
                    className="menu-link grow cursor-pointer gap-[14px] border border-transparent py-[8px] pl-[10px] pr-[10px]"
                    tabIndex={0}
                  >
                    <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                    <span className="menu-title mr-1 text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                      Branded
                    </span>
                    <span className="menu-arrow ml-1 mr-[-10px] w-[20px] shrink-0 justify-end text-gray-400">
                      <i className="ki-filled ki-plus text-2xs menu-item-show:hidden"></i>
                      <i className="ki-filled ki-minus hidden text-2xs menu-item-show:inline-flex"></i>
                    </span>
                  </div>
                  <div className="menu-accordion relative gap-0.5 pl-[22px] before:absolute before:bottom-0 before:left-[32px] before:top-0 before:border-l before:border-gray-200">
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/authentication/branded/sign-in.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Sign In
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/authentication/branded/sign-up.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Sign Up
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/authentication/branded/2fa.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          2FA
                        </span>
                      </a>
                    </div>
                    <div className="menu-item">
                      <a
                        className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                        href="html/demo1/authentication/branded/check-email.html"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Check Email
                        </span>
                      </a>
                    </div>
                    <div className="menu-item" data-menu-item-toggle="accordion" data-menu-item-trigger="click">
                      <div
                        className="menu-link grow cursor-pointer gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px]"
                        tabIndex={0}
                      >
                        <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                        <span className="menu-title mr-1 text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                          Reset Password
                        </span>
                        <span className="menu-arrow ml-1 mr-[-10px] w-[20px] shrink-0 justify-end text-gray-400">
                          <i className="ki-filled ki-plus text-2xs menu-item-show:hidden"></i>
                          <i className="ki-filled ki-minus hidden text-2xs menu-item-show:inline-flex"></i>
                        </span>
                      </div>
                      <div className="menu-accordion relative gap-0.5 pl-[22px] before:absolute before:bottom-0 before:left-[32px] before:top-0 before:border-l before:border-gray-200">
                        <div className="menu-item">
                          <a
                            className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                            href="html/demo1/authentication/branded/reset-password/enter-email.html"
                            tabIndex={0}
                          >
                            <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                            <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                              Enter Email
                            </span>
                          </a>
                        </div>
                        <div className="menu-item">
                          <a
                            className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                            href="html/demo1/authentication/branded/reset-password/check-email.html"
                            tabIndex={0}
                          >
                            <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                            <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                              Check Email
                            </span>
                          </a>
                        </div>
                        <div className="menu-item">
                          <a
                            className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                            href="html/demo1/authentication/branded/reset-password/change-password.html"
                            tabIndex={0}
                          >
                            <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                            <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                              Change Password
                            </span>
                          </a>
                        </div>
                        <div className="menu-item">
                          <a
                            className="menu-link grow items-center gap-[5px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                            href="html/demo1/authentication/branded/reset-password/password-changed.html"
                            tabIndex={0}
                          >
                            <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                            <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                              Password is Changed
                            </span>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="menu-item">
                  <a
                    className="menu-link grow items-center gap-[14px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                    href="html/demo1/authentication/welcome-message.html"
                    tabIndex={0}
                  >
                    <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                    <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                      Welcome Message
                    </span>
                  </a>
                </div>
                <div className="menu-item">
                  <a
                    className="menu-link grow items-center gap-[14px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                    href="html/demo1/authentication/account-deactivated.html"
                    tabIndex={0}
                  >
                    <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                    <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                      Account Deactivated
                    </span>
                  </a>
                </div>
                <div className="menu-item">
                  <a
                    className="menu-link grow items-center gap-[14px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                    href="html/demo1/authentication/error-404.html"
                    tabIndex={0}
                  >
                    <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                    <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                      Error 404
                    </span>
                  </a>
                </div>
                <div className="menu-item">
                  <a
                    className="menu-link grow items-center gap-[14px] border border-transparent py-[8px] pl-[10px] pr-[10px] hover:rounded-lg hover:bg-secondary-active menu-item-active:rounded-lg menu-item-active:bg-secondary-active dark:hover:border-gray-100 dark:hover:bg-coal-300 dark:menu-item-active:border-gray-100 dark:menu-item-active:bg-coal-300"
                    href="html/demo1/authentication/error-500.html"
                    tabIndex={0}
                  >
                    <span className="menu-item-hover:before:bg-primary menu-bullet relative flex w-[6px] before:absolute before:top-0 before:size-[6px] before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full menu-item-active:before:bg-primary"></span>
                    <span className="menu-title text-2sm font-medium text-gray-700 menu-item-active:font-semibold menu-item-active:text-primary menu-link-hover:!text-primary">
                      Error 500
                    </span>
                  </a>
                </div>
              </div>
            </div>
            <div className="menu-item pb-px pt-2.25">
              <span className="menu-heading pl-[10px] pr-[10px] text-2sm font-semibold uppercase text-gray-500">Apps</span>
            </div>
            <div className="menu-item">
              <div className="menu-label gap-[10px] border border-transparent py-[6px] pl-[10px] pr-[10px]" tabIndex={0}>
                <span className="menu-icon w-[20px] items-start text-gray-500 dark:text-gray-400">
                  <i className="ki-filled ki-users text-lg"></i>
                </span>
                <span className="menu-title text-sm font-semibold text-gray-700">User Management</span>
                <span className="menu-badge mr-[-10px]">
                  <span className="badge badge-xs">Soon</span>
                </span>
              </div>
            </div>
            <div className="menu-item">
              <div className="menu-label gap-[10px] border border-transparent py-[6px] pl-[10px] pr-[10px]" tabIndex={0}>
                <span className="menu-icon w-[20px] items-start text-gray-500 dark:text-gray-400">
                  <i className="ki-filled ki-questionnaire-tablet text-lg"></i>
                </span>
                <span className="menu-title text-sm font-semibold text-gray-700">Projects</span>
                <span className="menu-badge mr-[-10px]">
                  <span className="badge badge-xs">Soon</span>
                </span>
              </div>
            </div>
            <div className="menu-item">
              <div className="menu-label gap-[10px] border border-transparent py-[6px] pl-[10px] pr-[10px]" tabIndex={0}>
                <span className="menu-icon w-[20px] items-start text-gray-500 dark:text-gray-400">
                  <i className="ki-filled ki-handcart text-lg"></i>
                </span>
                <span className="menu-title text-sm font-semibold text-gray-700">eCommerce</span>
                <span className="menu-badge mr-[-10px]">
                  <span className="badge badge-xs">Soon</span>
                </span>
              </div>
            </div>
            <div className="menu-item pb-px pt-2.25">
              <span className="menu-heading pl-[10px] pr-[10px] text-2sm font-semibold uppercase text-gray-500">Miscellaneous</span>
            </div>
            <div className="menu-item">
              <div className="menu-label gap-[10px] border border-transparent py-[6px] pl-[10px] pr-[10px]" tabIndex={0}>
                <span className="menu-icon w-[20px] items-start text-gray-500 dark:text-gray-400">
                  <i className="ki-filled ki-some-files text-lg"></i>
                </span>
                <span className="menu-title text-sm font-semibold text-gray-700">Modals</span>
                <span className="menu-badge mr-[-10px]">
                  <span className="badge badge-xs">Soon</span>
                </span>
              </div>
            </div>
            <div className="menu-item">
              <div className="menu-label gap-[10px] border border-transparent py-[6px] pl-[10px] pr-[10px]" tabIndex={0}>
                <span className="menu-icon w-[20px] items-start text-gray-500 dark:text-gray-400">
                  <i className="ki-filled ki-note-2 text-lg"></i>
                </span>
                <span className="menu-title text-sm font-semibold text-gray-700">Wizards</span>
                <span className="menu-badge mr-[-10px]">
                  <span className="badge badge-xs">Soon</span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Sidebar;
