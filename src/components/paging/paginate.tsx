'use client';

import { Paging } from '@/types/paging';
import { generateUrlSearchParams } from '@/utils';
import { usePathname, useRouter } from 'next/navigation';

import { ChangeEvent, MouseEvent, useEffect, useState } from 'react';

interface PageProps {
  data: Paging;
  requestData: any;
  isAsync?: boolean;
  onMovePage?: (value: number) => void;
}
export default function Paginate({ data, requestData, isAsync = false, onMovePage }: PageProps) {
  const router = useRouter();
  const pathName = usePathname();
  const [scaleNum, setScaleNum] = useState<number>(0);

  const [pagingData, setPagingData] = useState<Paging>(null);

  const onChangScaleNum = (e: ChangeEvent<HTMLSelectElement>) => {
    const { value } = e.currentTarget;
    requestData.startNum = 0;
    requestData.scaleNum = value;
    const queryStr = generateUrlSearchParams(requestData);
    window.location.href = `${pathName}?${queryStr}`;
  };

  useEffect(() => {
    setPagingData(data);
    setScaleNum(requestData.scaleNum);
  }, [requestData, data]);

  const pageMove = (value: number) => {
    if (isAsync) {
      onMovePage(value);
    } else {
      requestData.startNum = value;
      const queryStr = generateUrlSearchParams(requestData);
      window.location.href = `${pathName}?${queryStr}`;
    }
  };
  return (
    <>
      <div className="order-2 flex items-center gap-2 md:order-1">
        Show
        <select className="select select-sm w-16" name="perpage" value={scaleNum} onChange={onChangScaleNum}>
          <option value="10">10</option>
          <option value="15">15</option>
          <option value="20">20</option>
          <option value="30">30</option>
          <option value="50">50</option>
        </select>
        per page
      </div>
      {pagingData != null && (
        <div className="order-1 flex items-center gap-4 md:order-2">
          <span data-datatable-info="true">{`${pagingData.currentPageNum} of ${pagingData.totalPageNum}`}</span>
          <div className="pagination" data-datatable-pagination="true">
            <div className="pagination">
              <button className="btn" disabled={pagingData.isPrevPage == 'true' ? false : true} onClick={() => pageMove(0)}>
                <i className="ki-outline ki-double-left"></i>
              </button>
              <button
                className="btn"
                disabled={pagingData.isPrevPage == 'true' ? false : true}
                onClick={() => pageMove(pagingData.prevNum)}
              >
                <i className="ki-outline ki-left"></i>
              </button>
              {pagingData.pages.map((item, index) => (
                <button
                  key={`paginate_${index}`}
                  className={`${item.pageNum == pagingData.currentPageNum ? 'btn-primary' : ''} btn btn-outline`}
                  onClick={() => pageMove(item.startNum)}
                >
                  {item.pageNum}
                </button>
              ))}
              <button
                className="btn"
                disabled={pagingData.isNextPage == 'true' ? false : true}
                onClick={() => pageMove(pagingData.nextNum)}
              >
                <i className="ki-outline ki-right"></i>
              </button>
              <button
                className="btn"
                disabled={pagingData.isNextPage == 'true' ? false : true}
                onClick={() => pageMove(pagingData.lastNum)}
              >
                <i className="ki-outline ki-double-right"></i>
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
