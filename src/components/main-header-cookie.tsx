'use client';

import { signOut, useSession } from 'next-auth/react';
import { useEffect, useRef, useState } from 'react';
import { setCookie, getCookie } from 'cookies-next';
import SessionAlertModal from './modal/session-alert-modal';
import SessionModal from './modal/session-modal';
import { useRouter, usePathname } from 'next/navigation';
import emitter from '@/lib/event-emitter';
export default function MainHeaderCookie() {
  const { data: session, update } = useSession();
  const [sessionModal, setSessionModal] = useState<boolean>(false);
  // const [sessionAlertModal, setSessionAlertModal] = useState<boolean>(false);
  const [gaiSessionTime, setGaiSessionTime] = useState<number>(0);
  const expireTime = useRef<number>(0);

  const sessionAlertCheck = useRef<boolean>(false);
  const sessionAlertRef = useRef<boolean>(false);

  const [sessionAlertModal, setSessionAlertModal] = useState<boolean>(false);

  const sessionAlertModalRef = useRef(null);

  const sessionIntervalRef = useRef(null);

  const router = useRouter();
  const pathName = usePathname();

  useEffect(() => {
    emitter.on('change.session_time', onEventBus);
    const init = async () => {
      const extendTime = Date.now();
      await update({ ...session.user, loginTime: extendTime });

      const cookieValue = getCookie('GAI_SESSION_TIMEOUT') ?? null;

      // let expireTime = 0;

      if (cookieValue === null) {
        let defaultSessionTime: number = Number(process.env.NEXT_PUBLIC_SESSION_TIMEOUT ?? '7200');

        setCookie('GAI_SESSION_TIMEOUT', defaultSessionTime, {
          path: '/',
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict',
          maxAge: 60 * 60 * 24 * 30 // 30일
        });

        setGaiSessionTime(defaultSessionTime * 1000);
        expireTime.current = Date.now() + defaultSessionTime * 1000;
      } else {
        setGaiSessionTime(Number(cookieValue));
        expireTime.current = Date.now() + Number(cookieValue) * 1000;
      }
      sessionProcess();
      sessionProcessTimer();
    };
    init();
    return () => {
      emitter.off('change.session_time', onEventBus);
    };
  }, [pathName]);

  const onEventBus = (event: any) => {
    setGaiSessionTime(event.sessionTime);
    expireTime.current = Date.now() + event.sessionTime * 1000;
  };

  const onenSessionAlertModal = () => {
    sessionAlertRef.current = true;
    setSessionAlertModal(true);
  };
  const sessionProcessTimer = () => {
    if (sessionIntervalRef.current == null) {
      sessionIntervalRef.current = setInterval(() => {
        sessionProcess();
      }, 10 * 1000);
    }
  };

  const sessionProcess = () => {
    if (expireTime.current - Date.now() < 1000 * 60 * 5.1 && !sessionAlertCheck.current && !sessionAlertRef.current) {
      onenSessionAlertModal();
    } else if (Date.now() > expireTime.current) {
      if (sessionAlertRef.current) {
        sessionAlertModalRef.current?.onParentClose();
      }
      signOut({ redirect: false });
      setSessionModal(true);
      clearInterval(sessionIntervalRef.current);
    }
  };

  const onSessionHandler = (e: any) => {
    if (e.result === 'session extend success') {
      sessionAlertCheck.current = false;
      expireTime.current = e.extendTime + gaiSessionTime * 1000;
      sessionAlertModalRef.current?.onParentClose();
    } else if (e.result === 'session extend fail') {
      sessionAlertModalRef.current?.onParentClose();
    } else if (e.result === 'close') {
      sessionAlertCheck.current = true;
      sessionAlertModalRef.current?.onParentClose();
    }
  };

  const onSessionAlertModalClose = () => {
    sessionAlertRef.current = false;
    setSessionAlertModal(false);
  };

  return (
    <>
      {sessionAlertModal && (
        <SessionAlertModal
          sessionAlertModal={sessionAlertModal}
          expireTime={expireTime.current}
          ref={sessionAlertModalRef}
          onSessionHandler={(e: any) => onSessionHandler(e)}
          onCloseModal={() => onSessionAlertModalClose()}
        ></SessionAlertModal>
      )}
      {sessionModal && <SessionModal sessionModal={sessionModal}></SessionModal>}
    </>
  );
}
