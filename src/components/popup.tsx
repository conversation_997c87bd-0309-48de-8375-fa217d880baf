'use client';

import { isMobileUserAgent } from '@/utils';
import dayjs from 'dayjs';
import { MouseEvent, useEffect, useState } from 'react';

export default function PopupPage() {
  const COOKIE_NAME = 'GAI_POPUP';

  const [isMobile, setMobile] = useState<boolean>(false);
  const [width, setWidth] = useState<number>(0);

  useEffect(() => {
    const handleResize = () => {
      if (window !== undefined) {
        if (isMobileUserAgent(window.navigator.userAgent)) {
          setMobile(true);
        } else {
          if (window.innerWidth < 768 || window.innerHeight <= 640) {
            setMobile(true);
          } else {
            setMobile(false);
          }
        }

        setWidth(window.innerWidth);
      }
    };

    if (window !== undefined) {
      handleResize();

      window.addEventListener('resize', handleResize);
    }
    if (!getCookie(COOKIE_NAME)) {
      openPopup();
    }
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  /**
   * @brief 팝업 열기
   */
  const openPopup = () => {
    document.getElementById('popupOverlay').style.display = 'flex';
  };

  /**
   * @brief 팝업 닫기
   */
  const closePopup = () => {
    document.getElementById('popupOverlay').style.display = 'none';
  };

  /**
   * @brief 쿠키 저장하기
   * @param name
   * @param value
   */
  const setCookie = (name: string, value: string) => {
    const days = dayjs().endOf('day');
    const date = new Date(days.valueOf() + 6 * 60 * 60 * 1000);
    document.cookie = `${name}=${value};expires=${date.toUTCString()};path=/`;
  };

  /**
   * @brief 쿠키 가져오기
   * @param name
   * @returns
   */
  const getCookie = (name: string) => {
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.startsWith(`${name}=`)) {
        return cookie.substring(name.length + 1);
      }
    }
    return null;
  };

  /**
   * @brief 오늘 하루 열지 않기
   */
  const hideForToday = () => {
    setCookie(COOKIE_NAME, 'true'); // Set cookie to expire in 1 day
    closePopup();
  };

  /**
   * @brief 링크 열기
   * @param e
   */
  const openLink = (e: MouseEvent<HTMLButtonElement | HTMLImageElement>, siteName: string) => {
    e.preventDefault();
    if (siteName === 'deepseek') {
      window.open('https://blog.naver.com/gcube-official/223759415526', '_blank');
    }
  };

  const openKakaoChannel = (e: MouseEvent<HTMLButtonElement | HTMLImageElement>) => {
    e.preventDefault();
    window.open('https://pf.kakao.com/_jBeTn', '_blank');
  };

  return (
    <>
      <div className="popup-overlay" id="popupOverlay">
        {isMobile && (
          <div className="popup-mobile flex-col">
            <div className="flex items-center justify-between p-3">
              <div className="flex gap-4">
                <img
                  src="/da/img/popup/checkbox-normal.png"
                  className="hover:cursor-pointer"
                  style={{ width: '24px', height: '24px' }}
                  onClick={() => hideForToday()}
                  alt=""
                />
                오늘 하루 더 이상 보지 않기
              </div>
              <div className="flex">
                <img
                  src="/da/img/popup/popup_but_close.png"
                  className="hover:cursor-pointer"
                  style={{ width: '24px', height: '24px' }}
                  onClick={() => closePopup()}
                  alt=""
                />
              </div>
            </div>
            <div className="flex justify-center">
              <img
                src="/da/img/popup/popup_but_kakaochannel.png"
                className="kakaoChannelBtn"
                onClick={(e) => openKakaoChannel(e)}
                alt=""
              ></img>
            </div>
          </div>
        )}

        {!isMobile && (
          <div className="popup flex flex-col justify-between">
            <div className="flex justify-end">
              <img
                src="/da/img/popup/popup_but_close.png"
                className="hover:cursor-pointer"
                style={{ width: '24px', height: '24px' }}
                onClick={() => closePopup()}
                alt=""
              />
            </div>

            <img src="/da/img/popup/popup_all_pc.png" alt=""></img>

            <div className="mt-[-1.5rem] flex justify-center gap-5">
              <button onClick={() => closePopup()} className="btn btn-sm btn-light !text-sm">
                닫기
              </button>
              <button onClick={() => hideForToday()} className="btn btn-sm btn-light !text-sm">
                오늘 하루 더 이상 보지 않기
              </button>
            </div>
          </div>
        )}
      </div>
    </>
  );
}
