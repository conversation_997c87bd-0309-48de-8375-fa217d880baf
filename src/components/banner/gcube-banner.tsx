'use client';

import { isMobileUserAgent } from '@/utils';
import { useEffect, useState } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { usePathname } from 'next/navigation';
import { Autoplay, Navigation } from 'swiper/modules';

import 'swiper/css';
import 'swiper/css/navigation';
export default function GcubeBanner() {
  const [isMobile, setMobile] = useState<boolean>(false);
  const [isMobileBanner, setMobileBanner] = useState<boolean>(false);
  const [isVisableBanner, setVisableBanner] = useState<boolean>(false);
  const pathName = usePathname();

  useEffect(() => {
    const updateScrollWidth = () => {
      // console.log('innerWidth ', window.innerWidth);
      // console.log('scrollWidth ', document.documentElement.scrollWidth);
      // console.log('clientWidth ', document.documentElement.clientWidth);
      // console.log('offsetWidth ', document.documentElement.offsetWidth);
      // console.log(isMobileUserAgent(window.navigator.userAgent));
      if (isMobileUserAgent(window.navigator.userAgent)) {
        document.documentElement.style.setProperty('--scroll-width', `${window.innerWidth - document.documentElement.scrollWidth}px`);
      } else {
        document.documentElement.style.setProperty('--scroll-width', `${window.innerWidth - document.documentElement.clientWidth}px`);
      }
    };
    const handleResize = () => {
      updateScrollWidth();
      if (window !== undefined) {
        if (isMobileUserAgent(window.navigator.userAgent)) {
          setMobile(true);

          if (window.innerWidth < 640) {
            setMobileBanner(true);
          } else {
            setMobile(true);
            setMobileBanner(false);
          }
        } else {
          setMobile(false);
          if (window.innerWidth < 640) {
            setMobileBanner(true);
          } else {
            setMobileBanner(false);
          }
        }
      }
    };

    if (window !== undefined) {
      handleResize();
      window.addEventListener('resize', handleResize);
    }

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    if (pathName === '/ko/index' || pathName === '/ko/index') {
      setVisableBanner(true);
    } else {
      setVisableBanner(false);
    }
  }, [pathName]);
  return (
    <>
      <div
        className={`${isMobile ? '' : 'banner-container-pc'} banner-container flex flex-col ${isVisableBanner ? 'block' : 'hidden'}`}
        style={{ scrollbarGutter: 'stable' }}
      >
        <Swiper
          slidesPerView={1}
          // spaceBetween={100}
          loop={true}
          autoplay={{
            delay: 5000,
            disableOnInteraction: false
          }}
          // navigation={true}
          slidesPerGroup={1}
          watchOverflow={true}
          modules={[Autoplay, Navigation]}
          autoHeight={true}
          className="mySwiper w-full"
        >
          <SwiperSlide>
            <div className={`banner ${isMobileBanner ? 'mobile_banner_1' : 'pc_banner_1 h-[80px]'}`}>
              <img
                src="/da/img/banner/but_close_white.png"
                className="absolute right-3 top-1 w-[18px] cursor-pointer sm:w-[24px] md:right-5"
                onClick={() => {
                  setVisableBanner(false);
                }}
                alt=""
              />
              <img
                src={`/da/img/banner/banner_${isMobileBanner ? 'sm' : 'lg'}_content_1.png`}
                className={`${isMobileBanner ? 'mobile_banner_img' : 'pc_banner_img'}`}
                alt="banner 1"
              />
            </div>
          </SwiperSlide>
          <SwiperSlide>
            <div className={`banner ${isMobileBanner ? 'mobile_banner_2' : 'pc_banner_2'}`}>
              <img
                src="/da/img/banner/but_close_black.png"
                className="absolute right-3 top-1 w-[18px] cursor-pointer sm:w-[24px] md:right-5"
                onClick={() => {
                  setVisableBanner(false);
                }}
                alt=""
              />
              <img
                src={`/da/img/banner/banner_${isMobileBanner ? 'sm' : 'lg'}_content_2.png`}
                className={`${isMobileBanner ? 'mobile_banner_img' : 'pc_banner_img'}`}
                alt="banner 2"
              />
            </div>
          </SwiperSlide>
          <SwiperSlide>
            <div className={`banner ${isMobileBanner ? 'mobile_banner_1' : 'pc_banner_1 h-[80px]'}`}>
              <img
                src="/da/img/banner/but_close_white.png"
                className="absolute right-3 top-1 w-[18px] cursor-pointer sm:w-[24px] md:right-5"
                onClick={() => {
                  setVisableBanner(false);
                }}
                alt=""
              />
              <img
                src={`/da/img/banner/banner_${isMobileBanner ? 'sm' : 'lg'}_content_1.png`}
                className={`${isMobileBanner ? 'mobile_banner_img' : 'pc_banner_img'}`}
                alt="banner 1"
              />
            </div>
          </SwiperSlide>
          <SwiperSlide>
            <div className={`banner ${isMobileBanner ? 'mobile_banner_2' : 'pc_banner_2'}`}>
              <img
                src="/da/img/banner/but_close_black.png"
                className="absolute right-3 top-1 w-[18px] cursor-pointer sm:w-[24px] md:right-5"
                onClick={() => {
                  setVisableBanner(false);
                }}
                alt=""
              />
              <img
                src={`/da/img/banner/banner_${isMobileBanner ? 'sm' : 'lg'}_content_2.png`}
                className={`${isMobileBanner ? 'mobile_banner_img' : 'pc_banner_img'}`}
                alt="banner 2"
              />
            </div>
          </SwiperSlide>
        </Swiper>
      </div>
    </>
  );
}
