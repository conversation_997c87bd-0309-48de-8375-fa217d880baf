'use client';

import { useEffect, useRef, useState } from 'react';
import { usePathname } from 'next/navigation';
import { getAlertList } from '@/action/user-alert-action';
import { UserAlarm, UserAlarmResponse } from '@/types/user-alarm';
import dayjs from 'dayjs';
import NoticeAlertModal from '../modal/notice-alert-modal';
import { useLocale, useTranslations } from 'next-intl';

export default function NoticeAlertBanner() {
  const locale = useLocale();
  const t_i18n = useTranslations('i18nData');
  const pathname = usePathname();
  const noticeAlertBannerRef = useRef(null);

  const [noticeAlertModal, setNoticeAlertModal] = useState<boolean>(false);
  const [noticeAlertModalData, setNoticeAlertModalData] = useState<any>();

  const [isVisableBanner, setVisableBanner] = useState<boolean>(false);
  const [noticeAlertList, setNoticeAlertList] = useState<UserAlarm[]>([]);

  const [currentIndex, setCurrentIndex] = useState(0);
  const [expandedId, setExpandedId] = useState<number | null>(null);
  const [isContentVisible, setIsContentVisible] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(true);

  const terminalPagePattern = /^\/(ko|en)\/demand\/workload\/[^/]+\/pod\/[^/]+(\/terminal)?$/;

  const COOKIE_NOTICE = 'GCUBE_NOTICE';
  const COOKIE_POPUP = 'GCUBE_POPUP';

  // CSS 애니메이션을 위한 스타일 태그
  const expandStyle = `
    @keyframes expandDown {
      from {
        max-height: 0;
        opacity: 0;
      }
      to {
        max-height: 300px;
        opacity: 1;
      }
    }
    .animate-expand {
      animation: expandDown 0.4s ease-out forwards;
      overflow: hidden;
    }
  `;

  useEffect(() => {
    const styleElement = document.createElement('style');
    styleElement.textContent = expandStyle;
    document.head.appendChild(styleElement);

    const init = async () => {
      const response: UserAlarmResponse = await getAlertList();

      if (response.status === 200) {
        const list = response.alarms.map((item) => ({ ...item, content: item.content.replaceAll('\n', '<br/>') }));
        if (getCookie(COOKIE_NOTICE) === undefined || getCookie(COOKIE_NOTICE) === null) {
          const bannerList = list.filter((item) => item.popupYn === 'N');

          setNoticeAlertList(bannerList);
          setVisableBanner(true);
        } else {
          setVisableBanner(false);
        }
        if (getCookie(COOKIE_POPUP) === undefined || getCookie(COOKIE_POPUP) === null) {
          const popupData = list.filter((item) => item.popupYn === 'Y');

          if (popupData.length > 0) {
            setNoticeAlertModalData({
              btnColor: 'btn-primary',
              title: t_i18n('dialog_title_notice'),
              okBtn: t_i18n('but_close'),
              data: popupData[0],
              onConfirmHandler: noticeAlertModalHandler
            });
            setNoticeAlertModal(true);
          }
        }
      } else {
        setVisableBanner(false);
        setNoticeAlertList([]);
      }
    };

    init();
    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  useEffect(() => {
    if (isContentVisible) return; // content가 보일 때는 swiper 중지

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => prevIndex + 1);
    }, 5000); // 5초마다 다음 아이템으로 이동

    return () => clearInterval(interval);
  }, [isContentVisible]);

  useEffect(() => {
    // 무한 루프 처리
    if (currentIndex === noticeAlertList.length) {
      setTimeout(() => {
        setIsTransitioning(false);
        setCurrentIndex(0);
        setTimeout(() => setIsTransitioning(true), 50);
      }, 500);
    }
  }, [currentIndex, noticeAlertList.length]);

  /**
   * @brief Notice Alert Modal에서 받은 데이터 처리
   * @param e
   */
  const noticeAlertModalHandler = (e: any) => {
    if (e.result === 'hide') {
      hideForToday(COOKIE_POPUP);
    }
  };

  /**
   * @brief 쿠키 저장하기
   * @param name
   * @param value
   */
  const setCookie = (name: string, value: string) => {
    const days = dayjs().endOf('day');
    const date = new Date(days.valueOf());
    document.cookie = `${name}=${value};expires=${date.toUTCString()};path=/`;
  };

  /**
   * @brief 쿠키 가져오기
   * @param name
   * @returns
   */
  const getCookie = (name: string) => {
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.startsWith(`${name}=`)) {
        return cookie.substring(name.length + 1);
      }
    }
    return null;
  };

  /**
   * @brief 오늘 하루 열지 않기
   */
  const hideForToday = (name: string) => {
    if (name === COOKIE_NOTICE) {
      setCookie(COOKIE_NOTICE, 'true');
      setVisableBanner(false);
    } else if (name === COOKIE_POPUP) {
      setCookie(COOKIE_POPUP, 'true');
      setNoticeAlertModal(false);
    }
  };

  const handleTitleClick = (id: number) => {
    if (expandedId === id) {
      setExpandedId(null);
      setIsContentVisible(false);
    } else {
      setExpandedId(id);
      setIsContentVisible(true);
    }
  };

  const handleCloseContent = () => {
    setExpandedId(null);
    setIsContentVisible(false);
  };

  if (terminalPagePattern.test(pathname)) {
    return <></>;
  }

  return (
    <>
      {noticeAlertList.length > 0 && isVisableBanner && (
        <>
          <header
            className="relative flex w-full flex-col items-center justify-center py-2 lg:px-5"
            id="notice_header"
            ref={noticeAlertBannerRef}
          >
            <div className="mx-auto w-full max-w-[1440px] overflow-hidden rounded-lg shadow-lg">
              {/* Swiper Container - Title Only */}
              <div className="relative h-16 overflow-hidden bg-gray-50">
                <div
                  className={`${isTransitioning ? 'transition-transform duration-500 ease-in-out' : ''}`}
                  style={{
                    transform: `translateY(-${currentIndex * 64}px)`
                  }}
                >
                  {noticeAlertList.map((notice, index) => (
                    <div
                      key={notice.ser}
                      className="flex h-16 cursor-pointer items-center border-b border-gray-200 px-4 transition-colors duration-200 hover:bg-gray-100"
                      onClick={() => handleTitleClick(notice.ser)}
                    >
                      <div className="flex w-full items-center justify-between">
                        <div className="flex gap-2">
                          {notice.alarmCategory === 'admin-notice-alert' && (
                            <span className="badge badge-warning h-5 shrink-0 px-3">공지</span>
                          )}
                          <h3 className="truncate text-sm font-medium text-gray-800">{notice.title}</h3>
                        </div>
                        <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    </div>
                  ))}

                  <div
                    key={`${noticeAlertList[0].ser}-copy`}
                    className="flex h-16 cursor-pointer items-center border-b border-gray-200 px-4 transition-colors duration-200 hover:bg-gray-100"
                    onClick={() => handleTitleClick(noticeAlertList[0].ser)}
                  >
                    <div className="flex w-full items-center justify-between">
                      <div className="flex gap-2">
                        {noticeAlertList[0].alarmCategory === 'admin-notice-alert' && (
                          <span className="badge badge-warning h-5 shrink-0 px-3">공지</span>
                        )}
                        <h3 className="truncate text-sm font-medium text-gray-800">{noticeAlertList[0].title}</h3>
                      </div>
                      <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
              {/* Content Area - Shows when title is clicked */}
              {expandedId && (
                <div className="animate-expand border-t bg-white">
                  <div className="p-4">
                    {/* <div className="mb-3 flex items-center justify-between">
                      <h3 className="text-sm font-semibold text-gray-800">
                        {noticeAlertList.find((notice) => notice.ser === expandedId)?.title}
                      </h3>
                      <button onClick={handleCloseContent} className="text-gray-400 transition-colors duration-200 hover:text-gray-600">
                        <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div> */}
                    <div className="rounded-lg bg-gray-50 p-3">
                      <p
                        className="text-sm leading-relaxed text-gray-600"
                        dangerouslySetInnerHTML={{ __html: noticeAlertList.find((notice) => notice.ser === expandedId)?.content }}
                      ></p>
                    </div>
                  </div>
                </div>
              )}
            </div>
            <div className="flex w-full max-w-[1440px] items-center justify-end gap-1 whitespace-nowrap pt-2">
              <button className="btn btn-sm btn-clear !px-0" onClick={() => hideForToday(COOKIE_NOTICE)}>
                {t_i18n('but_stop_watching_today')}
              </button>
              <button className="btn-icon btn btn-xs btn-clear">
                <i className="ki-outline ki-cross" onClick={() => setVisableBanner(false)}></i>
              </button>
            </div>
          </header>
        </>
      )}
      {noticeAlertModal && (
        <NoticeAlertModal
          noticeAlertModal={noticeAlertModal}
          noticeAlertModalData={noticeAlertModalData}
          onCloseModal={() => {
            setNoticeAlertModal(false);
          }}
        ></NoticeAlertModal>
      )}
    </>
  );
}
