'use client';

import { getCategoryCodes } from '@/action/category-action';
import { getNodes } from '@/action/node-action';
import { registerQna } from '@/action/qna-action';
import { getWorkloads } from '@/action/workload-action';
import { CategoryCode } from '@/types/category-code';
import { NodeResponse } from '@/types/node';
import { Qna } from '@/types/qna';
import { WorkloadResponse } from '@/types/workload';
import { useSession } from 'next-auth/react';
import { ChangeEvent, useEffect, useState } from 'react';
import { z } from 'zod';
import Script from 'next/script';
import AlertModal from '../modal/alert-modal';
import { useLocale, useTranslations } from 'next-intl';

export default function QnaSection() {
  interface TargetValue {
    target: string | number;
    label: string;
  }

  const t_i18n = useTranslations('i18nData');
  const locale = useLocale();
  const schema = z.object({
    categoryCode: z.string().min(1, t_i18n('qna_register_msg_type_required')),
    name: z.string().min(1, t_i18n('qna_register_msg_name_required')),
    title: z.string().min(1, t_i18n('qna_register_msg_title_required')),
    content: z.string().min(1, t_i18n('qna_register_msg_content_required'))
  });
  const [errors, setErrors] = useState<QnaError>({});
  const [alertModal, setAlertModal] = useState<boolean>(false);
  const [alertModalData, setAlertModalData] = useState<any>();
  const { data: session } = useSession();
  const [categoryCodes, setCategoryCodes] = useState<CategoryCode[]>([]);
  const [qna, setQna] = useState<Qna | null>(null);
  const [targetList, setTargetList] = useState<TargetValue[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);

  const qnaInitData: Qna = {
    ser: 0,
    owner: session?.user.email ?? '',
    name: session?.user.name ?? '',
    categoryCode: '',
    title: '',
    content: '',
    targetType: null,
    target: null,
    locale: locale
  };

  /**
   * @brief validate
   * @param data
   * @param field
   * @returns
   */
  const validateForm = (data: Qna, field?: keyof QnaType): QnaError => {
    try {
      schema.parse(data);
      setErrors({});
      return field ? { [field]: null } : {};
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors = error.flatten().fieldErrors;
        field ? setErrors((prevData) => ({ ...prevData, [field]: newErrors[field] })) : setErrors(newErrors);

        return field ? { [field]: newErrors[field] || null } : newErrors;
      }
      return {};
    }
  };

  useEffect(() => {
    const init = async () => {
      setQna(qnaInitData);
      const categoryCodeRequest = { startNum: 0, scaleNum: 0, type: 'QNA', isUsed: true };
      const categoryCodeResponse = await getCategoryCodes(categoryCodeRequest);
      if (categoryCodeResponse.status == 200) {
        setCategoryCodes(categoryCodeResponse.codes);
      } else {
        setCategoryCodes([]);
      }
      setIsInitialized(true);
    };
    init();
  }, []);

  /**
   * @brief Kakao SDK 로드 완료 시 초기화
   */
  const onLoad = () => {
    window.Kakao.init('0c735d49a02594a64a8489ae1e44371e');
  };

  /**
   * @brief Kakao 채팅
   */
  const chatChannel = () => {
    if (window.Kakao.isInitialized()) {
      window.Kakao.Channel.chat({
        channelPublicId: '_jBeTn'
      });
    }
  };

  /**
   * @brief 워크로드 목록
   */
  const getWorkloadList = async () => {
    const response: WorkloadResponse = await getWorkloads({
      owner: '',
      startNum: 0,
      scaleNum: 0,
      category: '',
      target: '',
      state: '',
      startTime: '',
      endTime: '',
      sortName: 'ser',
      sortType: 'DESC',
      isDedicated: false
    });
    if (response.status == 200) {
      setTargetList(response.workloads.map((item) => ({ label: item.ser + ' - ' + item.description, target: item.ser })));
    } else {
      setTargetList([]);
    }
  };

  /**
   * @brief 노드목록
   */
  const getNodeList = async () => {
    const response: NodeResponse = await getNodes({
      startNum: 0,
      scaleNum: 0,
      owner: undefined,
      category: undefined,
      cloud: undefined,
      gpuSpec: undefined,
      hostSpec: undefined,
      state: undefined,
      startTime: undefined,
      endTime: undefined,
      sortName: undefined,
      sortType: undefined,
      isDedicated: false,
      dedicatedNmsp: undefined
    });

    if (response.status == 200) {
      console.log(response.nodes);
      setTargetList(response.nodes.map((item) => ({ label: item.name, target: item.name })));
    } else {
      setTargetList([]);
    }
  };

  useEffect(() => {
    if (qna?.targetType == 'workload') {
      getWorkloadList();
    } else if (qna?.targetType == 'node') {
      getNodeList();
    } else {
      setTargetList([]);
    }
  }, [qna?.targetType]);

  /**
   * @brief INPUT 값 변경
   * @param e
   */
  const onInputChange = (e: ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.currentTarget || e.target;
    setQna((prevData) => ({ ...prevData, [name]: value }));
  };

  /**
   * @brief QnA 등록
   */
  const onSubmit = async () => {
    const newErrors = validateForm(qna);

    if (Object.keys(newErrors).length === 0) {
      const response = await registerQna(qna);

      if (response.status == 200) {
        setAlertModalData({
          btnColor: 'btn-primary',
          title: t_i18n('dialog_title_ok'),
          content: t_i18n('qna_register_msg_success'),
          okBtn: t_i18n('but_ok')
        });

        setAlertModal(true);
        setQna(response.qan);
      } else {
        setAlertModalData({
          btnColor: 'btn-primary',
          title: t_i18n('dialog_title_waring'),
          content: t_i18n('qna_register_msg_failed'),
          okBtn: t_i18n('but_ok')
        });

        setAlertModal(true);
      }
    }
  };
  if (isInitialized)
    return (
      <>
        <div className="grid gap-5 lg:gap-5">
          <div className="card pb-7.5">
            <div className="card-body">
              <div className="flex w-full flex-wrap gap-5">
                <div className="grid w-full gap-6 pr-10 lg:w-[70%]">
                  <div className="flex items-start gap-2.5">
                    <label className="form-label w-24">{t_i18n('qna_register_msg_type')}</label>
                    <div className="flex flex-col gap-2">
                      <div className="flex gap-2">
                        {categoryCodes.map((item, index) => (
                          <label className="form-label flex items-center gap-2.5 text-nowrap" key={`category_${index}`}>
                            <input
                              type="radio"
                              className="radio radio-sm"
                              name="categoryCode"
                              value={item.code}
                              checked={item.code === qna?.categoryCode}
                              onChange={onInputChange}
                            />
                            {item.category}
                          </label>
                        ))}
                      </div>

                      {errors.categoryCode && (
                        <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">{errors.categoryCode}</span>
                      )}
                    </div>
                  </div>
                  <div className="flex items-start gap-2.5">
                    <label className="form-label w-24">{t_i18n('qna_register_msg_name')}</label>
                    <div className="flex w-32 flex-col gap-2">
                      <input type="text" className="input input-sm" name="name" value={qna?.name || ''} onChange={onInputChange} />

                      {errors.name && (
                        <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">{errors.name}</span>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2.5">
                    <label className="form-label w-24">{t_i18n('qna_register_msg_title')}</label>
                    <div className="flex grow flex-col gap-2">
                      <input type="text" className="input input-sm" name="title" value={qna?.title || ''} onChange={onInputChange} />

                      {errors.title && (
                        <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">{errors.title}</span>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2.5">
                    <label className="form-label w-24">{t_i18n('qna_register_msg_target')}</label>
                    <div className="flex gap-2">
                      <select className="select select-sm pr-10" name="targetType" onChange={onInputChange}>
                        <option value="">{t_i18n('qna_register_msg_select')}</option>
                        <option value="workload">{t_i18n('qna_register_msg_target_workload')}</option>
                        <option value="node">{t_i18n('qna_register_msg_target_node')}</option>
                      </select>
                    </div>
                    <div className="flex gap-2">
                      <select className="select select-sm pr-10" name="target" onChange={onInputChange}>
                        <option value="">{t_i18n('qna_register_msg_select')}</option>
                        {targetList.map((item, index) => (
                          <option key={index} value={item.target}>
                            {item.label}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                  <div className="flex flex-col gap-2.5">
                    <div className="flex justify-end text-xs text-gray-500">{qna?.content?.length || 0} / 5000</div>
                    <div className="flex w-full gap-2.5">
                      <label className="form-label w-24">{t_i18n('qna_register_msg_content')}</label>
                      <div className="flex grow flex-col gap-2">
                        <textarea
                          className="textarea w-full"
                          name="content"
                          rows={10}
                          value={qna?.content || ''}
                          onChange={onInputChange}
                          maxLength={5000}
                        ></textarea>

                        {errors.content && (
                          <span className="text-[13px] font-medium leading-[14px] tracking-[-1%] text-danger">{errors.content}</span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex w-full items-center justify-center gap-2.5">
                    <button className="btn btn-lg btn-primary px-10" onClick={onSubmit}>
                      {t_i18n('qna_register_msg_send_qna')}
                    </button>
                  </div>
                  <div className="grid grid-cols-2 gap-2 lg:gap-5">
                    <button className="group btn btn-light h-auto justify-center px-3 py-3 lg:px-6 lg:py-5">
                      <span className="flex flex-col items-end">
                        <span className="text-md font-medium text-gray-600">FAQ</span>
                      </span>
                    </button>

                    <button
                      data-tooltip="#gcube_chat_tooltip_content"
                      id="gcube-chat-button"
                      className="group btn btn-light h-auto justify-center px-3 py-3 lg:px-6 lg:py-5"
                      onClick={() => {
                        chatChannel();
                      }}
                    >
                      <span className="flex items-end gap-2">
                        <img src="/da/img/kakaotalk_sharing_btn_medium.png" className="w-5" alt="kakao" />
                        <span className="text-md font-medium text-gray-600">카카오 채팅</span>
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {alertModal && (
          <AlertModal
            alertModal={alertModal}
            alertModalData={alertModalData}
            onCloseModal={() => {
              setAlertModal(false);
            }}
          ></AlertModal>
        )}
        <Script
          src="https://t1.kakaocdn.net/kakao_js_sdk/2.7.4/kakao.min.js"
          integrity="sha384-DKYJZ8NLiK8MN4/C5P2dtSmLQ4KwPaoqAfyA/DfmEc1VDxu4yyC7wy6K1Hs90nka"
          crossOrigin="anonymous"
          onLoad={onLoad}
        ></Script>
      </>
    );
}
