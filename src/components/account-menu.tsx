'use client';
import { User } from '@/types/user';
import { signOut } from 'next-auth/react';
import Link from 'next/link';

import { useLocale, useTranslations } from 'next-intl';
import { FaUserCircle, FaRegHdd, FaQuestionCircle } from 'react-icons/fa';
import { usePathname } from 'next/navigation';
import path from 'path';
import { isBlank } from '@/utils';
import { FaGear } from 'react-icons/fa6';
import { useState } from 'react';
import SettingModal from './modal/setting-modal';
import { MdOutlinePayment } from 'react-icons/md';

interface PageProps {
  user: User;
}
/**
 * @brief 상단 우측 사용자 메뉴
 * @param param0
 * @returns
 */
export default function AccountMenu({ user }: PageProps) {
  const locale = useLocale();
  const t_i18n = useTranslations('i18nData');

  const pathname = usePathname();
  const pathArr = pathname.split('/');

  // const path = '/' + pathArr[1] + '/' + pathArr[2] + '/user/' + encodeURIComponent(user.email);
  const path = '/' + pathArr[1] + '/' + pathArr[2] + '/user/info';
  const pathStorage = '/' + pathArr[1] + '/' + pathArr[2] + '/user/storage/';
  const pathQna = '/' + pathArr[1] + '/' + pathArr[2] + '/qna';

  const [settingModal, setSettingModal] = useState<boolean>(false);

  /**
   * @brief 설정 Modal Open
   */
  const openSettingModal = () => {
    setSettingModal(true);
  };

  return (
    <>
      {user != null && (
        <div className="menu menu-default flex justify-center gap-1 border-none pb-2 pr-2 pt-2" data-menu="true">
          <div className="menu-item" data-menu-item-placement="bottom-end" data-menu-item-toggle="dropdown" data-menu-item-trigger="hover">
            <a className="menu-link !ml-0 !pl-0" href="#">
              <span className="font-medidum menu-title flex justify-center text-nowrap text-[12px] leading-[12px]">{user.name}</span>
              <span className="menu-arrow">
                <i className="ki-outline ki-down"></i>
              </span>
            </a>

            <div className="menu-dropdown menu-default w-full max-w-[250px] light:border-gray-300">
              <div className="flex items-center justify-between gap-1.5 px-5 py-1.5">
                <div className="flex items-center gap-2">
                  {!isBlank(user.picture) && <img alt="" className="size-9 rounded-full border-2 border-success" src={user.picture} />}
                  <div className="flex flex-col gap-1.5">
                    <span className="text-sm font-semibold leading-none text-gray-800">{user.name}</span>
                    <span className="text-xs font-medium leading-none text-gray-600 hover:text-primary">{user.email}</span>
                  </div>
                </div>
              </div>
              <div className="menu-separator !my-0"></div>
              <div className="flex flex-col" data-menu-dismiss="true">
                <div className="menu-item">
                  <Link className="menu-link" href={`${path}`}>
                    <span className="menu-icon">
                      <FaUserCircle />
                    </span>
                    <span className="menu-title"> {t_i18n('menu_profile')}</span>
                  </Link>
                </div>

                {/* 지정노드 사용자 결재내역 API 개별이후 진행예정 */}
                {user.isDedicated && (
                  <div className="menu-item">
                    {/* <Link className="menu-link" href={`/${locale}/demand/user/${user.email}/payment`}> */}
                    <Link className="menu-link" href={`/${locale}/demand/user/info/payment`}>
                      <span className="menu-icon">
                        <MdOutlinePayment />
                      </span>
                      <span className="menu-title"> {t_i18n('menu_personal_payment')}</span>
                    </Link>
                  </div>
                )}
              </div>

              {pathname.indexOf('/demand/') > -1 && (
                <>
                  <div className="menu-separator !my-0"></div>
                  <div className="flex flex-col" data-menu-dismiss="true">
                    <div className="menu-item">
                      <Link className="menu-link" href={`${pathStorage}`}>
                        <span className="menu-icon">
                          <FaRegHdd />
                        </span>
                        <span className="menu-title"> {t_i18n('menu_personal_storage')}</span>
                      </Link>
                    </div>
                  </div>
                </>
              )}
              {/* <div className="menu-separator !my-0"></div>
              <div className="flex flex-col">
                <div className="menu-item">
                  <Link className="menu-link" href={pathQna}>
                    <span className="menu-icon">
                      <FaQuestionCircle />
                    </span>
                    <span className="menu-title"> {t_i18n('menu_qna')}</span>
                  </Link>
                </div>
              </div>
              <div className="menu-separator !my-0"></div>
              <div className="flex flex-col">
                <div className="menu-item px-4 py-1.5">
                  <button
                    type="button"
                    className="btn btn-sm btn-light justify-center"
                    onClick={() => signOut({ callbackUrl: `/${locale}/index` })}
                  >
                    {t_i18n('menu_logout')}
                  </button>
                </div>
              </div>*/}
            </div>
          </div>
          {settingModal && (
            <SettingModal
              settingModal={settingModal}
              onCloseModal={() => {
                setSettingModal(false);
              }}
            ></SettingModal>
          )}
        </div>
      )}
    </>
  );
}
