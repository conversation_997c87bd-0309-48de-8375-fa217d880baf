'use client';
import { usePathname } from 'next/navigation';
import { useLocale, useTranslations } from 'next-intl';
import Link from 'next/link';
import { User } from '@/types/user';

interface PageProps {
  user: User;
}

/**
 * @brief 메인 메뉴 컴포넌트
 * @returns
 */
export default function MainMenu({ user }: PageProps) {
  const pathname = usePathname();

  const locale = useLocale();
  const t_i18n = useTranslations('i18nData');

  if (pathname.indexOf('/provision') > -1) {
    return (
      <>
        <div className="flex shrink-0 flex-col justify-center gap-6">
          <div className="grid">
            <div className="menu gap-3" data-menu="true">
              <div
                className={`menu-item border-b-2 border-b-transparent menu-item-active:border-b-primary menu-item-here:border-b-primary`}
              >
                <Link
                  locale={locale}
                  className="menu-link gap-1.5"
                  target="_blank"
                  href={`https://data-alliance.github.io/gai-platform-docs/`}
                  tabIndex={0}
                >
                  <span className="text-nowrap px-3 py-1.5 text-[16px] font-medium leading-[16px] text-gray-900 menu-item-active:text-primary menu-item-here:text-primary menu-item-show:text-primary menu-link-hover:text-primary lg:px-3 lg:py-3">
                    {t_i18n('menu_docs')}
                  </span>
                </Link>
              </div>
              <div
                className={` ${pathname.startsWith(`/${locale}/provision/pricing`, 0) ? 'active' : ''} menu-item border-b-2 border-b-transparent menu-item-active:border-b-primary menu-item-here:border-b-primary`}
              >
                <Link locale={locale} className="menu-link gap-1.5" href={`/${locale}/provision/pricing`}>
                  <span className="text-nowrap px-3 py-1.5 text-[16px] font-medium leading-[16px] text-gray-900 menu-item-active:text-primary menu-item-here:text-primary menu-item-show:text-primary menu-link-hover:text-primary lg:px-6 lg:py-3">
                    {t_i18n('menu_pricing')}
                  </span>
                </Link>
              </div>
              <div
                className={` ${pathname.startsWith(`/${locale}/provision/dashboard`, 0) ? 'active' : ''} menu-item border-b-2 border-b-transparent menu-item-active:border-b-primary menu-item-here:border-b-primary`}
              >
                <Link locale={locale} className="menu-link gap-1.5" href={`/${locale}/provision/dashboard`}>
                  <span className="text-nowrap px-3 py-1.5 text-[16px] font-medium leading-[16px] text-gray-900 menu-item-active:text-primary menu-item-here:text-primary menu-item-show:text-primary menu-link-hover:text-primary lg:px-6 lg:py-3">
                    {t_i18n('menu_dashboard')}
                  </span>
                </Link>
              </div>
              <div
                className={` ${pathname.startsWith(`/${locale}/provision/point`, 0) ? 'active' : ''} menu-item border-b-2 border-b-transparent menu-item-active:border-b-primary menu-item-here:border-b-primary`}
              >
                <Link locale={locale} className="menu-link gap-1.5" href={`/${locale}/provision/point`}>
                  <span className="text-nowrap px-3 py-1.5 text-[16px] font-medium leading-[16px] text-gray-900 menu-item-active:text-primary menu-item-here:text-primary menu-item-show:text-primary menu-link-hover:text-primary lg:px-6 lg:py-3">
                    {t_i18n('menu_point')}
                  </span>
                </Link>
              </div>
              <div
                className={` ${pathname.startsWith(`/${locale}/provision/node`, 0) ? 'active' : ''} menu-item border-b-2 border-b-transparent menu-item-active:border-b-primary menu-item-here:border-b-primary`}
              >
                <Link locale={locale} className="menu-link gap-1.5" href={`/${locale}/provision/node/list`}>
                  <span className="text-nowrap px-3 py-1.5 text-[16px] font-medium leading-[16px] text-gray-900 menu-item-active:text-primary menu-item-here:text-primary menu-item-show:text-primary menu-link-hover:text-primary lg:px-6 lg:py-3">
                    {t_i18n('menu_nodes')}
                  </span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  } else if (pathname.indexOf('/demand') > -1) {
    return (
      <>
        <div className="flex shrink-0 flex-col justify-center gap-6">
          <div className="grid">
            <div className="menu gap-1.5" data-menu="true">
              <div
                className={`menu-item border-b-2 border-b-transparent menu-item-active:border-b-primary menu-item-here:border-b-primary`}
              >
                <Link
                  locale={locale}
                  className="menu-link gap-1.5"
                  target="_blank"
                  href={`https://data-alliance.github.io/gai-platform-docs/`}
                  tabIndex={0}
                >
                  <span className="text-nowrap px-3 py-1.5 text-[16px] font-medium leading-[16px] text-gray-900 menu-item-active:text-primary menu-item-here:text-primary menu-item-show:text-primary menu-link-hover:text-primary lg:px-3 lg:py-3">
                    {t_i18n('menu_docs')}
                  </span>
                </Link>
              </div>
              <div
                className={` ${pathname.startsWith(`/${locale}/demand/pricing`, 0) ? 'active' : ''} menu-item border-b-2 border-b-transparent menu-item-active:border-b-primary menu-item-here:border-b-primary`}
              >
                <Link locale={locale} className="menu-link gap-1.5" href={`/${locale}/demand/pricing`}>
                  <span className="text-nowrap px-3 py-1.5 text-[16px] font-medium leading-[16px] text-gray-900 menu-item-active:text-primary menu-item-here:text-primary menu-item-show:text-primary menu-link-hover:text-primary lg:px-3 lg:py-3">
                    {t_i18n('menu_pricing')}
                  </span>
                </Link>
              </div>
              <div
                className={` ${pathname.startsWith(`/${locale}/demand/dashboard`, 0) ? 'active' : ''} menu-item border-b-2 border-b-transparent menu-item-active:border-b-primary menu-item-here:border-b-primary`}
              >
                <Link locale={locale} className="menu-link gap-1.5" href={`/${locale}/demand/dashboard`}>
                  <span className="text-nowrap px-3 py-1.5 text-[16px] font-medium leading-[16px] text-gray-900 menu-item-active:text-primary menu-item-here:text-primary menu-item-show:text-primary menu-link-hover:text-primary lg:px-3 lg:py-3">
                    {t_i18n('menu_dashboard')}
                  </span>
                </Link>
              </div>
              {!user.isDedicated && (
                <>
                  <div
                    className={` ${pathname == `/${locale}/demand/point` || pathname.startsWith(`/${locale}/demand/point/history`, 0) ? 'active' : ''} menu-item border-b-2 border-b-transparent menu-item-active:border-b-primary menu-item-here:border-b-primary`}
                  >
                    <Link locale={locale} className="menu-link gap-1.5" href={`/${locale}/demand/point`}>
                      <span className="text-nowrap px-3 py-1.5 text-[16px] font-medium leading-[16px] text-gray-900 menu-item-active:text-primary menu-item-here:text-primary menu-item-show:text-primary menu-link-hover:text-primary lg:px-3 lg:py-3">
                        {t_i18n('menu_point')}
                      </span>
                    </Link>
                  </div>

                  <div
                    className={` ${pathname.startsWith(`/${locale}/demand/point/charge`, 0) ? 'active' : ''} menu-item border-b-2 border-b-transparent menu-item-active:border-b-primary menu-item-here:border-b-primary`}
                  >
                    <Link locale={locale} className="menu-link gap-1.5" href={`/${locale}/demand/point/charge`}>
                      <span className="text-nowrap px-3 py-1.5 text-[16px] font-medium leading-[16px] text-gray-900 menu-item-active:text-primary menu-item-here:text-primary menu-item-show:text-primary menu-link-hover:text-primary lg:px-3 lg:py-3">
                        {t_i18n('menu_point_charge')}
                      </span>
                    </Link>
                  </div>
                </>
              )}
              <div
                className={` ${pathname.startsWith(`/${locale}/demand/workload`, 0) ? 'active' : ''} menu-item border-b-2 border-b-transparent menu-item-active:border-b-primary menu-item-here:border-b-primary`}
              >
                <Link locale={locale} className="menu-link gap-1.5" href={`/${locale}/demand/workload/list`}>
                  <span className="text-nowrap px-3 py-1.5 text-[16px] font-medium leading-[16px] text-gray-900 menu-item-active:text-primary menu-item-here:text-primary menu-item-show:text-primary menu-link-hover:text-primary lg:px-6 lg:py-3">
                    {t_i18n('menu_workloads')}
                  </span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  } else {
    return <></>;
  }
}
