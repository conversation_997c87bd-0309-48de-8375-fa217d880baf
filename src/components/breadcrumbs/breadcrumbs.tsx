'use client';

import { useEffect, useState } from 'react';
import { useTheme } from 'next-themes';
import Link from 'next/link';
import { useLocale } from 'next-intl';
/**
 * @brief Header에 GCUBE 아이콘
 * @returns
 */
export default function Breadcrumbs() {
  const locale = useLocale();
  const { theme, setTheme } = useTheme();

  useEffect(() => {}, [theme]);

  return (
    <>
      <style jsx>{`
        .title_sub {
          font-size: 1.3em;
          font-family: inherit;
          font-weight: 600;
          flex: 1;
          display: inline-block;
          color: #858796;
        }
        .title {
          font-size: 1.4em;
          font-weight: 600;
          color: #000000;
          display: inline-block;
        }
      `}</style>
      <div className="flex shrink-0 items-center gap-1 lg:w-[180px]">
        <Link href={`/${locale}/index`}>
          <img alt="" className="inline-block pl-[10px] dark:hidden" src={`/da/img/logo-gcube-light.svg`} />
          <img alt="" className="hidden pl-[10px] dark:inline-block" src={`/da/img/logo-gcube-dark.svg`} />
        </Link>
      </div>
    </>
  );
}
