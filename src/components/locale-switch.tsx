'use client';
import { useLocale, useTranslations } from 'next-intl';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';

import { useState } from 'react';

interface PageProps {
  isPoint: boolean;
}

export default function LocaleSwitch({ isPoint }: PageProps) {
  const pathname = usePathname();
  const router = useRouter();
  const locale = useLocale();
  const t_i18n = useTranslations('i18nData');

  const newPathname = pathname.replace('/' + locale, '');

  // const [localeValue, setLocaleValue] = useState<string>(locale);

  return (
    <>
      {/* <div className="flex items-center gap-2"> */}
      <div className="menu menu-default flex justify-center gap-1 border-none" data-menu="true">
        {isPoint && (
          <div className="menu-item">
            <a className="menu-link" href="#">
              <span className="menu-icon">
                <i className="ki-outline ki-badge"></i>
              </span>
              <span className="menu-title">point</span>
            </a>
          </div>
        )}
        <div className="menu-item" data-menu-item-placement="bottom-start" data-menu-item-toggle="dropdown" data-menu-item-trigger="hover">
          <a className="menu-link" href="#">
            {/* <span className="menu-icon">
                <i className="ki-outline ki-profile-circle"></i>
              </span> */}
            <span className="font-medidum menu-title flex justify-center text-[12px] leading-[12px]">{locale == 'ko' ? 'KOR' : 'ENG'}</span>
            <span className="menu-arrow">
              <i className="ki-outline ki-down"></i>
            </span>
          </a>
          <div className="menu-dropdown py-2">
            <div className="menu-item">
              <button className="menu-link" onClick={() => (window.location.href = `/ko${newPathname}`)}>
                <span className="font-medidum menu-title flex justify-center text-[12px] leading-[12px]">KOR</span>
              </button>
            </div>
            <div className="menu-item">
              <button className="menu-link" onClick={() => (window.location.href = `/en${newPathname}`)}>
                <span className="font-medidum menu-title flex justify-center text-[12px] leading-[12px]">ENG</span>
              </button>
            </div>
          </div>
        </div>
      </div>
      {/* </div> */}
    </>
  );
}
