import { cloludProvidersData } from '@/constants/cloud-provider-data';
import { comma } from '@/utils';

import { getLocale, getTranslations } from 'next-intl/server';

interface PageProps {
  providerName: string;
  items: GpuPricing[];
  krw: number;
}

/**
 * @brief Pricing 테이블 컴포넌트
 * @param param
 * @returns
 */
export async function PriceTable({ providerName, items, krw }: PageProps) {
  const t_i18n = await getTranslations('i18nData');
  const locale = await getLocale();

  const providerList = cloludProvidersData.filter((item) => item.cloudAlias == providerName);
  const provider = providerList[0];

  const convertPriceUnit = (item: GpuPricing) => {
    if (item.cpu == 0 && item.memory == 0) {
      return t_i18n('pricing_onrequest');
    } else {
      // if (locale == 'en') {
      //   if (item.currencyUnit === 'USD') {
      //     return '$ ' + item.price;
      //   } else {
      //     return '$ ' + (item.price / krw).toFixed(4);
      //   }
      // } else {
      if (item.currencyUnit === 'USD') {
        return '₩ ' + comma(Math.round(item.price * krw));
      } else {
        return '₩ ' + comma(item.price);
      }
      // }
    }
  };

  if (provider == null) {
    <div className="grid h-[350px]"></div>;
  } else {
    return (
      <>
        <div className="grid">
          <div className="card card-grid min-w-full" id={`${provider.cloudAlias}`}>
            <div className="card-header !px-[30px] !py-[20px]">
              <h3 className="card-title flex items-center justify-center gap-2">
                <img alt="" className="h-11 shrink-0" src={provider.cloudImg} style={{ height: '24px' }} />
                <div className="flex">{provider.cloudName}</div>
              </h3>
              <div className="btn-icon btn btn-sm btn-clear btn-light">
                <a target="_blank" href={provider.cloudLink}>
                  {/* <i className="ki-filled ki-exit-right-corner" data-tooltip={`#${provider.cloudAlias}_tooltip`}></i> */}
                  <i className="ki-filled ki-arrow-right" data-tooltip={`#${provider.cloudAlias}_tooltip`}></i>
                  <div className="tooltip" id={`${provider.cloudAlias}_tooltip`}>
                    {t_i18n('but_more_gpu')}
                  </div>
                </a>
              </div>
            </div>
            <div className="card-body">
              <div className="min-w-full">
                <div className="scrollable-x-auto">
                  <table className="table">
                    <thead>
                      <tr className="text-nowrap text-left">
                        <th className="text-left">{t_i18n('table_th_gpu_model')}</th>
                        <th>VRAM</th>
                        <th>vCPU</th>
                        <th>STORAGE</th>
                        <th>RAM</th>
                        <th>{t_i18n('table_th_price')}</th>
                      </tr>
                    </thead>
                    <tbody>
                      {items.length > 0 &&
                        items.map((item, index) => (
                          <tr key={`${provider.cloudAlias}_${index}`} className="text-nowrap text-left text-[14px] leading-[14px]">
                            <td className="text-gray-900">{item.productName}</td>
                            <td>{item.vram}GB</td>
                            <td>{item.cpu == 0 ? '--' : item.cpu}</td>
                            <td>{item.disk == 0 ? '--' : item.disk + 'GB'}</td>
                            <td>{item.memory == 0 ? '--' : item.memory + 'GB'}</td>
                            <td>{convertPriceUnit(item)}</td>
                          </tr>
                        ))}
                      {items.length == 0 && (
                        <tr>
                          <td colSpan={6}>No Data</td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }
}
