import { cloludProvidersData } from '@/constants/cloud-provider-data';
import Link from 'next/link';
import { getLocale, getTranslations } from 'next-intl/server';
interface PageProps {
  providerName: string;
  gpuPricing?: GpuPricing;
}

/**
 * @brief GPUs Pricing 카드 컴포넌트
 * @param param GPUs Pricing에서 전달하는 Cloud provider Data
 * @returns
 */
export async function ProviderCard({ gpuPricing, providerName }: PageProps) {
  const t_i18n = await getTranslations('i18nData');

  const providerList = cloludProvidersData.filter((item) => item.cloudAlias == providerName);
  const provider = providerList[0];

  if (provider != null)
    return (
      <>
        <div className="card">
          <div className="card-body p-5 lg:p-7.5">
            <div className="mb-3 flex items-center justify-between lg:mb-5">
              <div className="flex items-center justify-center">
                <img alt="" className="h-11 shrink-0" src={provider?.cloudImg} />
              </div>
              <div className="btn-icon btn btn-sm btn-clear btn-light">
                <Link target="_blank" href={provider.cloudLink}>
                  <i className="ki-filled ki-exit-right-corner" data-tooltip={`#${provider?.cloudAlias}_card_tooltip`}>
                    <div className="tooltip" id={`#${provider?.cloudAlias}_card_tooltip`}>
                      {t_i18n('but_more_gpu')}
                    </div>
                  </i>
                </Link>
              </div>
            </div>
            <div className="flex flex-col gap-1 lg:gap-2.5">
              <Link className="text-base font-semibold text-gray-900 hover:text-primary-active" href={`#${provider?.cloudAlias}`}>
                {provider?.cloudName}
              </Link>
            </div>
          </div>
        </div>
      </>
    );
}
