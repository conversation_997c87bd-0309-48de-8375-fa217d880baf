import { cloludProvidersData } from '@/constants/cloud-provider-data';

import { comma } from '@/utils';

import { getLocale, getTranslations } from 'next-intl/server';

interface PageProps {
  providerName: string;
  items: GpuPricing[];
  krw: number;
}

/**
 * @brief GPUs Pricing 테이블 컴포넌트
 * @param param
 * @returns
 */
export async function GcubePriceTable({ providerName, items, krw }: PageProps) {
  const t_i18n = await getTranslations('i18nData');
  const locale = await getLocale();

  const providerList = cloludProvidersData.filter((item) => item.cloudAlias == providerName);
  const provider = providerList[0];

  const tier1 = items.filter((item) => item.productCode == 'tier1');
  const tier2 = items.filter((item) => item.productCode == 'tier2');
  const tier3 = items.filter((item) => item.productCode == 'tier3');

  /**
   * @brief Price 단위 변환
   * @param item
   * @returns
   */
  const convertPriceUnit = (item: GpuPricing) => {
    let priceValue = '';
    let usagePriceValue = '';
    let hourPriceValue = '';

    if (locale == 'en') {
      let price = 0;
      let usagePrice = 0;
      if (item.currencyUnit === 'USD') {
        price = item.price;
        usagePrice = item.usagePrice;
      } else {
        price = item.price / krw;
        usagePrice = item.usagePrice / krw;
      }

      priceValue = '$ ' + price.toFixed(4);
      usagePriceValue = '$ ' + usagePrice.toFixed(4);
      hourPriceValue = '$ ' + (price + usagePrice).toFixed(4);
    } else {
      let price = 0;
      let usagePrice = 0;
      if (item.currencyUnit === 'USD') {
        price = Math.round(item.price * krw);
        usagePrice = Math.round(item.usagePrice * krw);
      } else {
        price = item.price;
        usagePrice = item.usagePrice;
      }

      priceValue = '₩ ' + comma(price);
      usagePriceValue = ' ~ ₩ ' + comma(usagePrice);
      hourPriceValue = '₩ ' + comma(parseFloat((price + usagePrice).toFixed(2)));
    }

    return (
      <>
        <td>{priceValue}</td>
        {/* <td>{usagePriceValue}</td> */}
        <td>{hourPriceValue}</td>
      </>
    );
  };

  if (provider != null)
    return (
      <>
        <div className="grid">
          <div className="card card-grid min-w-full" id={`${provider.cloudAlias}_tier1`}>
            <div className="card-header !px-[30px] !py-[20px]">
              <h3 className="card-title flex items-center justify-center gap-[14px]">
                <div className="flex items-center gap-2">
                  <img alt="" className="h-11 shrink-0" src={provider.cloudImg} style={{ height: '24px' }} />
                  <span className="text-[16px] font-medium leading-[16px] text-gray-800">{provider.cloudName}</span>
                </div>
                <span className="text-[16px] font-medium leading-[16px] text-gray-600">Tier 1</span>
              </h3>
              <h3 className="text-[16px] font-medium leading-[16px] text-gray-600">{t_i18n('pricing_msg12')}</h3>
              {/* <div className="btn-icon btn btn-sm btn-clear btn-light">
                <a target="_blank" href={provider.cloudLink}>
                  <i className="ki-filled ki-exit-right-corner" data-tooltip={`#${provider.cloudAlias}_tier1_tooltip`}></i>
                  <div className="tooltip" id={`${provider.cloudAlias}_tier1_tooltip`}>
                    {provider.cloudName}
                  </div>
                </a>
              </div> */}
            </div>
            <div className="card-body">
              <div className="min-w-full">
                <div className="scrollable-auto">
                  <table className="table" data-datatable-table="true">
                    <thead>
                      <tr className="text-nowrap text-left">
                        <th className="!border-b-1 w-[345px] !border-b-[#F1F1F4] !bg-[#FCFCFC] text-left !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          {t_i18n('table_th_gpu_model')}
                        </th>
                        <th className="!border-b-1 w-[110px] !border-b-[#F1F1F4] !bg-[#FCFCFC] !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          GPU 개수
                        </th>
                        <th className="!border-b-1 w-[110px] !border-b-[#F1F1F4] !bg-[#FCFCFC] !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          vRAM
                        </th>
                        <th className="!border-b-1 w-[110px] !border-b-[#F1F1F4] !bg-[#FCFCFC] !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          vCPUs
                        </th>
                        <th className="!border-b-1 w-[110px] !border-b-[#F1F1F4] !bg-[#FCFCFC] !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          STORAGE
                        </th>
                        <th className="!border-b-1 w-[110px] !border-b-[#F1F1F4] !bg-[#FCFCFC] !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          RAM
                        </th>
                        <th className="!border-b-1 w-[110px] !border-b-[#F1F1F4] !bg-[#FCFCFC] !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          {t_i18n('pricing_table_th01')}
                        </th>

                        {/* <th className="!border-b-1 !border-b-[#F1F1F4] !bg-[#FCFCFC] !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          {t_i18n('table_th_price_usage')}
                        </th> */}

                        <th className="!border-b-1 !border-b-[#F1F1F4] !bg-[#FCFCFC] !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          {t_i18n('pricing_table_th06')}
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {tier1.length > 0 &&
                        tier1.map((item, index) => (
                          <tr key={`${provider.cloudAlias}_tier1_${index}`} className="text-nowrap text-left">
                            <td className="text-left">{item.gpuName}</td>
                            <td className="">{'X ' + item.gpu}</td>
                            <td className="">{item.vram}GB</td>
                            <td className="">{item.cpu == 0 ? '--' : item.cpu}</td>
                            <td className="">{item.disk == 0 ? '--' : item.disk + item.capUnit}</td>
                            <td className="">{item.memory == 0 ? '--' : item.memory + item.capUnit}</td>
                            {convertPriceUnit(item)}
                          </tr>
                        ))}
                      {tier1.length == 0 && (
                        <tr>
                          <td colSpan={6}>{t_i18n('msg_no_content')}</td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="grid">
          <div className="card card-grid min-w-full" id={`${provider.cloudAlias}_tier2`}>
            <div className="card-header !px-[30px] !py-[20px]">
              <h3 className="card-title flex items-center justify-center gap-[14px]">
                <div className="flex items-center gap-2">
                  <img alt="" className="h-11 shrink-0" src={provider.cloudImg} style={{ height: '24px' }} />
                  <span className="text-[16px] font-medium leading-[16px] text-gray-800">{provider.cloudName}</span>
                </div>
                <span className="text-[16px] font-medium leading-[16px] text-gray-600">Tier 2</span>
              </h3>
              <h3 className="text-[16px] font-medium leading-[16px] text-gray-600">{t_i18n('pricing_msg13')}</h3>
            </div>
            <div className="card-body">
              <div className="min-w-full">
                <div className="scrollable-auto">
                  <table className="table" data-datatable-table="true">
                    <thead>
                      <tr className="text-nowrap text-center">
                        <th className="!border-b-1 w-[345px] !border-b-[#F1F1F4] !bg-[#FCFCFC] text-left !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          {t_i18n('table_th_gpu_model')}
                        </th>
                        <th className="!border-b-1 w-[110px] !border-b-[#F1F1F4] !bg-[#FCFCFC] !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          GPU 개수
                        </th>
                        <th className="!border-b-1 w-[110px] !border-b-[#F1F1F4] !bg-[#FCFCFC] !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          vRAM
                        </th>
                        <th className="!border-b-1 w-[110px] !border-b-[#F1F1F4] !bg-[#FCFCFC] !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          {t_i18n('pricing_table_min') + ' vCPUs'}
                        </th>
                        <th className="!border-b-1 w-[110px] !border-b-[#F1F1F4] !bg-[#FCFCFC] !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          {t_i18n('pricing_table_min') + ' STORAGE'}
                        </th>
                        <th className="!border-b-1 w-[110px] !border-b-[#F1F1F4] !bg-[#FCFCFC] !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          {t_i18n('pricing_table_min') + ' RAM'}
                        </th>
                        <th className="!border-b-1 w-[110px] !border-b-[#F1F1F4] !bg-[#FCFCFC] !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          {t_i18n('pricing_table_th01')}
                        </th>

                        {/* <th className="!border-b-1 !border-b-[#F1F1F4] !bg-[#FCFCFC] !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          {t_i18n('table_th_price_usage')}
                        </th> */}

                        <th className="!border-b-1 !border-b-[#F1F1F4] !bg-[#FCFCFC] !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          {t_i18n('pricing_table_th06')}
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {tier2.length > 0 &&
                        tier2.map((item, index) => (
                          <tr key={`${provider.cloudAlias}_tier1_${index}`} className="text-nowrap text-left">
                            <td className="text-left">{item.productName}</td>
                            <td className="">{'X ' + item.gpu}</td>
                            <td className="">{item.vram}GB</td>
                            <td className="">{item.cpu == 0 ? '--' : item.cpu}</td>
                            <td className="">{item.disk == 0 ? '--' : item.disk + 'GB'}</td>
                            <td className="">{item.memory == 0 ? '--' : item.memory + 'GB'}</td>
                            {convertPriceUnit(item)}
                          </tr>
                        ))}
                      {tier2.length == 0 && (
                        <tr>
                          <td colSpan={6}>{t_i18n('msg_no_content')}</td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="grid">
          <div className="card card-grid min-w-full" id={`${provider.cloudAlias}_tier3`}>
            <div className="card-header !px-[30px] !py-[20px]">
              <h3 className="card-title flex items-center justify-center gap-[14px]">
                <div className="flex items-center gap-2">
                  <img alt="" className="h-11 shrink-0" src={provider.cloudImg} style={{ height: '24px' }} />
                  <span className="text-[16px] font-medium leading-[16px] text-gray-800">{provider.cloudName}</span>
                </div>
                <span className="text-[16px] font-medium leading-[16px] text-gray-600">Tier 3</span>
              </h3>
              <h3 className="text-[16px] font-medium leading-[16px] text-gray-600">{t_i18n('pricing_msg14')}</h3>
            </div>
            <div className="card-body">
              <div className="min-w-full">
                <div className="scrollable-x-auto">
                  <table className="table" data-datatable-table="true">
                    <thead>
                      <tr className="text-nowrap text-center">
                        <th className="!border-b-1 w-[345px] !border-b-[#F1F1F4] !bg-[#FCFCFC] text-left !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          {t_i18n('table_th_gpu_model')}
                        </th>
                        <th className="!border-b-1 w-[110px] !border-b-[#F1F1F4] !bg-[#FCFCFC] !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          GPU 개수
                        </th>
                        <th className="!border-b-1 w-[110px] !border-b-[#F1F1F4] !bg-[#FCFCFC] !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          vRAM
                        </th>
                        <th className="!border-b-1 w-[110px] !border-b-[#F1F1F4] !bg-[#FCFCFC] !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          {t_i18n('pricing_table_min') + ' vCPUs'}
                        </th>
                        <th className="!border-b-1 w-[110px] !border-b-[#F1F1F4] !bg-[#FCFCFC] !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          {t_i18n('pricing_table_min') + ' STORAGE'}
                        </th>
                        <th className="!border-b-1 w-[110px] !border-b-[#F1F1F4] !bg-[#FCFCFC] !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          {t_i18n('pricing_table_min') + ' RAM'}
                        </th>
                        <th className="!border-b-1 w-[110px] !border-b-[#F1F1F4] !bg-[#FCFCFC] !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          {t_i18n('pricing_table_th01')}
                        </th>
                        {/* 
                        <th className="!border-b-1 !border-b-[#F1F1F4] !bg-[#FCFCFC] !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          {t_i18n('table_th_price_usage')}
                        </th> */}

                        <th className="!border-b-1 !border-b-[#F1F1F4] !bg-[#FCFCFC] !text-[13px] !font-medium !leading-[14px] !tracking-[-1%] !text-gray-600">
                          {t_i18n('pricing_table_th06')}
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {tier3.length > 0 &&
                        tier3.map((item, index) => (
                          <tr key={`${provider.cloudAlias}_tier1_${index}`} className="text-nowrap text-left">
                            <td className="text-left">{item.productName}</td>
                            <td className="">{'X ' + item.gpu}</td>
                            <td className="">{item.vram}GB</td>
                            <td className="">{item.cpu == 0 ? '--' : item.cpu}</td>
                            <td className="">{item.disk == 0 ? '--' : item.disk + 'GB'}</td>
                            <td className="">{item.memory == 0 ? '--' : item.memory + 'GB'}</td>
                            {convertPriceUnit(item)}
                          </tr>
                        ))}
                      {tier3.length == 0 && (
                        <tr>
                          <td colSpan={6}>{t_i18n('msg_no_content')}</td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
}
