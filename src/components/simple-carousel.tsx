'use client';

import React, { ReactNode, useState } from 'react';
import { useSwipeable } from 'react-swipeable';

interface SimpleCarouselProps {
  children: ReactNode;
}

const SimpleCarousel: React.FC<SimpleCarouselProps> = ({ children }) => {
  const slides = React.Children.toArray(children);
  const [currentIndex, setCurrentIndex] = useState(0);

  const goToNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex === slides.length - 1 ? 0 : prevIndex + 1));
  };

  const goToPrev = () => {
    setCurrentIndex((prevIndex) => (prevIndex === 0 ? slides.length - 1 : prevIndex - 1));
  };

  const swipedHandlers = useSwipeable({
    onSwipedLeft: () => goToNext(),
    onSwipedRight: () => goToPrev(),
    trackMouse: true
  });

  return (
    <div className="w-full">
      <div className="relative w-full">
        <div className="invisible">{slides[currentIndex]}</div>
        <div {...swipedHandlers} className="absolute inset-0">
          {slides.map((child, index) => (
            <div
              key={index}
              className={`absolute inset-0 transition-opacity duration-700 ease-in-out ${
                index === currentIndex ? 'opacity-100' : 'opacity-0'
              }`}
            >
              {child}
            </div>
          ))}
        </div>
      </div>
      <div className="mt-4 flex justify-center space-x-2">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentIndex(index)}
            className={`z-50 focus:outline-none ${index === currentIndex ? 'text-black' : 'text-gray-400'}`}
            aria-label={`Go to slide ${index + 1}`}
          >
            {index === currentIndex ? '●' : '○'}
          </button>
        ))}
      </div>
    </div>
  );
};

export default SimpleCarousel;
