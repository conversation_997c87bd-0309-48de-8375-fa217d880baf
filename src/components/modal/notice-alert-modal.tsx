'use client';
import { KTModal } from '@/metronic/core';

import { useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom';

import { useLocale, useTranslations } from 'next-intl';
import { convertDateTime, isMobileUserAgent } from '@/utils';

interface ModalProps {
  noticeAlertModal?: boolean;
  noticeAlertModalData: any;
  onCloseModal: () => void;
}

export default function NoticeAlertModal(props: ModalProps) {
  const { noticeAlertModal, onCloseModal, noticeAlertModalData } = props;

  const locale = useLocale();
  const t_i18n = useTranslations('i18nData');

  const isMobileRef = useRef(false);

  const [eventId, setEventId] = useState<string>('');

  useEffect(() => {
    KTModal.init();
    if (window !== undefined) {
      const result = isMobileUserAgent(window.navigator.userAgent);
      isMobileRef.current = result;
    }
    return () => {
      const backdrop = document.querySelector('.modal-backdrop');
      if (backdrop) {
        backdrop.remove();
      }
    };
  }, []);

  useEffect(() => {
    if (noticeAlertModal) {
      onOpen();
    } else {
      onClose();
    }
  }, [noticeAlertModal]);

  /**
   * @brief Modal 열기
   */
  const onOpen = () => {
    const modalEl = document.querySelector('#noticeAlertModal') as HTMLElement;
    const modalInt = KTModal.getOrCreateInstance(modalEl);

    if (isMobileRef.current) {
      document.getElementById('bodyDiv').classList.add('overflow-hidden');
    }

    const eId = modalInt.on('hidden', () => {
      modalInt.getOption('persistent');
      // onClose();
    });
    setEventId(eId);
    modalInt.show();
  };

  /**
   * @brief Modal 닫힘 && 로그아웃
   */
  const onClose = () => {
    const modalEl = document.querySelector('#noticeAlertModal') as HTMLElement;
    const modalInt = KTModal.getInstance(modalEl);

    if (isMobileRef.current) {
      document.getElementById('bodyDiv').classList.remove('overflow-hidden');
    }
    modalInt.off('hidden', eventId);
    modalInt.hide();

    onCloseModal();
  };

  const onSubmit = () => {
    noticeAlertModalData.onConfirmHandler({ result: 'hide' });
    onClose();
  };

  if (!noticeAlertModal) null;

  return ReactDOM.createPortal(
    <>
      <div
        className="modal"
        data-modal="true"
        id="noticeAlertModal"
        data-modal-backdrop="true"
        data-modal-backdrop-static="true"
        data-modal-persistent="true"
      >
        <div className={`modal-content top-[10%] max-w-[${screen.width}px] sm:max-w-[600px]`}>
          <div className="modal-header">
            <h3 className="modal-title">{t_i18n('dialog_title_notice')}</h3>
            <button className="btn-icon btn btn-xs btn-light" onClick={() => onClose()}>
              <i className="ki-outline ki-cross"></i>
            </button>
          </div>
          <div className="modal-body">
            <div className="flex px-3 py-5">{noticeAlertModalData.data.title}</div>
            <div className="flex px-3 py-5" dangerouslySetInnerHTML={{ __html: noticeAlertModalData.data.content }}></div>
          </div>
          <div className="modal-footer justify-end">
            <div className="flex items-center gap-3">
              <button className="btn btn-clear" onClick={() => onSubmit()}>
                {t_i18n('but_stop_watching_today')}
              </button>
              {/* <button className="btn btn-light" onClick={() => onClose()}>
                {t_i18n('but_close')}
              </button> */}
            </div>
          </div>
        </div>
      </div>
    </>,

    document.getElementById('global_modal')
  );
}
