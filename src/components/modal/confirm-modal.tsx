'use client';

import { KTModal } from '@/metronic/core';
import { useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';
interface ModalProps {
  confirmModal?: boolean;
  confirmModalData: any;
  onCloseModal: () => void;
}
/**
 * @brief Confirm Modal Component
 * @param param
 * @returns
 */
export default function ConfirmModal({ confirmModal, confirmModalData, onCloseModal }: ModalProps) {
  const t_i18n = useTranslations('i18nData');

  const [eventId, setEventId] = useState<string>('');

  useEffect(() => {
    return () => {
      const backdrop = document.querySelector('.modal-backdrop');
      if (backdrop) {
        backdrop.remove();
      }
    };
  }, []);

  // confirmModal 값에 따라서 Modal Open , Close
  useEffect(() => {
    if (confirmModal) {
      onOpen();
    } else {
      onClose();
    }
  }, [confirmModal]);

  /**
   * @brief Modal 열기 및 숨겨질 때 이벤트 등록
   */
  const onOpen = () => {
    const modalEl = document.querySelector('#confirmModal') as HTMLElement;
    const modalInt = KTModal.getInstance(modalEl);
    const eId = modalInt.on('hidden', () => {
      onCloseModal();
    });
    setEventId(eId);
    modalInt.show();
  };

  /**
   * @brief Modal 닫기
   */
  const onClose = () => {
    onCloseModal();
    const modalEl = document.querySelector('#confirmModal') as HTMLElement;
    const modalInt = KTModal.getInstance(modalEl);
    modalInt.off('hidden', eventId);
    modalInt.hide();
  };

  /**
   * @brief Modal의 Submit 버튼 클릭 시  요청한 페이지/컴포넌트로 값 전달 및 Modal 닫기
   */
  const onSubmit = () => {
    confirmModalData.onConfirmHandler(confirmModalData.data);
    onClose();
  };

  return (
    <div className="modal" data-modal="true" id="confirmModal" data-modal-backdrop-static="true">
      <div className="modal-content top-[10%] max-w-[600px]">
        <div className="modal-header">
          <h3 className="modal-title">{confirmModalData?.title}</h3>
          <button className="btn-icon btn btn-xs btn-light" onClick={() => onClose()}>
            <i className="ki-outline ki-cross"></i>
          </button>
        </div>

        {confirmModalData?.content && <div className="modal-body">{confirmModalData?.content}</div>}
        {confirmModalData?.htmlContent && (
          <div className="modal-body" dangerouslySetInnerHTML={{ __html: confirmModalData?.htmlContent }}></div>
        )}
        <div className="modal-footer justify-end">
          <div className="flex gap-4">
            <button className="btn btn-light" onClick={() => onClose()}>
              {t_i18n('but_cancel')}
            </button>
            <button className={`btn ${confirmModalData?.btnColor}`} onClick={onSubmit}>
              {confirmModalData.okBtn}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
