'use client';

import { KTModal } from '@/metronic/core';
import { useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';
interface ModalProps {
  workloadCredentialModal?: boolean;
  workloadCredentialModalData: any;
  onCloseModal: () => void;
}
/**
 * @brief Confirm Modal Component
 * @param param
 * @returns
 */
export default function WorkloadCredentialModal({ workloadCredentialModal, workloadCredentialModalData, onCloseModal }: ModalProps) {
  const t_i18n = useTranslations('i18nData');

  const [eventId, setEventId] = useState<string>('');

  useEffect(() => {
    return () => {
      const backdrop = document.querySelector('.modal-backdrop');
      if (backdrop) {
        backdrop.remove();
      }
    };
  }, []);

  // workloadCredentialModal 값에 따라서 Modal Open , Close
  useEffect(() => {
    if (workloadCredentialModal) {
      onOpen();
    } else {
      onClose();
    }
  }, [workloadCredentialModalData]);

  /**
   * @brief Modal 열기 및 숨겨질 때 이벤트 등록
   */
  const onOpen = () => {
    const modalEl = document.querySelector('#workloadCredentialModal') as HTMLElement;
    const modalInt = KTModal.getInstance(modalEl);
    const eId = modalInt.on('hidden', () => {
      onCloseModal();
    });
    setEventId(eId);
    modalInt.show();
  };

  /**
   * @brief Modal 닫기
   */
  const onClose = () => {
    onCloseModal();
    const modalEl = document.querySelector('#workloadCredentialModal') as HTMLElement;
    const modalInt = KTModal.getInstance(modalEl);
    modalInt.off('hidden', eventId);
    modalInt.hide();
  };

  /**
   * @brief Modal의 Submit 버튼 클릭 시  요청한 페이지/컴포넌트로 값 전달 및 Modal 닫기
   */
  const onSubmit = (type: string) => {
    workloadCredentialModalData.onConfirmHandler({ result: type });
    onClose();
  };

  return (
    <div className="modal" data-modal="true" id="workloadCredentialModal" data-modal-backdrop-static="true">
      <div className="modal-content top-[10%] max-w-[500px]">
        <div className="modal-header">
          <h3 className="modal-title">{workloadCredentialModalData?.title}</h3>
          <button className="btn-icon btn btn-xs btn-light" onClick={() => onSubmit('cancel')}>
            <i className="ki-outline ki-cross"></i>
          </button>
        </div>

        {workloadCredentialModalData?.content && <div className="modal-body">{workloadCredentialModalData?.content}</div>}
        {workloadCredentialModalData?.htmlContent && (
          <div className="modal-body" dangerouslySetInnerHTML={{ __html: workloadCredentialModalData?.htmlContent }}></div>
        )}
        <div className="modal-footer justify-end">
          <div className="flex gap-4">
            <button className="btn btn-light" onClick={() => onSubmit('workload_modify')}>
              {workloadCredentialModalData.cancelBtn ? workloadCredentialModalData.cancelBtn : t_i18n('but_cancel')}
            </button>
            <button className={`btn ${workloadCredentialModalData?.btnColor}`} onClick={() => onSubmit('credential')}>
              {workloadCredentialModalData.okBtn}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
