'use client';

import { signOut, useSession } from 'next-auth/react';
import { KTModal } from '@/metronic/core';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import { useLocale, useTranslations } from 'next-intl';
interface ModalProps {
  sessionAlertModal?: boolean;
  expireTime: number;
  onCloseModal: () => void;
  onSessionHandler: (e: any) => void;
}

/**
 * @brief Session Modal 컴포넌트(세션 만료 시 )
 * @param param0
 * @returns
 */
const SessionAlertModal = forwardRef((props: ModalProps, ref) => {
  const locale = useLocale();
  const t_i18n = useTranslations('i18nData');

  const [eventId, setEventId] = useState<string>('');
  const { data: session, update } = useSession();

  const { sessionAlertModal, expireTime, onSessionHandler, onCloseModal } = props;

  const intervalRef = useRef(null);
  const expireSeconds = useRef(null);
  const [expireSeconds2, setExpireSeconds2] = useState<string>('');

  // 부모 컴포넌트에서 호출할 메서드 정의
  useImperativeHandle(ref, () => ({
    onParentClose() {
      onClose();
    }
  }));
  useEffect(() => {
    return () => {
      clearInterval(intervalRef.current);
      const backdrop = document.querySelector('.modal-backdrop');
      if (backdrop) {
        backdrop.remove();
      }
    };
  }, []);

  // sessionModal 값에 따라서 Modal Open , Close
  useEffect(() => {
    if (sessionAlertModal) {
      onOpen();
    } else {
      onClose();
    }
  }, [sessionAlertModal]);

  /**
   * @brief Modal 열기
   */
  const onOpen = () => {
    convertExpireProcess();
    if (intervalRef.current == null) {
      intervalRef.current = setInterval(() => {
        convertExpireProcess();
      }, 1000);
    }
    const modalEl = document.querySelector('#sessionAlertModal') as HTMLElement;
    const modalInt = KTModal.getInstance(modalEl);

    const eId = modalInt.on('hidden', () => {
      // signOut({ callbackUrl: '/admin' });
    });

    setEventId(eId);
    modalInt.show();
  };

  /**
   * @brief Modal 닫힘 && 로그아웃
   */
  const onClose = () => {
    const modalEl = document.querySelector('#sessionAlertModal') as HTMLElement;
    const modalInt = KTModal.getInstance(modalEl);
    modalInt.off('hidden', eventId);
    modalInt.hide();

    onCloseModal();
  };

  const onReject = () => {
    onSessionHandler({ result: 'close' });
  };

  /**
   * @brief Modal의 submit 버튼 클릭 시 로그아웃 및 Modal 닫기
   */
  const onSubmit = async () => {
    const user = session.user;
    ('use server');
    const extendTime = Date.now();
    await update({ ...user, loginTime: extendTime });
    if (expireTime < Date.now()) {
      onSessionHandler({ result: 'session extend fail' });
    } else {
      onSessionHandler({ result: 'session extend success', extendTime: extendTime });
    }
    clearInterval(intervalRef.current);
  };

  const convertExpireProcess = () => {
    const seconds = Math.floor((expireTime - Date.now()) / 1000);
    if (seconds > 0) {
      const minutes = Math.floor(seconds / 60); // 전체 분
      const remainingSeconds = seconds % 60; // 나머지 초
      expireSeconds.current = `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
      setExpireSeconds2(`${minutes}:${remainingSeconds.toString().padStart(2, '0')}`);
    } else {
      expireSeconds.current = '0:0';
      setExpireSeconds2('0:0');
    }
  };

  if (!sessionAlertModal) return null;
  return ReactDOM.createPortal(
    // return (
    <>
      <div
        className="modal"
        data-modal="true"
        id="sessionAlertModal"
        data-modal-backdrop="true"
        data-modal-backdrop-static="true"
        data-modal-persistent="true"
      >
        <div className="modal-content top-[10%] max-w-[600px]">
          <div className="modal-header">
            <h3 className="modal-title">{t_i18n('session_expire_title')}</h3>
            <button className="btn-icon btn btn-xs btn-light" onClick={() => onClose()}>
              <i className="ki-outline ki-cross"></i>
            </button>
          </div>
          <div className="modal-body">
            <p
              dangerouslySetInnerHTML={{
                __html: t_i18n.rich('session_extends_description1', {
                  0: expireSeconds.current,
                  span: (chunks) => `<span class="font-bold text-danger">${chunks}</span>`
                })
              }}
            ></p>
            <p>{t_i18n('session_extends_description2')}</p>
            <p>{t_i18n('session_extends_description3')}</p>
          </div>
          <div className="modal-footer justify-end">
            <div className="flex gap-4">
              <button className="btn btn-light" onClick={() => onReject()}>
                {t_i18n('but_close')}
              </button>
              <button className="btn btn-primary" onClick={onSubmit}>
                {t_i18n('but_session_extends_setting')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </>,
    // document.body
    document.getElementById('content')
  );
});
SessionAlertModal.displayName = 'SessionAlertModal';
export default SessionAlertModal;
