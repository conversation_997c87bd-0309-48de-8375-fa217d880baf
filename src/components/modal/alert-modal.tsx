'use client';

import { KTModal } from '@/metronic/core';

import React, { useEffect, ReactNode } from 'react';
import ReactDOM from 'react-dom';

interface ModalProps {
  alertModal?: boolean;
  alertModalData?: any;
  onCloseModal: () => void;
}

/**
 * @brief Alert Modal Component
 * @param param
 * @returns
 */
export default function AlertModal({ alertModal, alertModalData, onCloseModal }: ModalProps) {
  useEffect(() => {
    return () => {
      const backdrop = document.querySelector('.modal-backdrop');
      if (backdrop) {
        backdrop.remove();
      }
    };
  }, []);

  // alertModal 값에 따라서 Modal Open , Close
  useEffect(() => {
    if (alertModal) {
      // console.log(alertModalData);
      onOpen();
    } else {
      onClose();
    }
  }, [alertModal]);

  /**
   * @brief Modal 열기 및 숨겨질 때 이벤트 등록
   */
  const onOpen = () => {
    const alerModalEl = document.querySelector('#alertModal') as HTMLElement;
    const alerModalInt = KTModal.getInstance(alerModalEl);
    alerModalInt.on('hidden', () => {
      onCloseModal();
    });
    alerModalInt.show();
  };

  /**
   * @brief Modal 닫기
   */
  const onClose = () => {
    const alerModalEl = document.querySelector('#alertModal') as HTMLElement;
    const alerModalInt = KTModal.getInstance(alerModalEl);
    alerModalInt.hide();
    if (alertModalData?.data?.redirectUrl != undefined) {
      window.location = alertModalData.data.redirectUrl;
    }

    if (alertModalData?.data?.signup == 'success') {
      alertModalData.onSignupSuccessHandler(alertModalData.data);
    }
  };
  if (!alertModal) return null;
  return ReactDOM.createPortal(
    <>
      <div className="modal" data-modal="true" id="alertModal">
        <div className="modal-content top-[10%] max-w-[600px]">
          <div className="modal-header">
            <h3 className="modal-title">{alertModalData?.title}</h3>
            <button className="btn-icon btn btn-xs btn-light" type="button" onClick={() => onClose()}>
              <i className="ki-outline ki-cross"></i>
            </button>
          </div>
          {/* <div className="modal-body">{alertModalData?.content}</div>
           */}
          {alertModalData?.content && <div className="modal-body">{alertModalData?.content}</div>}
          {alertModalData?.htmlContent && (
            <div className="modal-body" dangerouslySetInnerHTML={{ __html: alertModalData?.htmlContent }}></div>
          )}
          <div className="modal-footer justify-end">
            <div className="flex gap-4">
              <button type="button" className={`btn ${alertModalData?.btnColor}`} onClick={() => onClose()}>
                {alertModalData?.okBtn}
              </button>
            </div>
          </div>
        </div>
      </div>
    </>,
    document.getElementById('global_modal')
  );
}
