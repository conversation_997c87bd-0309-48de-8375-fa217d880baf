'use client';
import { KTModal } from '@/metronic/core';
import { isBlank } from '@/utils';
import { getCookie, setCookie } from 'cookies-next';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { ChangeEvent, forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import emitter from '@/lib/event-emitter';
import { useLocale, useTranslations } from 'next-intl';

interface ModalProps {
  settingModal?: boolean;
  onCloseModal: () => void;
}

export default function SettingModal(props: ModalProps) {
  const { settingModal, onCloseModal } = props;

  const locale = useLocale();
  const t_i18n = useTranslations('i18nData');

  const router = useRouter();
  const modalRef = useRef(null);

  const [eventId, setEventId] = useState<string>('');
  const { data: session, update } = useSession();

  const [sessionTime, setSessionTime] = useState<string>('7200');

  useEffect(() => {
    KTModal.init();
    const cookieSessionTime = isBlank(getCookie('GAI_SESSION_TIMEOUT')) ? '7200' : getCookie('GAI_SESSION_TIMEOUT');
    setSessionTime(cookieSessionTime);
    return () => {
      const backdrop = document.querySelector('.modal-backdrop');
      if (backdrop) {
        backdrop.remove();
      }
    };
  }, []);

  useEffect(() => {
    if (settingModal) {
      onOpen();
    } else {
      onClose();
    }
  }, [settingModal]);

  /**
   * @brief Modal 열기
   */
  const onOpen = () => {
    const modalEl = document.querySelector('#settingModal') as HTMLElement;

    const modalInt = KTModal.getOrCreateInstance(modalEl);

    const eId = modalInt.on('hidden', () => {
      modalInt.getOption('persistent');
      // onClose();
    });
    setEventId(eId);
    modalInt.show();
  };

  /**
   * @brief Modal 닫힘 && 로그아웃
   */
  const onClose = () => {
    const modalEl = document.querySelector('#settingModal') as HTMLElement;
    const modalInt = KTModal.getInstance(modalEl);
    modalInt.off('hidden', eventId);
    modalInt.hide();

    onCloseModal();
  };

  /**
   * @brief 세션 유지시간 ChangeEvent
   * @param e
   */
  const onChangeSessionTime = (e: ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.currentTarget || e.target;

    setSessionTime(value);
  };

  const onSubmit = () => {
    setCookie('GAI_SESSION_TIMEOUT', sessionTime, {
      path: '/',
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 60 * 60 * 24 * 30 // 30일
    });

    onClose();
    // router.refresh();
    emitter.emit('change.session_time', { sessionTime: sessionTime });
  };

  if (!settingModal) null;

  return ReactDOM.createPortal(
    <>
      <div
        className="modal"
        data-modal="true"
        id="settingModal"
        data-modal-backdrop="true"
        data-modal-backdrop-static="true"
        data-modal-persistent="true"
      >
        <div className="modal-content top-[10%] max-w-[600px]">
          <div className="modal-header">
            <h3 className="modal-title">{t_i18n('personal_setting_title')}</h3>
            <button className="btn-icon btn btn-xs btn-light" onClick={() => onClose()}>
              <i className="ki-outline ki-cross"></i>
            </button>
          </div>
          <div className="modal-body">
            <div className="flex px-3 py-5">
              <div className="flex flex-col flex-wrap gap-5 md:flex-nowrap lg:gap-5">
                <div className="flex w-full items-center gap-5">
                  <div className="min-w-32 text-sm font-semibold text-gray-900">세션유지시간</div>
                  <span className="min-w-32 text-2sm font-medium text-gray-600">
                    <select className="select select-sm" onChange={onChangeSessionTime} value={sessionTime}>
                      {process.env.NODE_ENV === 'development' && (
                        <>
                          <option value="60">1분</option>
                          <option value="180">3분</option>
                          <option value="300">5분</option>
                          <option value="600">10분</option>
                        </>
                      )}
                      <option value="1800">{t_i18n('personal_setting_30min')}</option>
                      <option value="3600">{t_i18n('personal_setting_60min')}</option>
                      <option value="5400">{t_i18n('personal_setting_90min')}</option>
                      <option value="7200">{t_i18n('personal_setting_120min')}</option>
                    </select>
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="modal-footer justify-end">
            <div className="flex gap-4">
              <button className="btn btn-light" onClick={() => onClose()}>
                {t_i18n('but_close')}
              </button>
              <button className="btn btn-primary" onClick={onSubmit}>
                {t_i18n('but_modify')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </>,

    document.getElementById('global_modal')
  );
}
