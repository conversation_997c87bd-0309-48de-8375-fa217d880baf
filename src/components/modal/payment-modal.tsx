'use client';

import { KTModal } from '@/metronic/core';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';

interface ModalProps {
  paymentModal?: boolean;
  paymentModalData: any;
  onCloseModal: () => void;
}
/**
 * @brief Confirm Modal Component
 * @param param
 * @returns
 */
export default function PaymentModal({ paymentModal, paymentModalData, onCloseModal }: ModalProps) {
  const t_i18n = useTranslations('i18nData');

  const [eventId, setEventId] = useState<string>('');

  useEffect(() => {
    return () => {
      const backdrop = document.querySelector('.modal-backdrop');
      if (backdrop) {
        backdrop.remove();
      }
    };
  }, []);

  // paymentModal 값에 따라서 Modal Open , Close
  useEffect(() => {
    if (paymentModal) {
      onOpen();
    } else {
      onClose();
    }
  }, [paymentModal]);

  /**
   * @brief Modal 열기 및 숨겨질 때 이벤트 등록
   */
  const onOpen = () => {
    const modalEl = document.querySelector('#paymentModal') as HTMLElement;
    const modalInt = KTModal.getInstance(modalEl);
    const eId = modalInt.on('hidden', () => {
      onCloseModal();
    });
    setEventId(eId);
    modalInt.show();
  };

  /**
   * @brief Modal 닫기
   */
  const onClose = () => {
    onCloseModal();
    const modalEl = document.querySelector('#paymentModal') as HTMLElement;
    const modalInt = KTModal.getInstance(modalEl);
    modalInt.off('hidden', eventId);
    modalInt.hide();
  };

  /**
   * @brief Modal의 Submit 버튼 클릭 시  요청한 페이지/컴포넌트로 값 전달 및 Modal 닫기
   */
  const onSubmit = () => {
    paymentModalData.onPaymentHandler(paymentModalData.data);
    onClose();
  };

  return (
    <div className="modal" data-modal="true" id="paymentModal" data-modal-backdrop-static="true">
      <div className="modal-content top-[10%] max-w-[600px]">
        <div className="modal-header">
          <h3 className="modal-title">{paymentModalData?.title}</h3>
          <button className="btn-icon btn btn-xs btn-light" onClick={() => onClose()}>
            <i className="ki-outline ki-cross"></i>
          </button>
        </div>
        <div className="modal-body">{paymentModalData?.content}</div>
        <div className="modal-footer justify-end">
          <div className="flex gap-4">
            <button className={`btn ${paymentModalData?.btnColor}`} onClick={onSubmit}>
              {paymentModalData.okBtn}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
