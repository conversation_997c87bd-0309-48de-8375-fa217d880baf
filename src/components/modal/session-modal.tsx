'use client';

import { signOut } from 'next-auth/react';
import { KTModal } from '@/metronic/core';
import { useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';
import ReactDOM from 'react-dom';

interface ModalProps {
  sessionModal?: boolean;
}
/**
 * @brief Session Modal 컴포넌트(세션 만료 시 )
 * @param param0
 * @returns
 */
export default function SessionModal({ sessionModal }: ModalProps) {
  const t_i18n = useTranslations('i18nData');

  useEffect(() => {
    return () => {
      const backdrop = document.querySelector('.modal-backdrop');
      if (backdrop) {
        backdrop.remove();
      }
    };
  }, []);

  const [eventId, setEventId] = useState<string>('');
  // sessionModal 값에 따라서 Modal Open , Close
  useEffect(() => {
    if (sessionModal) {
      onOpen();
    } else {
      onClose();
    }
  }, [sessionModal]);

  /**
   * @brief Modal 열기
   */
  const onOpen = () => {
    const modalEl = document.querySelector('#sessionModal') as HTMLElement;
    const modalInt = KTModal.getInstance(modalEl);

    const eId = modalInt.on('hidden', () => {
      signOut({ callbackUrl: '/index' });
    });
    setEventId(eId);
    modalInt.show();
  };

  /**
   * @brief Modal 닫힘 && 로그아웃
   */
  const onClose = () => {
    signOut({ callbackUrl: '/index' });

    const modalEl = document.querySelector('#sessionModal') as HTMLElement;
    const modalInt = KTModal.getInstance(modalEl);
    modalInt.off('hidden', eventId);
    modalInt.hide();
  };

  /**
   * @brief Modal의 submit 버튼 클릭 시 로그아웃 및 Modal 닫기
   */
  const onSubmit = () => {
    signOut({ callbackUrl: '/index' });
    onClose();
  };

  if (!sessionModal) return null;

  // return (
  //   <>
  //     <div
  //       className="modal"
  //       data-modal="true"
  //       id="sessionModal"
  //       data-modal-persistent="true"
  //       data-modal-backdrop="true"
  //       data-modal-backdrop-static="true"
  //     >
  //       <div className="modal-content top-[10%] max-w-[600px]">
  //         <div className="modal-header">
  //           <h3 className="modal-title">{t_i18n('unauthorized_msg01')}</h3>
  //           <button className="btn-icon btn btn-xs btn-light" onClick={() => onClose()}>
  //             <i className="ki-outline ki-cross"></i>
  //           </button>
  //         </div>
  //         <div className="modal-body" dangerouslySetInnerHTML={{ __html: t_i18n.raw('unauthorized_msg02') }}></div>
  //         <div className="modal-footer justify-end">
  //           <div className="flex gap-4">
  //             <button className="btn btn-primary" onClick={onSubmit}>
  //               {t_i18n('but_ok')}
  //             </button>
  //           </div>
  //         </div>
  //       </div>
  //     </div>
  //   </>
  // );

  return ReactDOM.createPortal(
    <>
      <div
        className="modal"
        data-modal="true"
        id="sessionModal"
        data-modal-persistent="true"
        data-modal-backdrop="true"
        data-modal-backdrop-static="true"
      >
        <div className="modal-content top-[10%] max-w-[600px]">
          <div className="modal-header">
            <h3 className="modal-title">{t_i18n('unauthorized_msg01')}</h3>
            <button className="btn-icon btn btn-xs btn-light" onClick={() => onClose()}>
              <i className="ki-outline ki-cross"></i>
            </button>
          </div>
          <div className="modal-body" dangerouslySetInnerHTML={{ __html: t_i18n.raw('unauthorized_msg02') }}></div>
          <div className="modal-footer justify-end">
            <div className="flex gap-4">
              <button className="btn btn-primary" onClick={onSubmit}>
                {t_i18n('but_ok')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </>,

    document.body
  );
}
