'use client';
import { getSession } from '@/auth';
import SessionModal from '@/components/modal/session-modal';
import { Session } from 'next-auth';
import { SessionProvider } from 'next-auth/react';

type Props = {
  children: React.ReactNode;
  session: Session;
};
/**
 * @brief Session Prover Component
 * @param param0
 * @returns
 */
const AuthWrapper = ({ children, session }: Props) => {
  if (session == null || session.accessToken == undefined || session.accessToken == null) {
    return <SessionModal sessionModal={true}></SessionModal>;
  }
  return (
    // <SessionProvider session={session} refetchInterval={30}>
    <SessionProvider session={session}>{children}</SessionProvider>
  );
};

export default AuthWrapper;
