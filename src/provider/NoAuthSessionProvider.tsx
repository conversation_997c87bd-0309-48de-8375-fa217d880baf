'use client';

import { Session } from 'next-auth';
import { SessionProvider } from 'next-auth/react';

type Props = {
  children: React.ReactNode;
  session: Session;
};
/**
 * @brief Session Prover Component
 * @param param0
 * @returns
 */
const NoAuthWrapper = ({ children, session }: Props) => {
  return <SessionProvider session={session}>{children}</SessionProvider>;
};

export default NoAuthWrapper;
