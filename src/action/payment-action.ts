'use server';
import { getSession } from '@/auth';
import { getHeaders } from '@/lib/fetch-header';

/**
 * @brief 결제 주문번호 요청
 * @returns
 */
export const getPointPaymentOrderId = async () => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/point/payment/orderid/request`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, orderId: null };
    }
  } catch (error: any) {
    return { status: 500, error: error.cause };
  }
};
/**
 * @brief 결제 승인
 * @param body
 * @returns
 */
export const sendPaymentApproval = async (body: any) => {
  try {
    const session = await getSession();

    body.owner = session.user.email;

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/point/payment/approval`, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify(body),
      credentials: 'include'
    });
    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};
