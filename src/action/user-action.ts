'use server';

import { getSession } from '@/auth';
import { generateUrlSearchParams } from '@/utils';
import { getHeaders } from '@/lib/fetch-header';

/**
 * @brief 사용자 정보
 * @param email
 * @returns
 */
export const getUser = async (email: string) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/users/${email}`, {
      method: 'GET',
      headers: await getHeaders(),
      credentials: 'include'
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error };
  }
};

/**
 * @brief 사용자 정보 수정
 * @param body
 * @returns
 */
export const updateUser = async (body: any) => {
  const url = `${process.env.NEXT_PUBLIC_GAI_SERVER}/api/users/update`;

  try {
    const response = await fetch(url, {
      method: 'PUT',
      headers: await getHeaders(),
      credentials: 'include',
      body: JSON.stringify(body)
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error };
  }
};

/**
 * @brief 사용자 Credentail 목록
 * @param email
 * @returns
 */
export const getCredentails = async (email: string) => {
  try {
    const queryStr = generateUrlSearchParams({ owner: email });
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/credential/list?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 사용자 Credentail 등록
 * @param body
 * @returns
 */
export const registerCredential = async (body: any) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/credential/register`, {
      method: 'POST',
      headers: await getHeaders(),
      credentials: 'include',
      body: JSON.stringify(body)
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 사용자 Credentail 수정
 * @param body
 * @returns
 */
export const updateCredential = async (body: any) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/credential/update`, {
      method: 'PUT',
      headers: await getHeaders(),
      credentials: 'include',
      body: JSON.stringify(body)
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 사용자 Credentail 삭제
 * @param category
 * @param cloud
 * @returns
 */
export const deleteCredential = async (category: string, cloud: string) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/credential/delete/${category}/${cloud}`, {
      method: 'DELETE',
      headers: await getHeaders(),
      credentials: 'include'
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 사용자 은행 정보
 * @returns
 */
export const getUserBankInfo = async () => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/users/bank/info`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};

export const getUserKaKaoBankIdentification = async () => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/users/kakao/bank/identification`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};

export const getUserKaKaoBankIdentificationResult = async (body: any) => {
  try {
    const queryParams = {
      digitalCompositeKey: body.identityCompositeKey,
      accountCompositeKey: body.accountCompositeKey,
      accountTrxKey: body.accountTrxKey
    };

    const queryStr = generateUrlSearchParams(queryParams);
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/users/kakao/bank/identification/result?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};

export const getUserKaKaoBankDigitalSign = async () => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/users/kakao/bank/digital`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};

export const getUserKaKaoBankDigitalSignResult = async (body: any) => {
  try {
    const queryParams = {
      digitalCompositeKey: body.digitalCompositeKey,
      accountCompositeKey: body.accountCompositeKey,
      accountTrxKey: body.accountTrxKey
    };

    const queryStr = generateUrlSearchParams(queryParams);
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/users/kakao/bank/digital?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 개인 저장소 목록
 * @param email
 * @returns
 */
export const getUserStorageList = async (email: string) => {
  try {
    const queryStr = generateUrlSearchParams({ owner: email });
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/users/storage/list?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};
/**
 * @brief 개인 저장소 정보
 * @returns
 */
export const getUserStorage = async (ser: number) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/users/storage/${ser}`, {
      method: 'GET',
      headers: await getHeaders()
    });
    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};
/**
 * @brief 개인 저장소 등록
 * @param body
 * @returns
 */
export const registerUserStorage = async (body: any) => {
  try {
    const session = await getSession();
    body.owner = session.user.email;
    console.log(body);
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/users/storage/register`, {
      method: 'POST',
      headers: await getHeaders(),
      credentials: 'include',
      body: JSON.stringify(body)
    });
    if (response.ok) {
      const json = await response.json();
      console.log(json);
      return json;
    } else {
      console.log(response);
      const json = await response.json();
      return { status: response.status, data: json };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 개인 저장소 PV & PVC 갱신 및 정보수정
 * @param body
 * @returns
 */
export const updateUserStorage = async (body: any) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/users/storage/update`, {
      method: 'PUT',
      headers: await getHeaders(),
      credentials: 'include',
      body: JSON.stringify(body)
    });
    console.log(body);
    if (response.ok) {
      const json = await response.json();
      console.log(json);
      return json;
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 개인 저장소 삭제
 * @param ser
 * @returns
 */
export const deleteUserStorage = async (ser: number) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/users/storage/delete/${ser}`, {
      method: 'DELETE',
      headers: await getHeaders(),
      credentials: 'include'
    });
    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 개인 저장소 인증 요청
 * @param body
 * @returns
 */
export const credentialUserStorage = async (body: any) => {
  try {
    const session = await getSession();
    body.owner = session.user.email;
    console.log(body);
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/users/storage/credential`, {
      method: 'POST',
      headers: await getHeaders(),
      credentials: 'include',
      body: JSON.stringify(body)
    });
    if (response.ok) {
      const json = await response.json();
      console.log(json);
      return json;
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};
