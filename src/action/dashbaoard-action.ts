'use server';
import { getSession } from '@/auth';
import { getHeaders } from '@/lib/fetch-header';
import { generateUrlSearchParams } from '@/utils';

/**
 * @brief Node/Workload/Pod Cluster State Count 조회
 * @returns
 */
export const getDashboardNodeClusterStateCount = async () => {
  try {
    const session = await getSession();
    const queryParams: any = {
      owner: session.user.email
    };

    const queryStr = generateUrlSearchParams(queryParams);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/dashboard/node/cluster/state?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders(),
      cache: 'no-store'
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};
