'use server';
import { NodeRequest } from '@/types/node';

import { generateUrlSearchParams } from '@/utils';
import { getSession } from '@/auth';
import { getHeaders } from '@/lib/fetch-header';
import { PodSshDto } from '@/types/pods';

/**
 * @brief 소유주 pod 목록
 * @returns
 */
export const getPods = async () => {
  try {
    const session = await getSession();

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/pods/list?owner=${session.user.email}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief pod 스트림로그
 * @param ser
 * @param podName
 * @returns
 */
export const getPodStreamLogs = async (ser: string, podName: string) => {
  const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/workloads/${ser}/pod/${podName}/logs/stream`, {
    method: 'GET',
    headers: await getHeaders()
  });

  const reader = response.body.getReader();

  let chunks = [];
  let done = false;

  while (!done) {
    // 스트림의 데이터를 chunk 단위로 읽기
    const { value, done: streamDone } = await reader.read();
    if (streamDone) {
      done = true;
    } else {
      chunks.push(new TextDecoder().decode(value));
    }
  }
  const data = chunks.join('');
  return data;
};

export const getPodEvents = async (data: any) => {
  try {
    const session = await getSession();
    const body = { ...data, owner: session.user.email };
    const queryStr = generateUrlSearchParams(body);
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/pods/event/list?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

export const getPodsByWorkload = async (ser: string, namespace: string) => {
  try {
    const session = await getSession();

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/pods/list/workload/${ser}?namespace=${namespace}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

export const getPodSSHUser = async (podName: string) => {
  const session = await getSession();
  const namespace = session.user.namespace;
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/pods/ssh/user/${namespace}/${podName}`, {
      method: 'GET',
      headers: await getHeaders()
    });
    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 공인 IP 주소 가져오기기
 * @returns
 */
export const getPublicIp = async () => {
  try {
    // const url = `${process.env.NEXT_PUBLIC_GAI_SERVER}/api/pods/ssh/user/pip`;
    const url = 'https://api.ip.pe.kr/json/';
    const response = await fetch(url, {
      method: 'GET',
      headers: await getHeaders()
    });
    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief Pod Ssh 등록
 * @param data
 * @returns
 */
export const registerPodSSHUser = async (data: PodSshDto) => {
  const session = await getSession();
  const body = { ...data, namespace: session.user.namespace };
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/pods/ssh/user/register`, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify(body),
      credentials: 'include'
    });
    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief Pod Ssh 삭제
 * @param padName
 * @returns
 */
export const deletePodSSHUser = async (padName: string) => {
  const session = await getSession();
  const namespace = session.user.namespace;
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/pods/ssh/user/${namespace}/${padName}/delete`, {
      method: 'DELETE',
      headers: await getHeaders()
    });
    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};
