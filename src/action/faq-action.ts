'use server';

import { generateUrlSearchParams } from '@/utils';

/**
 * @brief Faq목록
 */
const getFaqList = async (queryParams: any) => {
  try {
    const queryStr = generateUrlSearchParams(queryParams);
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/faq/list?${queryStr}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause?.code };
  }
};

export { getFaqList };
