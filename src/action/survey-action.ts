'use server';

import { getHeaders } from '@/lib/fetch-header';

export const getSurvey = async (params: any) => {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_GAI_SERVER}/api/survey/${params.ser}?email=${params.email}&category=${params.category}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        cache: 'no-store'
      }
    );

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause?.code };
  }
};

export const updateSurvey = async (data: any) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/survey/update`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      // headers: await getHeaders(),
      // credentials: 'include',
      body: JSON.stringify(data)
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause?.code };
  }
};
