'use server';
import { getSession } from '@/auth';

/**
 * @brief 토큰 발급
 * @param token
 * @returns
 */

export const getPlatformToken = async (token: string) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/token/platform`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      return await response.json();
    } else {
      const data = await response.json();
      if (data.status == 404) {
        return { status: data.status, data: data };
      } else {
        return { status: response.status, data: null };
      }
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause?.code };
  }
};

/**
 * @brief 회원가입
 * @param body
 * @returns
 */
export const signupPlatform = async (body: any) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/users/signup`, {
      method: 'POST',
      headers: {
        Authorization: 'Bearer ',
        'Content-Type': `application/json`
      },
      body: JSON.stringify(body)
    });

    if (response.ok) {
      return await response.json();
    } else {
      const result = await response.json();
      return { status: result.status, data: {}, message: result.message };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: {}, error: error.cause?.code };
  }
};

export const login = async (idToken: any) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/auth/login/success`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${idToken}`,
        'Content-Type': `application/json`
      }
    });

    if (response.ok) {
      return await response.json();
    } else {
      const result = await response.json();
      return { status: result.status, data: null, message: result.message };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause?.code };
  }
};
