'use server';

import { getSession } from '@/auth';
import { getHeaders } from '@/lib/fetch-header';
import { generateUrlSearchParams } from '@/utils';
import { useSession } from 'next-auth/react';

/**
 * @brief 사용자 포인트 수입, 지출 상태
 * @param queryParams
 * @returns
 */
export const getPointStatus = async (queryParams: any) => {
  try {
    const session = await getSession();

    queryParams.owner = session.user.email;
    const queryStr = generateUrlSearchParams(queryParams);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/point/status?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 네이버 제공 환율정보
 * @returns
 */
export const getExchangeRate = async () => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/point/exchangerate`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      const data = await response.json();

      const majors = data.majors as any[];

      const major = majors.find((item) => item.exchangeCode == 'USD');
      if (major != undefined) {
        return { status: response.status, data: Number(major.calcPrice) };
      } else {
        return { status: response.status, data: null };
      }
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, error: error.cause };
  }
};

export const getPointBase = async () => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/point/base`, {
      method: 'GET',
      // headers: await getHeaders()

      headers: { 'Content-Type': `application/json;charset=UTF-8` }
    });

    if (response.ok) {
      const data = await response.json();
      return { status: data.status, data: data.pointBase };
    } else {
      return { status: response.status, data: null };
    }
    // const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/point/exchangerate/krw`, {
    //   method: 'GET',
    //   headers: await getHeaders()
    // });

    // if (response.ok) {
    //   const data = await response.json();
    //   return { status: data.status, data: data.krw };
    // } else {
    //   return { status: response.status, data: null };
    // }
  } catch (error: any) {
    return { status: 500, error: error.cause };
  }
};

/**
 * @brief 해당 노드의 수입 포인트 년 월 목록
 * @returns
 */
export const getPointNodeYearMonth = async (node: string) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/point/node/${node}/yearmonth`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, error: error.cause };
  }
};

/**
 * @brief 해당 노드의 일별 수입내역 목록
 * @param node
 * @returns
 */
export const getPointDailyIncomeHistory = async (queryParams: any) => {
  try {
    const session = await getSession();
    queryParams.owner = session.user.email;

    const queryStr = generateUrlSearchParams(queryParams);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/point/daily/incomes?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, error: error.cause };
  }
};

/**
 * @brief 해당 워크로드의 지출 포인트 년 월 목록
 * @returns
 */
export const getPointWorkloadYearMonth = async (ser: string) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/point/workload/${ser}/yearmonth`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, error: error.cause };
  }
};

/**
 * @brief 해당 워크로드의 일별 지출내역 목록
 * @param
 * @returns
 */
export const getPointDailySpendHistory = async (queryParams: any) => {
  try {
    const session = await getSession();
    queryParams.owner = session.user.email;

    const queryStr = generateUrlSearchParams(queryParams);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/point/daily/spends?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, error: error.cause };
  }
};

/**
 * @brief 포인트 결제내역 목록
 * @param
 * @returns
 */
export const getPointPaymentList = async (queryParams: any) => {
  try {
    const session = await getSession();
    queryParams.owner = session.user.email;

    const queryStr = generateUrlSearchParams(queryParams);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/point/payments?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, error: error.cause };
  }
};

/**
 * @brief 포인트 결제 년월 목록
 * @param
 * @returns
 */
export const getPointUserIncomeYearMonth = async (queryParams: any) => {
  try {
    const session = await getSession();
    queryParams.owner = session.user.email;
    const queryStr = generateUrlSearchParams(queryParams);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/point/user/income/yearmonth?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, error: error.cause };
  }
};

/**
 * @brief 포인트 결제 년월 목록
 * @param
 * @returns
 */
export const getPointUserYearMonth = async (queryParams: any) => {
  try {
    const session = await getSession();
    queryParams.owner = session.user.email;
    const queryStr = generateUrlSearchParams(queryParams);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/point/user/yearmonth?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, error: error.cause };
  }
};

/**
 * @brief 해당 워크로드의 당일 지출내역 목록
 * @param
 * @returns
 */
export const getPointSpendHistory = async (queryParams: any) => {
  try {
    const session = await getSession();
    queryParams.owner = session.user.email;

    const queryStr = generateUrlSearchParams(queryParams);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/point/spend/history?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, error: error.cause };
  }
};

/**
 * @brief 해당 노드의 당일 수입내역 목록
 * @param node
 * @returns
 */
export const getPointIncomeHistory = async (queryParams: any) => {
  try {
    const session = await getSession();
    queryParams.owner = session.user.email;

    const queryStr = generateUrlSearchParams(queryParams);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/point/income/history?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, error: error.cause };
  }
};

/**
 * @brief 해당 노드의 당일 수입내역 목록
 * @param node
 * @returns
 */
export const getPointNetwork = async (queryParams: any) => {
  try {
    const session = await getSession();
    queryParams.owner = session.user.email;

    const queryStr = generateUrlSearchParams(queryParams);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/point/networks?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, error: error.cause };
  }
};

/**
 * @brief 주문번호로 결제정보 조회
 * @param node
 * @returns
 */
export const getPointPaymentByOrderId = async (orderId: string) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/point/payment/${orderId}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, error: error.cause };
  }
};

/**
 * @brief 포인트 환급 상태 조회
 * @returns
 */
export const getPointRefundStatus = async () => {
  const session = await getSession();

  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/point/refund/status/${session.user.email}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, error: error.cause };
  }
};

/**
 * @brief 포인트 환급 목록
 * @param
 * @returns
 */
export const getPointRefundList = async (params: any) => {
  try {
    const session = await getSession();
    params.owner = session.user.email;

    const queryStr = generateUrlSearchParams(params);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/point/refund/list?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });
    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, error: error.cause };
  }
};

/**
 * @brief 포인트 환급 신청
 * @returns
 */
export const registerPointRefund = async (body: {
  ser: number;
  owner: string;
  reqPoint: number;
  krw: number;
  isRefund: boolean;
  note: string;
  reqAt: string;
  transferAt: string;
}) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/point/refund/register`, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify(body),
      credentials: 'include'
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, error: error.cause };
  }
};

/**
 * @brief 포인트 정산 상태 조회
 * @param node
 * @returns
 */
export const getPointSettleStatus = async () => {
  const session = await getSession();

  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/point/settle/status/${session.user.email}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, error: error.cause };
  }
};

/**
 * @brief 포인트 정산 목록
 * @param
 * @returns
 */
export const getPointSettleList = async (params: any) => {
  try {
    const session = await getSession();
    params.owner = session.user.email;

    const queryStr = generateUrlSearchParams(params);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/point/settle/list?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });
    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, error: error.cause };
  }
};

/**
 * @brief 포인트 정산 신청
 * @returns
 */
export const registerPointSettle = async (body: {
  ser: number;
  owner: string;
  reqPoint: number;
  krw: number;
  isSettle: boolean;
  note: string;
  reqAt: string;
  transferAt: string;
}) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/point/settle/register`, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify(body),
      credentials: 'include'
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, error: error.cause };
  }
};

/**
 * @brief 포인트 충전 내역
 * @param
 * @returns
 */
export const getPointChargeList = async (queryParams: any) => {
  try {
    const session = await getSession();
    queryParams.owner = session.user.email;
    const queryStr = generateUrlSearchParams(queryParams);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/point/charge/list?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, error: error.cause };
  }
};

export const usedPointCharge = async (ser: number) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/point/charge/used/${ser}`, {
      method: 'PUT',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, error: error.cause };
  }
};
