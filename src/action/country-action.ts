'use server';

/**
 * @brief 노드 목록
 * @param queryParams
 * @returns
 */
export const getCountrys = async (locale: string) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/country/list?locale=${locale}`, {
      method: 'GET',
      headers: {
        Authorization: 'Bearer ',
        'Content-Type': `application/json`
      }
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};
