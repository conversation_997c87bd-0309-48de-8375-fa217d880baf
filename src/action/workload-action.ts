'use server';
import { NodeRequest } from '@/types/node';

import { generateUrlSearchParams } from '@/utils';
import { getSession } from '@/auth';
import { cookies } from 'next/headers';
import { WorkloadDto, WorkloadRequest } from '@/types/workload';
import { getHeaders } from '@/lib/fetch-header';

/**
 * @brief Workload 목록
 * @param queryParams
 * @returns
 */
export const getWorkloads = async (queryParams: WorkloadRequest) => {
  try {
    const session = await getSession();
    queryParams.owner = session.user.email;
    const queryStr = generateUrlSearchParams(queryParams);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/workloads/list?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief Workload 정보
 * @param ser
 * @returns
 */
export const getWorkload = async (ser: string) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/workloads/${ser}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};
/**
 * @brief Workload 등록
 * @param data
 * @returns
 */
export const registerWorkload = async (data: WorkloadDto) => {
  try {
    const session = await getSession();

    const body = { ...data, owner: session.user.email };

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/workloads/register`, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify(body),
      credentials: 'include'
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief Workload 수정
 * @param data
 * @returns
 */
export const updateWorkload = async (data: WorkloadDto) => {
  try {
    const session = await getSession();

    const body = { ...data, owner: session.user.email };

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/workloads/update`, {
      method: 'PUT',
      headers: await getHeaders(),
      body: JSON.stringify(body),
      credentials: 'include'
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief Workload 상태 수정
 * @param ser
 * @param state
 * @returns
 */
export const updateWorkloadState = async (ser: string, state: string) => {
  try {
    const session = await getSession();

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/workloads/${ser}/state/${state}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief Workload 삭제
 * @param ser
 * @returns
 */
export const deleteWorkload = async (ser: string) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/workloads/delete/${ser}`, {
      method: 'DELETE',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief Workload 상태별 카운트
 * @returns
 */
export const getWorkloadStateCount = async () => {
  try {
    const session = await getSession();
    const queryParams: any = {
      owner: session.user.email
    };
    const queryStr = generateUrlSearchParams(queryParams);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/workloads/state/count?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 워크로드 컨테이너 이미지 체크
 * @returns
 */
export const verifyContainerUrl = async (data: any) => {
  try {
    const session = await getSession();
    const body = { ...data, owner: session.user.email };
    const queryStr = generateUrlSearchParams(body);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/workloads/container/url/verify?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      const result = await response.json();

      return result;
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};
