'use server';
import { getSession } from '@/auth';
import { getHeaders } from '@/lib/fetch-header';
import { generateUrlSearchParams } from '@/utils';

/**
 * @brief 사용자 알람 개수 요청
 * @returns
 */
export const getUserAlarmCount = async () => {
  try {
    const session = await getSession();

    const queryParams = {
      owner: session.user.email
    };
    const queryStr = generateUrlSearchParams(queryParams);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/alarms/data/count?${queryStr}`, {
      method: 'GET',
      cache: 'no-store',
      headers: await getHeaders()
    });
    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 사용자 알람 목록 요청
 * @returns
 */
export const getUserAlarmList = async (params: any) => {
  try {
    const session = await getSession();

    params.owner = session.user.email;
    const queryStr = generateUrlSearchParams(params);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/alarms/data?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });
    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 사용자 알림 삭제
 * @param data
 * @returns
 */
export const deleteUserAlarms = async (data: number[]) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/alarms/data/delete`, {
      method: 'POST',
      headers: await getHeaders(),
      credentials: 'include',
      body: JSON.stringify(data)
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 사용자 알림 메세지 상세보기
 * @param ser
 * @returns
 */
export const getAlarmDetail = async (ser: number) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/alarms/data/view/${ser}`, {
      method: 'GET',
      headers: await getHeaders(),
      credentials: 'include'
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 긴급알림 조회
 * @returns
 */
export const getAlertList = async () => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/alarms/data/alert`, {
      method: 'GET',
      headers: await getHeaders(),
      credentials: 'include'
    });

    if (response.ok) {
      if (response.status === 204) {
        return { status: response.status, data: null };
      } else {
        return await response.json();
      }
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};
