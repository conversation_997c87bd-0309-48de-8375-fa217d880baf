import { UsageRequest } from '@/types/usage';
import { generateUrlSearchParams, mutcTime } from '@/utils';

/**
 * @brief 노드 정보
 * @param node
 * @returns
 */
export const getGuestNode = async (node: string) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/guest/node?name=${node}`, {
      method: 'GET'
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief Node, Pod 시간 별 평균 리소스 사용량
 * @param data
 * @returns
 */
export const getGuestAVGUsage = async (data: UsageRequest) => {
  const requestData = Object.assign({}, data);

  if (requestData.startTime != undefined && requestData.startTime != null && requestData.startTime != '') {
    requestData.startTime = mutcTime(requestData.startTime);
  }

  if (requestData.endTime != undefined && requestData.endTime != null && requestData.endTime != '') {
    requestData.endTime = mutcTime(requestData.endTime);
  }

  const queryStr = generateUrlSearchParams(requestData);

  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/guest/usage/avg?${queryStr}`, {
      method: 'GET'
      //   headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};
