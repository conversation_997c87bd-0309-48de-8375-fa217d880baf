'use server';

import { getHeaders } from '@/lib/fetch-header';

/**
 * @brief 플랫폼에 설정된 GPU가격 목록
 * @param cloud
 * @param gpuName
 * @returns
 */
export const getGpuPricing = async (cloud: string, gpuName: string) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/gpu/pricing/list?cloud=${cloud}&gpuName=${gpuName}`, {
      method: 'GET',
      // headers: await getHeaders()
      headers: { 'Content-Type': `application/json;charset=UTF-8` }
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause?.code };
  }
};

/**
 * @brief 배포가능한 GPU가격 목록
 * @returns
 */
export const getAvailableGPUPricings = async () => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/gpu/available/pricing/list`, {
      method: 'GET',
      // headers: await getHeaders()
      headers: { 'Content-Type': `application/json;charset=UTF-8` }
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause?.code };
  }
};
