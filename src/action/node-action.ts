'use server';
import { NodeRequest } from '@/types/node';
import { NodeNetworkRequest } from '@/types/node-network';
import { generateUrlSearchParams } from '@/utils';
import { getSession } from '@/auth';
import { getHeaders } from '@/lib/fetch-header';

/**
 * @brief 노드 목록
 * @param queryParams
 * @returns
 */
export const getNodes = async (queryParams: NodeRequest) => {
  try {
    const session = await getSession();
    queryParams.owner = session.user.email;

    const queryStr = generateUrlSearchParams(queryParams);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/nodes/list?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 노드 정보
 * @param node
 * @returns
 */
export const getNode = async (node: string) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/nodes/${node}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 노트 상태 변경
 * @param name
 * @param state
 * @returns
 */
export const updateNodeState = async (name: string, state: string) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/nodes/${name}/state/${state}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 노트 삭제
 * @param name
 * @returns
 */
export const deleteNode = async (name: string) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/nodes/delete/${name}`, {
      method: 'DELETE',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 노드 GPU목록
 * @param category
 * @returns
 */
export const getGpus = async (category: string) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/nodes/gpus?category=${category}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 노드 상태별 카운트
 * @returns
 */
export const getNodeStateCount = async () => {
  try {
    const session = await getSession();
    const queryParams: any = {
      owner: session.user.email
    };
    const queryStr = generateUrlSearchParams(queryParams);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/nodes/state/count?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 노드 가격 목록
 * @param nodeName
 * @returns
 */
export const getNodePricingList = async (nodeName: string) => {
  try {
    const session = await getSession();

    const queryParams: any = {
      owner: session.user.email,
      nodeName: nodeName,
      startNum: -1,
      scaleNum: -1
    };
    const queryStr = generateUrlSearchParams(queryParams);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/nodes/pricing/list?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 노드 vm params 수정
 * @param body
 * @returns
 */
export const updateNodeVmParams = async (body: any) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/nodes/edit/vmparams`, {
      method: 'POST',
      headers: await getHeaders(),
      // credentials: 'include',
      body: JSON.stringify(body)
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 노드 가격 설정
 * @param body
 * @returns
 */
export const updateNodePrice = async (body: any) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/nodes/update/price`, {
      method: 'POST',
      headers: await getHeaders(),
      // credentials: 'include',
      body: JSON.stringify(body)
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause };
  }
};

export const downloadAgent = async () => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/agent/download/last`, {
      method: 'GET'
    });

    if (response.ok) {
      const blob = await response.blob();
      const buffer = await blob.arrayBuffer();
      const base64String = Buffer.from(buffer).toString('base64');
      return { status: response.status, data: base64String };
    } else {
      return { status: response.status };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, error: error.cause };
  }
};

/**
 * @brief 노드 네트워크 속도 이력
 * @param queryParams
 * @returns
 */
export const getNodeNetworkSpeedHistory = async (queryParams: NodeNetworkRequest) => {
  try {
    const session = await getSession();
    queryParams.owner = session.user.email;
    const queryStr = generateUrlSearchParams(queryParams);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/nodes/network/speed/list?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief 지정 노드 목록
 * @param queryParams
 * @returns
 */
export const getDedicatedNodes = async (queryParams: any) => {
  try {
    const queryStr = generateUrlSearchParams(queryParams);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/nodes/k8s/list?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

export const getNodeTaintList = async (queryParams: any) => {
  try {
    const session = await getSession();
    queryParams.owner = session.user.email;
    const queryStr = generateUrlSearchParams(queryParams);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/nodes/taint/list?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};
