'use server';
import { getSession } from '@/auth';
import { getHeaders } from '@/lib/fetch-header';
import { UsageRequest } from '@/types/usage';
import { generateUrlSearchParams, mutcTime } from '@/utils';
import dayjs from 'dayjs';

/**
 * @brief Node, Pod 최근시간 별 평군 리소스 사용량
 * @param data
 * @returns
 */
export const getLastAVGUsage = async (data: any) => {
  try {
    const session = await getSession();
    data.owner = session.user.email;
    const queryStr = generateUrlSearchParams(data);

    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/usage/avg/last?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders(),
      credentials: 'include'
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

/**
 * @brief Node, Pod 시간 별 평균 리소스 사용량
 * @param data
 * @returns
 */
export const getAVGUsage = async (data: UsageRequest) => {
  const session = await getSession();
  // data.owner = session.user.email;

  const requestData = Object.assign({}, data);

  if (requestData.startTime != undefined && requestData.startTime != null && requestData.startTime != '') {
    requestData.startTime = mutcTime(requestData.startTime);
  }

  if (requestData.endTime != undefined && requestData.endTime != null && requestData.endTime != '') {
    requestData.endTime = mutcTime(requestData.endTime);
  }

  const queryStr = generateUrlSearchParams(requestData);

  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/usage/avg?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });
    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};

export const getLastUsage = async (data: any) => {
  const requestData = Object.assign({}, data);

  const queryStr = generateUrlSearchParams(requestData);

  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/usage/avg?${queryStr}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (response.ok) {
      return await response.json();
    } else {
      return { status: response.status, data: null };
    }
  } catch (error: any) {
    return { status: 500, data: null, error: error.cause };
  }
};
