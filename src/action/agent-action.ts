'use server';
import { getSession } from '@/auth';

export const getPlatformToken = async (token: string) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_SERVER}/api/token/platform`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      return await response.json();
    } else {
      const data = await response.json();
      if (data.status == 404) {
        return { status: data.status, data: data };
      } else {
        return { status: response.status, data: null };
      }
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause?.code };
  }
};

export const getGrpcAgentInfo = async (nodeName: string): Promise<{ status: number; data: any; error?: string }> => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_GRPC_SERVER}/api/agent/status/${nodeName}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();
      return { status: 200, data };
    } else {
      const data = await response.json();
      if (response.status == 404) {
        return { status: 404, data };
      } else {
        return { status: response.status, data: null };
      }
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, data: null, error: error.cause?.code };
  }
};

export const getGrpcAgentVmControl = async (nodeName: string, action: string) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_GRPC_SERVER}/api/agent/vm/control/${nodeName}/${action}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    if (response.ok) {
      return { status: 200 };
    } else {
      return { status: response.status };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, error: error.cause?.code };
  }
};

export const getGrpcAgenModeControl = async (nodeName: string, action: string) => {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_GAI_GRPC_SERVER}/api/agent/mode/${nodeName}/${action}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    if (response.ok) {
      return { status: 200 };
    } else {
      return { status: response.status };
    }
  } catch (error: any) {
    console.log(error);
    return { status: 500, error: error.cause?.code };
  }
};
