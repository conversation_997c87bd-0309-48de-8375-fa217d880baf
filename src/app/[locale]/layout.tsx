import GoogleTags from '@/components/google/google-tags';

import { NextIntlClientProvider } from 'next-intl';
import { getMessages, unstable_setRequestLocale } from 'next-intl/server';

export const dynamic = 'force-dynamic';

export default async function LocaleLayout({
  children,
  params
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}>) {
  const { locale } = await params;
  const messages = await getMessages();
  unstable_setRequestLocale(locale);

  return (
    <>
      <html className={`h-full`} lang={locale} suppressHydrationWarning>
        <link rel="icon" href="/da/img/favicon.ico" sizes="any" />
        <meta name="naver-site-verification" content="993858c7bffc990f5f0a8ef29c400ade6bebfe20" />
        {process.env.NEXT_PUBLIC_GOOGLE_TAG_ENABLE == 'true' && (
          <>
            <GoogleTags></GoogleTags>
          </>
        )}
        <NextIntlClientProvider messages={messages}>{children}</NextIntlClientProvider>
      </html>
    </>
  );
}
