import { getTranslations } from 'next-intl/server';

import MainBanner from './main-banner';

type Props = {
  params: Promise<{ locale: string }>;
};

export default async function IndexPage({ params }: Props) {
  const { locale } = await params;

  const t_i18n = await getTranslations('i18nData');

  const indexTitle = () => (
    <div>
      <div className="flex w-full justify-center lg:!justify-start">
        <h3 className="text-[20px] font-bold text-[#78829d]"> Global GPU Grid</h3>
      </div>
      <div className="flex justify-center lg:!justify-start">
        <h3
          className="text-[38px] font-bold md:text-[50px] lg:text-[55px] xl:text-[70px]"
          dangerouslySetInnerHTML={{ __html: t_i18n.raw('index_msg01') }}
        ></h3>
      </div>
    </div>
  );

  return (
    <>
      <MainBanner />

      {/* <EfficientService />

      <CloudComputing />

      <Features />

      <TierPrice />

      <AlphaTester />

      <Partner />

      <Contact /> */}
    </>
  );
}
