/* Tailwind core */

/* ! tailwindcss v3.4.6 | MIT License | https://tailwindcss.com */

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/
/* @import url('https://fonts.googleapis.com/css2?family=Inter:latin,ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap'); */

/* @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+KR:wght@100..900&family=Noto+Serif:ital,wght@0,100..900;1,100..900&display=swap'); */

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: var(--tw-gray-200);
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

@font-face {
  font-family: 'Pretendard Variable';
  font-weight: 45 920;
  font-style: normal;
  font-display: swap;
  src: url('../app/fonts/PretendardVariable.woff2') format('woff2-variations');
}

html,
:host {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
  tab-size: 4;
  /* 3 */
  /* font-family: Inter, system-ui, sans-serif; */
  font-family:
    'Pretendard Variable',
    Pretendard,
    -apple-system,
    BlinkMacSystemFont,
    system-ui,
    Roboto,
    'Helvetica Neue',
    'Segoe UI',
    'Apple SD Gothic Neo',
    'Noto Sans KR',
    'Malgun Gothic',
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Segoe UI Symbol',
    sans-serif;
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
  -webkit-tap-highlight-color: transparent;
  /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/
@me body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-smoothing: never;
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
  /* overflow: overlay; */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  /* 1 */
  font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  letter-spacing: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder,
textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: var(--tw-gray-400);
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: var(--tw-gray-400);
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role='button'] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden] {
  display: none;
}

:root {
  --tw-sm: 640px;
  --tw-md: 768px;
  --tw-lg: 1024px;
  --tw-xl: 1280px;
  --tw-2xl: 1536px;
  --tw-gray-100-light: #f9f9f9;
  --tw-gray-200-light: #f1f1f4;
  --tw-gray-300-light: #dbdfe9;
  --tw-gray-400-light: #c4cada;
  --tw-gray-500-light: #99a1b7;
  --tw-gray-600-light: #78829d;
  --tw-gray-700-light: #4b5675;
  --tw-gray-800-light: #252f4a;
  --tw-gray-900-light: #071437;
  --tw-gray-100-dark: #1b1c22;
  --tw-gray-200-dark: #26272f;
  --tw-gray-300-dark: #363843;
  --tw-gray-400-dark: #464852;
  --tw-gray-500-dark: #636674;
  --tw-gray-600-dark: #808290;
  --tw-gray-700-dark: #9a9cae;
  --tw-gray-800-dark: #b5b7c8;
  --tw-gray-900-dark: #f5f5f5;
  --tw-brand-light: #ff6f1e;
  --tw-brand-active-light: #f15700;
  --tw-brand-light-light: #fff5ef;
  --tw-brand-clarity-light: rgba(255, 111, 30, 0.2);
  --tw-brand-inverse-light: #ffffff;
  /* --tw-primary-light: #1b84ff;
  --tw-primary-active-light: #056ee9;
  --tw-primary-light-light: #eff6ff;
  --tw-primary-clarity-light: rgba(27, 132, 255, 0.2);
  --tw-primary-inverse-light: #ffffff; */
  --tw-success-light: #17c653;
  --tw-success-active-light: #04b440;
  --tw-success-light-light: #eafff1;
  --tw-success-clarity-light: rgba(23, 198, 83, 0.2);
  --tw-success-inverse-light: #ffffff;
  /* --tw-info-light: #7239ea;
  --tw-info-active-light: #5014d0;
  --tw-info-light-light: #f8f5ff;
  --tw-info-clarity-light: rgba(114, 57, 234, 0.2);
  --tw-info-inverse-light: #ffffff; */
  --tw-danger-light: #f8285a;
  --tw-danger-active-light: #d81a48;
  --tw-danger-light-light: #ffeef3;
  --tw-danger-clarity-light: rgba(248, 40, 90, 0.2);
  --tw-danger-inverse-light: #ffffff;
  --tw-warning-light: #f6b100;
  --tw-warning-active-light: #dfa000;
  --tw-warning-light-light: #fff8dd;
  --tw-warning-clarity-light: rgba(246, 177, 0, 0.2);
  --tw-warning-inverse-light: #ffffff;
  --tw-dark-light: #1e2129;
  --tw-dark-active-light: #111318;
  --tw-dark-light-light: #f9f9f9;
  --tw-dark-clarity-light: rgba(30, 33, 41, 0.2);
  --tw-dark-inverse-light: #ffffff;
  --tw-light-light: #ffffff;
  --tw-light-active-light: #fcfcfc;
  --tw-light-light-light: #ffffff;
  --tw-light-clarity-light: rgba(255, 255, 255, 0.2);
  --tw-light-inverse-light: #4b5675;
  --tw-secondary-light: #f9f9f9;
  --tw-secondary-active-light: #f9f9f9;
  --tw-secondary-light-light: #f9f9f9;
  --tw-secondary-clarity-light: rgba(249, 249, 249, 0.2);
  --tw-secondary-inverse-light: #4b5675;
  --tw-brand-dark: #d74e00;
  --tw-brand-active-dark: #f35700;
  --tw-brand-light-dark: #272320;
  --tw-brand-clarity-dark: rgba(215, 78, 0, 0.2);
  --tw-brand-inverse-dark: #ffffff;
  /* --tw-primary-dark: #006ae6;
  --tw-primary-active-dark: #107eff;
  --tw-primary-light-dark: #172331;
  --tw-primary-clarity-dark: rgba(0, 106, 230, 0.2);
  --tw-primary-inverse-dark: #ffffff; */
  --tw-success-dark: #00a261;
  --tw-success-active-dark: #01bf73;
  --tw-success-light-dark: #1f2623;
  --tw-success-clarity-dark: rgba(0, 162, 97, 0.2);
  --tw-success-inverse-dark: #ffffff;
  /* --tw-info-dark: #883fff;
  --tw-info-active-dark: #9e63ff;
  --tw-info-light-dark: #272134;
  --tw-info-clarity-dark: rgba(136, 63, 255, 0.2);
  --tw-info-inverse-dark: #ffffff; */
  --tw-danger-dark: #e42855;
  --tw-danger-active-dark: #ff3767;
  --tw-danger-light-dark: #302024;
  --tw-danger-clarity-dark: rgba(228, 40, 85, 0.2);
  --tw-danger-inverse-dark: #ffffff;
  --tw-warning-dark: #c59a00;
  --tw-warning-active-dark: #d9aa00;
  --tw-warning-light-dark: #242320;
  --tw-warning-clarity-dark: rgba(197, 154, 0, 0.2);
  --tw-warning-inverse-dark: #ffffff;
  --tw-dark-dark: #272a34;
  --tw-dark-active-dark: #2d2f39;
  --tw-dark-light-dark: #1e2027;
  --tw-dark-clarity-dark: rgba(39, 42, 52, 0.2);
  --tw-dark-inverse-dark: #ffffff;
  --tw-light-dark: #1f212a;
  --tw-light-active-dark: #1f212a;
  --tw-light-light-dark: #1f212a;
  --tw-light-clarity-dark: rgba(31, 33, 42, 0.2);
  --tw-light-inverse-dark: #9a9cae;
  --tw-secondary-dark: #363843;
  --tw-secondary-active-dark: #464852;
  --tw-secondary-light-dark: #363843;
  --tw-secondary-clarity-dark: rgba(54, 56, 67, 0.2);
  --tw-secondary-inverse-dark: #9a9cae;
  --tw-coal-100: #15171c;
  --tw-coal-200: #13141a;
  --tw-coal-300: #111217;
  --tw-coal-400: #0f1014;
  --tw-coal-500: #0d0e12;
  --tw-coal-600: #0b0c10;
  --tw-coal-black: #000000;
  --tw-coal-clarity: rgba(24, 25, 31, 0.5);

  --tw-primary: #8247ff;
  --tw-primary-active: #5f14ff;
  --tw-primary-light: #faf8ff;
  --tw-primary-clarity: rgba(130, 71, 255, 0.2);
  --tw-primary-inverse: #ffffff;

  --tw-primary-light: #8247ff;
  --tw-primary-active-light: #5f14ff;
  --tw-primary-light-light: #faf8ff;
  --tw-primary-clarity-light: rgba(130, 71, 255, 0.2);
  --tw-primary-inverse-light: #ffffff;

  --tw-primary-dark: #8247ff;
  --tw-primary-active-dark: #a57aff;
  --tw-primary-light-dark: #272034;
  --tw-primary-clarity-dark: rgba(130, 71, 255, 0.2);
  --tw-primary-inverse-dark: #ffffff;

  --tw-info: #1b84ff;
  --tw-info-active: #056ee9;
  --tw-info-light: #eff6ff;
  --tw-info-clarity: rgba(5, 110, 233, 0.2);
  --tw-info-inverse: #ffffff;

  --tw-info-light: #1b84ff;
  --tw-info-active-light: #056ee9;
  --tw-info-light-light: #eff6ff;
  --tw-info-clarity-light: rgba(5, 110, 233, 0.2);
  --tw-info-inverse-light: #ffffff;

  --tw-info-dark: #006ae6;
  --tw-info-active-dark: #107eff;
  --tw-info-light-dark: #172331;
  --tw-info-clarity-dark: rgba(0, 106, 230, 0.2);
  --tw-info-inverse-dark: #ffffff;
}

:root,
.light {
  --tw-gray-100: #f9f9f9;
  --tw-gray-200: #f1f1f4;
  --tw-gray-300: #dbdfe9;
  --tw-gray-400: #c4cada;
  --tw-gray-500: #99a1b7;
  --tw-gray-600: #78829d;
  --tw-gray-700: #4b5675;
  --tw-gray-800: #252f4a;
  --tw-gray-900: #071437;
  --tw-brand: #ff6f1e;
  --tw-brand-active: #f15700;
  --tw-brand-light: #fff5ef;
  --tw-brand-clarity: rgba(255, 111, 30, 0.2);
  --tw-brand-inverse: #ffffff;
  /* --tw-primary: #1b84ff;
  --tw-primary-active: #056ee9;
  --tw-primary-light: #eff6ff;
  --tw-primary-clarity: rgba(27, 132, 255, 0.2);
  --tw-primary-inverse: #ffffff; */

  --tw-primary: #8247ff;
  --tw-primary-active: #5f14ff;
  --tw-primary-light: #faf8ff;
  --tw-primary-clarity: rgba(130, 71, 255, 0.2);
  --tw-primary-inverse: #ffffff;

  --tw-success: #17c653;
  --tw-success-active: #04b440;
  --tw-success-light: #eafff1;
  --tw-success-clarity: rgba(23, 198, 83, 0.2);
  --tw-success-inverse: #ffffff;
  /* --tw-info: #7239ea;
  --tw-info-active: #5014d0;
  --tw-info-light: #f8f5ff;
  --tw-info-clarity: rgba(114, 57, 234, 0.2);
  --tw-info-inverse: #ffffff; */

  --tw-info: #1b84ff;
  --tw-info-active: #056ee9;
  --tw-info-light: #eff6ff;
  --tw-info-clarity: rgba(5, 110, 233, 0.2);
  --tw-info-inverse: #ffffff;

  --tw-danger: #f8285a;
  --tw-danger-active: #d81a48;
  --tw-danger-light: #ffeef3;
  --tw-danger-clarity: rgba(248, 40, 90, 0.2);
  --tw-danger-inverse: #ffffff;
  --tw-warning: #f6b100;
  --tw-warning-active: #dfa000;
  --tw-warning-light: #fff8dd;
  --tw-warning-clarity: rgba(246, 177, 0, 0.2);
  --tw-warning-inverse: #ffffff;
  --tw-dark: #1e2129;
  --tw-dark-active: #111318;
  --tw-dark-light: #f9f9f9;
  --tw-dark-clarity: rgba(30, 33, 41, 0.2);
  --tw-dark-inverse: #ffffff;
  --tw-light: #ffffff;
  --tw-light-active: #fcfcfc;
  --tw-light-light: #ffffff;
  --tw-light-clarity: rgba(255, 255, 255, 0.2);
  --tw-light-inverse: #4b5675;
  --tw-secondary: #f9f9f9;
  --tw-secondary-active: #f9f9f9;
  --tw-secondary-light: #f9f9f9;
  --tw-secondary-clarity: rgba(249, 249, 249, 0.2);
  --tw-secondary-inverse: #4b5675;
  --tw-default-box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.09);
  --tw-light-box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.03);
  --tw-primary-box-shadow: 0px 4px 12px 0px rgba(40, 132, 239, 0.35);
  --tw-success-box-shadow: 0px 4px 12px 0px rgba(53, 189, 100, 0.35);
  --tw-danger-box-shadow: 0px 4px 12px 0px rgba(241, 65, 108, 0.35);
  --tw-info-box-shadow: 0px 4px 12px 0px rgba(114, 57, 234, 0.35);
  --tw-warning-box-shadow: 0px 4px 12px 0px rgba(246, 192, 0, 0.35);
  --tw-dark-box-shadow: 0px 4px 12px 0px rgba(37, 47, 74, 0.35);
  --tw-card-background-color: white;
  --tw-tooltip-background-color: #0f1014;
  --tw-popover-background-color: white;
  --tw-modal-background-color: white;
  --tw-drawer-background-color: white;
  --tw-dropdown-background-color: white;
  --tw-backdrop-background-color: rgba(0, 0, 0, 0.8);
  --tw-table-head-background-color: var(--tw-light-active);
  --tw-card-border: 1px solid var(--tw-gray-200);
  --tw-table-border: 1px solid var(--tw-gray-200);
  --tw-dropdown-border: 1px solid var(--tw-gray-200);
  --tw-popover-border: 1px solid var(--tw-gray-200);
  --tw-tooltip-border: 0;
  --tw-card-box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.03);
  --tw-tooltip-box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.03);
  --tw-popover-box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.03);
  --tw-modal-box-shadow: 0px 10px 14px 0px rgba(15, 42, 81, 0.03);
  --tw-drawer-box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.03);
  --tw-dropdown-box-shadow: 0px 7px 18px 0px rgba(0, 0, 0, 0.09);
  --tw-input-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
}

.dark {
  --tw-gray-100: #1b1c22;
  --tw-gray-200: #26272f;
  --tw-gray-300: #363843;
  --tw-gray-400: #464852;
  --tw-gray-500: #636674;
  --tw-gray-600: #808290;
  --tw-gray-700: #9a9cae;
  --tw-gray-800: #b5b7c8;
  --tw-gray-900: #f5f5f5;
  --tw-brand: #d74e00;
  --tw-brand-active: #f35700;
  --tw-brand-light: #272320;
  --tw-brand-clarity: rgba(215, 78, 0, 0.2);
  --tw-brand-inverse: #ffffff;
  /* --tw-primary: #006ae6;
  --tw-primary-active: #107eff;
  --tw-primary-light: #172331;
  --tw-primary-clarity: rgba(0, 106, 230, 0.2);
  --tw-primary-inverse: #ffffff; */

  --tw-primary: #8247ff;
  --tw-primary-active: #a57aff;
  --tw-primary-light: #272034;
  --tw-primary-clarity: rgba(130, 71, 255, 0.2);
  --tw-primary-inverse: #ffffff;

  --tw-success: #00a261;
  --tw-success-active: #01bf73;
  --tw-success-light: #1f2623;
  --tw-success-clarity: rgba(0, 162, 97, 0.2);
  --tw-success-inverse: #ffffff;
  /* --tw-info: #883fff;
  --tw-info-active: #9e63ff;
  --tw-info-light: #272134;
  --tw-info-clarity: rgba(136, 63, 255, 0.2);
  --tw-info-inverse: #ffffff; */

  --tw-info: #006ae6;
  --tw-info-active: #107eff;
  --tw-info-light: #172331;
  --tw-info-clarity: rgba(0, 106, 230, 0.2);
  --tw-info-inverse: #ffffff;

  --tw-danger: #e42855;
  --tw-danger-active: #ff3767;
  --tw-danger-light: #302024;
  --tw-danger-clarity: rgba(228, 40, 85, 0.2);
  --tw-danger-inverse: #ffffff;
  --tw-warning: #c59a00;
  --tw-warning-active: #d9aa00;
  --tw-warning-light: #242320;
  --tw-warning-clarity: rgba(197, 154, 0, 0.2);
  --tw-warning-inverse: #ffffff;
  --tw-dark: #272a34;
  --tw-dark-active: #2d2f39;
  --tw-dark-light: #1e2027;
  --tw-dark-clarity: rgba(39, 42, 52, 0.2);
  --tw-dark-inverse: #ffffff;
  --tw-light: #1f212a;
  --tw-light-active: #1f212a;
  --tw-light-light: #1f212a;
  --tw-light-clarity: rgba(31, 33, 42, 0.2);
  --tw-light-inverse: #9a9cae;
  --tw-secondary: #363843;
  --tw-secondary-active: #464852;
  --tw-secondary-light: #363843;
  --tw-secondary-clarity: rgba(54, 56, 67, 0.2);
  --tw-secondary-inverse: #9a9cae;
  --tw-default-box-shadow: none;
  --tw-light-box-shadow: none;
  --tw-primary-box-shadow: none;
  --tw-success-box-shadow: none;
  --tw-danger-box-shadow: none;
  --tw-info-box-shadow: none;
  --tw-warning-box-shadow: none;
  --tw-dark-box-shadow: none;
  --tw-card-background-color: #111217;
  --tw-tooltip-background-color: #0b0c10;
  --tw-popover-background-color: #0b0c10;
  --tw-modal-background-color: #0b0c10;
  --tw-drawer-background-color: #0b0c10;
  --tw-dropdown-background-color: #0b0c10;
  --tw-backdrop-background-color: rgba(0, 0, 0, 0.8);
  --tw-table-head-background-color: #13141a;
  --tw-card-border: 1px solid #1b1c22;
  --tw-table-border: 1px solid #1b1c22;
  --tw-dropdown-border: 1px solid #1b1c22;
  --tw-tooltip-border: 1px solid #1b1c22;
  --tw-popover-border: 1px solid #1b1c22;
  --tw-card-box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.03);
  --tw-tooltip-box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.03);
  --tw-popover-box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.03);
  --tw-modal-box-shadow: 0px 10px 14px 0px rgba(15, 42, 81, 0.03);
  --tw-drawer-box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.03);
  --tw-dropdown-box-shadow: 0px 7px 18px 0px rgba(0, 0, 0, 0.09);
  --tw-input-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
}

*,
::before,
::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

.\!container {
  width: 100% !important;
}

.container {
  width: 100%;
}

@media (min-width: 640px) {
  .\!container {
    max-width: 640px !important;
  }

  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .\!container {
    max-width: 768px !important;
  }

  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .\!container {
    max-width: 1024px !important;
  }

  .container {
    max-width: 1024px;
  }
}

/* @media (min-width: 1280px) {
  .\!container {
    max-width: 1280px !important;
  }

  .container {
    max-width: 1280px;
  }
} */

@media (min-width: 1280px) {
  .\!container {
    max-width: 1280px !important;
  }

  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .\!container {
    max-width: 1536px !important;
  }

  .container {
    max-width: 1536px;
  }
}

.\!menu {
  display: flex !important;
}

.menu {
  display: flex;
}

.menu-item,
.menu-link {
  padding: 0;
  margin: 0;
}

.menu-item {
  display: flex;
  flex-direction: column;
}

.menu-link {
  cursor: pointer;
  display: flex;
  align-items: center;
  flex-grow: 1;
}

.menu-label {
  display: flex;
  align-items: center;
  flex-grow: 1;
  line-height: 1;
}

.menu-title {
  display: flex;
  align-items: center;
  line-height: 1;
  flex-grow: 1;
}

.menu-icon,
.menu-toggle,
.menu-bullet,
.menu-badge,
.menu-arrow {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.menu-dropdown,
.menu-accordion {
  padding: 0;
  margin: 0;
  display: none;
  align-items: stretch;
  flex-direction: column;
}

.show.menu-item-dropdown > .menu-dropdown,
.base-Popper-root > .menu-container > .menu-dropdown,
.menu-dropdown.menu.show,
.menu-dropdown.show[data-popper-placement] {
  display: flex;
  will-change: transform;
}

.menu-dropdown.\!menu.show {
  display: flex !important;
  will-change: transform !important;
}

.menu-accordion {
  display: none;
  transition: height 0.3s ease;
}

.show:not(.menu-dropdown) > .menu-accordion,
.transitioning:not(.menu-dropdown) > .menu-accordion,
.menu-accordion.show {
  display: flex;
}

.link {
  color: var(--tw-primary);
}

.link:hover {
  color: var(--tw-primary-active);
}

.form-label {
  display: flex;
  width: 100%;
  color: var(--tw-gray-700);
  font-weight: 500;
  font-size: 0.8125rem;
  line-height: 1.125rem;
}

.form-label-sm {
  display: flex;
  width: 25%;
  color: var(--tw-gray-700);
  font-weight: 500;
  font-size: 0.8125rem;
  line-height: 1.125rem;
}

.form-info {
  color: var(--tw-gray-700);
  font-weight: 500;
  font-size: 0.8125rem;
  line-height: 1.125rem;
}

.form-hint {
  color: var(--tw-gray-600);
  font-weight: 500;
  font-size: 0.75rem;
  line-height: 1rem;
}

.menu-dropdown {
  gap: 0.125rem;
  border: var(--tw-dropdown-border);
  box-shadow: var(--tw-dropdown-box-shadow);
  background-color: var(--tw-dropdown-background-color);
  border-radius: 0.75rem;
}

.menu-default {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}

.menu-default .menu-link {
  margin-left: 0.625rem;
  margin-right: 0.625rem;
  padding: 0.625rem;
  border-radius: 0.375rem;
}

.menu-default .menu-title {
  font-size: 0.8125rem;
  line-height: 1.125rem;
  font-weight: 500;
}

.menu-default .menu-icon {
  margin-right: 0.625rem;
}

.menu-default .menu-icon i {
  font-size: 1.125rem;
}

.menu-default .menu-bullet {
  margin-right: 0.625rem;
}

.menu-default .menu-arrow {
  margin-left: 0.5rem;
}

.menu-default .menu-arrow i {
  font-size: 0.6875rem;
}

.menu-default .menu-badge {
  margin-left: 0.625rem;
}

.menu-default .menu-separator {
  border-bottom: var(--tw-dropdown-border);
  margin-top: 0.625rem;
  margin-bottom: 0.625rem;
}

.menu-default .menu-accordion:not(.menu-no-indent) .menu-item > .menu-link {
  margin-left: 1.25rem;
}

.menu-default .menu-accordion:not(.menu-no-indent) .menu-item > .menu-accordion .menu-item > .menu-link {
  margin-left: 2rem;
}

.menu-default .menu-accordion:not(.menu-no-indent) .menu-item > .menu-accordion .menu-item > .menu-accordion .menu-item > .menu-link {
  margin-left: 2.75rem;
}

.menu-fit {
  padding-top: 0;
  padding-bottom: 0;
}

.menu-fit .menu-link {
  margin-left: 0;
  margin-right: 0;
}

.menu-space .menu-link {
  margin-left: 0.625rem;
  margin-right: 0.625rem;
}

.menu-default .menu-item .menu-title {
  color: var(--tw-gray-800);
}

.menu-default .menu-item .menu-icon i {
  color: var(--tw-gray-500);
}

.menu-default .menu-item .menu-arrow i {
  color: var(--tw-gray-500);
}

.menu-default .menu-item .menu-link:hover .menu-title {
  color: var(--tw-gray-900);
}

.menu-default .menu-item .menu-link:hover .menu-icon i {
  color: var(--tw-primary);
}

.menu-default .menu-item.active > .menu-link .menu-title,
.menu-default .menu-item.show > .menu-link .menu-title,
.menu-default .menu-item.here > .menu-link .menu-title,
.menu-default .menu-item.focus > .menu-link .menu-title {
  color: var(--tw-gray-900);
}

.menu-default .menu-item.active > .menu-link .menu-icon i,
.menu-default .menu-item.show > .menu-link .menu-icon i,
.menu-default .menu-item.here > .menu-link .menu-icon i,
.menu-default .menu-item.focus > .menu-link .menu-icon i {
  color: var(--tw-primary);
}

.menu-default .menu-item.active > .menu-link,
.menu-default .menu-item.here > .menu-link {
  background-color: var(--tw-gray-100);
}

.dark .menu-default .menu-item.active > .menu-link,
.dark .menu-default .menu-item.here > .menu-link {
  background-color: var(--tw-coal-300);
}

.menu-default .menu-item > .menu-link:hover {
  background-color: var(--tw-gray-100);
}

.dark .menu-default .menu-item > .menu-link:hover {
  background-color: var(--tw-coal-300);
}

.menu-default .menu-item.disabled > .menu-link {
  opacity: 0.5;
}

.dropdown {
  display: flex;
}

.dropdown-content {
  display: none;
  align-items: stretch;
  flex-direction: column;
  border: var(--tw-dropdown-border);
  box-shadow: var(--tw-dropdown-box-shadow);
  background-color: var(--tw-dropdown-background-color);
  border-radius: 0.75rem;
}

.open.dropdown > .dropdown-content,
.dropdown-content.open[data-popper-placement] {
  will-change: transform;
}

.modal-rounded-t {
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}

.accordion-item {
  display: flex;
  flex-direction: column;
}

.accordion-toggle {
  display: flex;
  flex-grow: 1;
  align-items: center;
  justify-content: space-between;
}

.accordion-content {
  transition: height 300ms ease;
  overflow: hidden;
}

.accordion.active .accordion-content {
  display: block;
  transition: height 300ms ease;
}

.\!input {
  display: block !important;
  width: 100% !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  box-shadow: none !important;
  outline: none !important;
  font-weight: 500 !important;
  font-size: 0.8125rem !important;
  line-height: 1 !important;
  background-color: var(--tw-light-active) !important;
  border-radius: 0.375rem !important;
  height: 2.5rem !important;
  padding-left: 0.75rem !important;
  padding-right: 0.75rem !important;
  border: 1px solid var(--tw-gray-300) !important;
  color: var(--tw-gray-700) !important;
}

.input {
  display: block;
  width: 100%;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  box-shadow: none;
  outline: none;
  font-weight: 500;
  font-size: 0.8125rem;
  line-height: 1;
  background-color: var(--tw-light-active);
  border-radius: 0.375rem;
  height: 2.5rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  border: 1px solid var(--tw-gray-300);
  color: var(--tw-gray-700);
}

.\!input::-moz-placeholder,
.\!input input::-moz-placeholder {
  color: var(--tw-gray-500) !important;
}

.\!input::placeholder,
.\!input input::placeholder {
  color: var(--tw-gray-500) !important;
}

.input::-moz-placeholder,
.input input::-moz-placeholder {
  color: var(--tw-gray-500);
}

.input::placeholder,
.input input::placeholder {
  color: var(--tw-gray-500);
}

.\!input::-moz-placeholder,
.\!input input::-moz-placeholder {
  color: var(--tw-gray-500) !important;
}

.\!input::placeholder,
.\!input input::placeholder {
  color: var(--tw-gray-500) !important;
}

.\!input:hover {
  border-color: var(--tw-gray-400) !important;
}

.input:hover {
  border-color: var(--tw-gray-400);
}

.\!input:focus,
.\!input:has(input:focus) {
  border-color: var(--tw-primary) !important;
  box-shadow: var(--tw-input-focus-box-shadow) !important;
  color: var(--tw-gray-700) !important;
}

.input:focus,
.input:has(input:focus) {
  border-color: var(--tw-primary);
  box-shadow: var(--tw-input-focus-box-shadow);
  color: var(--tw-gray-700);
}

.\!input:focus,
.\!input:has(input:focus) {
  border-color: var(--tw-primary) !important;
  box-shadow: var(--tw-input-focus-box-shadow) !important;
  color: var(--tw-gray-700) !important;
}

.\!input:focus::-moz-placeholder,
.\!input:focus input::-moz-placeholder,
.\!input:has(input:focus)::-moz-placeholder,
.\!input:has(input:focus) input::-moz-placeholder {
  color: var(--tw-gray-600) !important;
}

.\!input:focus::placeholder,
.\!input:focus input::placeholder,
.\!input:has(input:focus)::placeholder,
.\!input:has(input:focus) input::placeholder {
  color: var(--tw-gray-600) !important;
}

.input:focus::-moz-placeholder,
.input:focus input::-moz-placeholder,
.input:has(input:focus)::-moz-placeholder,
.input:has(input:focus) input::-moz-placeholder {
  color: var(--tw-gray-600);
}

.input:focus::placeholder,
.input:focus input::placeholder,
.input:has(input:focus)::placeholder,
.input:has(input:focus) input::placeholder {
  color: var(--tw-gray-600);
}

.\!input:focus::-moz-placeholder,
.\!input:focus input::-moz-placeholder,
.\!input:has(input:focus)::-moz-placeholder,
.\!input:has(input:focus) input::-moz-placeholder {
  color: var(--tw-gray-600) !important;
}

.\!input:focus::placeholder,
.\!input:focus input::placeholder,
.\!input:has(input:focus)::placeholder,
.\!input:has(input:focus) input::placeholder {
  color: var(--tw-gray-600) !important;
}

.\!input:active,
.\!input:has(input:active) {
  color: var(--tw-gray-700) !important;
}

.input:active,
.input:has(input:active) {
  color: var(--tw-gray-700);
}

.\!input:active,
.\!input:has(input:active) {
  color: var(--tw-gray-700) !important;
}

.\!input:active::-moz-placeholder,
.\!input:active input::-moz-placeholder,
.\!input:has(input:active)::-moz-placeholder,
.\!input:has(input:active) input::-moz-placeholder {
  color: var(--tw-gray-600) !important;
}

.\!input:active::placeholder,
.\!input:active input::placeholder,
.\!input:has(input:active)::placeholder,
.\!input:has(input:active) input::placeholder {
  color: var(--tw-gray-600) !important;
}

.input:active::-moz-placeholder,
.input:active input::-moz-placeholder,
.input:has(input:active)::-moz-placeholder,
.input:has(input:active) input::-moz-placeholder {
  color: var(--tw-gray-600);
}

.input:active::placeholder,
.input:active input::placeholder,
.input:has(input:active)::placeholder,
.input:has(input:active) input::placeholder {
  color: var(--tw-gray-600);
}

.\!input:active::-moz-placeholder,
.\!input:active input::-moz-placeholder,
.\!input:has(input:active)::-moz-placeholder,
.\!input:has(input:active) input::-moz-placeholder {
  color: var(--tw-gray-600) !important;
}

.\!input:active::placeholder,
.\!input:active input::placeholder,
.\!input:has(input:active)::placeholder,
.\!input:has(input:active) input::placeholder {
  color: var(--tw-gray-600) !important;
}

.\!input:active,
.\!input:has(input:active) {
  box-shadow: none !important;
}

.input:active,
.input:has(input:active) {
  box-shadow: none;
}

.\!input:active,
.\!input:has(input:active) {
  box-shadow: none !important;
}

.\!input:disabled,
.\!input:has(input:disabled) {
  background-color: var(--tw-gray-100) !important;
  color: var(--tw-gray-400) !important;
}

.input:disabled,
.input:has(input:disabled) {
  background-color: var(--tw-gray-100);
  color: var(--tw-gray-400);
}

.\!input:disabled,
.\!input:has(input:disabled) {
  background-color: var(--tw-gray-100) !important;
  color: var(--tw-gray-400) !important;
}

.\!input:disabled::-moz-placeholder,
.\!input:disabled input::-moz-placeholder,
.\!input:has(input:disabled)::-moz-placeholder,
.\!input:has(input:disabled) input::-moz-placeholder {
  color: var(--tw-gray-300) !important;
}

.\!input:disabled::placeholder,
.\!input:disabled input::placeholder,
.\!input:has(input:disabled)::placeholder,
.\!input:has(input:disabled) input::placeholder {
  color: var(--tw-gray-300) !important;
}

.input:disabled::-moz-placeholder,
.input:disabled input::-moz-placeholder,
.input:has(input:disabled)::-moz-placeholder,
.input:has(input:disabled) input::-moz-placeholder {
  color: var(--tw-gray-300);
}

.input:disabled::placeholder,
.input:disabled input::placeholder,
.input:has(input:disabled)::placeholder,
.input:has(input:disabled) input::placeholder {
  color: var(--tw-gray-300);
}

.\!input:disabled::-moz-placeholder,
.\!input:disabled input::-moz-placeholder,
.\!input:has(input:disabled)::-moz-placeholder,
.\!input:has(input:disabled) input::-moz-placeholder {
  color: var(--tw-gray-300) !important;
}

.\!input:disabled::placeholder,
.\!input:disabled input::placeholder,
.\!input:has(input:disabled)::placeholder,
.\!input:has(input:disabled) input::placeholder {
  color: var(--tw-gray-300) !important;
}

.input-sm {
  font-weight: 500;
  font-size: 0.75rem;
  height: 2rem;
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

.input-lg {
  font-weight: 500;
  font-size: 0.875rem;
  height: 3rem;
}

.\!input:not(input) {
  display: flex !important;
  gap: 0.375rem !important;
  align-items: center !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  box-shadow: none !important;
  outline: none !important;
  color: var(--tw-gray-600) !important;
}

.input:not(input) {
  display: flex;
  gap: 0.375rem;
  align-items: center;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  box-shadow: none;
  outline: none;
  color: var(--tw-gray-600);
}

.\!input:not(input) input {
  flex-grow: 1 !important;
  background-color: transparent !important;
  border-color: transparent !important;
  color: inherit !important;
  outline: none !important;
  font-size: inherit !important;
  font-weight: inherit !important;
}

.input:not(input) input {
  flex-grow: 1;
  background-color: transparent;
  border-color: transparent;
  color: inherit;
  outline: none;
  font-size: inherit;
  font-weight: inherit;
}

.\!input:not(input) i {
  font-size: 1.125rem !important;
}

.input:not(input) i {
  font-size: 1.125rem;
}

.\!input:not(input) .btn-icon {
  border: 0 !important;
  height: auto !important;
  width: auto !important;
}

.input:not(input) .btn-icon {
  border: 0;
  height: auto;
  width: auto;
}

.\!input:not(input) .btn-icon:hover i,
.\!input:not(input) .btn-icon:focus i,
.\!input:not(input) .btn-icon:active i,
.\!input:not(input) .btn-icon.active i {
  color: var(--tw-gray-800) !important;
}

.input:not(input) .btn-icon:hover i,
.input:not(input) .btn-icon:focus i,
.input:not(input) .btn-icon:active i,
.input:not(input) .btn-icon.active i {
  color: var(--tw-gray-800);
}

.\!input:not(input) .btn-icon:hover i,
.\!input:not(input) .btn-icon:focus i,
.\!input:not(input) .btn-icon:active i,
.\!input:not(input) .btn-icon.active i {
  color: var(--tw-gray-800) !important;
}

.\!input:not(input).input-xs i {
  font-size: 0.75rem !important;
}

.input:not(input).input-xs i {
  font-size: 0.75rem;
}

.\!input:not(input).input-sm i {
  font-size: 0.875rem !important;
}

.input:not(input).input-sm i {
  font-size: 0.875rem;
}

.\!input:not(input).input-lg i {
  font-size: 1.25rem !important;
}

.input:not(input).input-lg i {
  font-size: 1.25rem;
}

.input-group {
  display: flex;
  align-items: stretch;
}

.input-group .btn {
  flex-shrink: 0;
}

.input-group .input {
  flex-grow: 1;
}

.input-group .\!input {
  flex-grow: 1 !important;
}

.input-group .input ~ .btn,
.input-group .input ~ .dropdown > .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.input-group .\!input ~ .btn,
.input-group .\!input ~ .dropdown > .btn {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

.input-group .input + .btn,
.input-group .input + .dropdown > .btn {
  border-left: 0;
}

.input-group .\!input + .btn,
.input-group .\!input + .dropdown > .btn {
  border-left: 0 !important;
}

.input-group .btn ~ .input,
.input-group .btn ~ .btn,
.input-group .input ~ .input {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.input-group .btn ~ .\!input,
.input-group .\!input ~ .\!input {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

.input-group .input:has(~ .btn),
.input-group .input:has(~ .input),
.input-group .input:has(~ .dropdown > .btn) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group .\!input:has(~ .btn),
.input-group .\!input:has(~ .\!input),
.input-group .\!input:has(~ .dropdown > .btn) {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.input-group .btn:has(~ .input, ~ .btn),
.input-group .input:has(~ .input) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: 0;
}

.input-group .btn:has(~ .\!input, ~ .btn),
.input-group .\!input:has(~ .\!input) {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
  border-right: 0 !important;
}

.select {
  display: block;
  width: 100%;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  box-shadow: none;
  outline: none;
  background-repeat: no-repeat;
  background-position: right 0.675rem center;
  background-size: 14px 11px;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%2378829D' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
}

.dark .select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23808290' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
}

.select[multiple],
.select[size]:not([size='1']) {
  padding-right: 1rem;
  background-image: none;
}

.select:-moz-focusring {
  color: transparent;
  text-shadow: none;
}

.select {
  font-weight: 500;
  font-size: 0.8125rem;
  line-height: 1;
  background-color: var(--tw-light-active);
  border-radius: 0.375rem;
  height: 2.5rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  border: 1px solid var(--tw-gray-300);
  color: var(--tw-gray-700);
}

.select:hover {
  border-color: var(--tw-gray-400);
}

.select:focus {
  border-color: var(--tw-primary);
  box-shadow: var(--tw-input-focus-box-shadow);
  color: var(--tw-gray-700);
}

.select:focus::-moz-placeholder {
  color: var(--tw-gray-600);
}

.select:focus::placeholder {
  color: var(--tw-gray-600);
}

.select:active {
  color: var(--tw-gray-700);
}

.select:active::-moz-placeholder {
  color: var(--tw-gray-600);
}

.select:active::placeholder {
  color: var(--tw-gray-600);
}

.select:active {
  box-shadow: none;
}

.select:disabled {
  background-color: var(--tw-gray-100);
  color: var(--tw-gray-400);
}

.select:disabled::-moz-placeholder {
  color: var(--tw-gray-300);
}

.select:disabled::placeholder {
  color: var(--tw-gray-300);
}

.select-sm {
  font-weight: 500;
  font-size: 0.75rem;
  height: 2rem;
  padding-left: 0.625rem;
  padding-right: 0.625rem;
  background-size: 14px 10px;
  background-position: right 0.55rem center;
}

.textarea {
  display: block;
  width: 100%;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  box-shadow: none;
  outline: none;
  font-weight: 500;
  font-size: 0.8125rem;
  background-color: var(--tw-light-active);
  border-radius: 0.375rem;
  padding: 0.55rem 0.75rem;
  border: 1px solid var(--tw-gray-300);
  color: var(--tw-gray-700);
}

.textarea::-moz-placeholder {
  color: var(--tw-gray-500);
}

.textarea::placeholder {
  color: var(--tw-gray-500);
}

.textarea:hover {
  border-color: var(--tw-gray-400);
}

.textarea:focus {
  border-color: var(--tw-primary);
  box-shadow: var(--tw-form-input-focus-box-shadow);
  color: var(--tw-gray-700);
}

.textarea:focus::-moz-placeholder {
  color: var(--tw-gray-600);
}

.textarea:focus::placeholder {
  color: var(--tw-gray-600);
}

.textarea:active {
  color: var(--tw-gray-700);
}

.textarea:active::-moz-placeholder {
  color: var(--tw-gray-600);
}

.textarea:active::placeholder {
  color: var(--tw-gray-600);
}

.textarea:active {
  box-shadow: none;
}

.textarea[disabled] {
  background-color: var(--tw-gray-100);
  color: var(--tw-gray-400);
}

.textarea[disabled]::-moz-placeholder {
  color: var(--tw-gray-300);
}

.textarea[disabled]::placeholder {
  color: var(--tw-gray-300);
}

.textarea[readonly] {
  background-color: var(--tw-gray-100);
  color: var(--tw-gray-400);
}

.textarea[readonly]::-moz-placeholder {
  color: var(--tw-gray-300);
}

.textarea[readonly]::placeholder {
  color: var(--tw-gray-300);
}

.switch {
  display: flex;
  align-items: center;
  gap: 0.625rem;
  cursor: pointer;
}

.switch input[type='checkbox'] {
  display: flex;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-color: var(--tw-gray-300);
  position: relative;
  cursor: pointer;
  flex-shrink: 0;
  height: 1.375rem;
  width: 2.125rem;
  border-radius: 1.375rem;
  transition: all 0.15s ease-in-out;
}

.switch input[type='checkbox']:before {
  display: flex;
  position: absolute;
  content: '';
  height: 1rem;
  width: 1rem;
  border-radius: 100%;
  background-color: var(--tw-light);
  left: 0.25rem;
  top: 50%;
  transform: translateY(-50%);
  filter: drop-shadow(0px 3px 4px rgba(0, 0, 0, 0.03));
  transition: all 0.15s ease-in-out;
}

.switch input[type='checkbox']:checked,
.switch input[type='checkbox'][aria-checked='true'] {
  background-color: var(--tw-primary);
  transition: all 0.15s ease-in-out;
}

.switch input[type='checkbox']:checked:before,
.switch input[type='checkbox'][aria-checked='true']:before {
  background-color: #ffffff;
  transition: all 0.15s ease-in-out;
  left: calc(100% - 0.25rem);
  transform: translate(-100%, -50%);
  filter: none;
}

.switch input[type='checkbox']:disabled {
  background-color: var(--tw-gray-100);
  border: 1px solid var(--tw-gray-300);
  cursor: not-allowed;
  opacity: 0.5;
}

.switch input[type='checkbox']:disabled:before {
  background-color: var(--tw-gray-300);
}

.switch input[type='checkbox']:disabled:checked,
.switch input[type='checkbox']:disabled[aria-checked='true'] {
  background-color: var(--tw-primary-clarity);
  border: 0;
}

.switch input[type='checkbox']:disabled:checked:before,
.switch input[type='checkbox']:disabled[aria-checked='true']:before {
  background-color: var(--tw-light);
}

.switch .switch-label {
  color: var(--tw-gray-700);
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1rem;
}

.switch input[type='checkbox'] + .switch-label {
  color: var(--tw-gray-800);
}

.switch:has(input[type='checkbox']:disabled) .switch-label {
  color: var(--tw-gray-500);
}

.switch-sm input[type='checkbox'] {
  height: 1.125rem;
  width: 1.875rem;
  border-radius: 1.125rem;
}

.switch-sm input[type='checkbox']:before {
  height: 0.75rem;
  width: 0.75rem;
}

.switch-sm .switch-label {
  font-size: 0.8125rem;
}

.switch-lg .switch-label {
  font-size: 0.9375rem;
}

.checkbox[type='checkbox'] {
  flex-shrink: 0;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  box-shadow: none;
  background-color: var(--tw-light-light);
  border-radius: 0.375rem;
  height: 1.375rem;
  width: 1.375rem;
  border: 1px solid var(--tw-gray-300);
  background-repeat: no-repeat;
  background-position: center;
  background-size: auto;
}

.checkbox[type='checkbox']:checked,
.checkbox[type='checkbox'][aria-checked='true'] {
  background-color: var(--tw-primary);
  border: 1px solid var(--tw-primary);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='9' viewBox='0 0 12 9' fill='none'%3e%3cpath d='M10.3667 0.541643L4.80007 6.10831L1.56674 2.87498C1.41061 2.71977 1.1994 2.63265 0.979241 2.63265C0.759086 2.63265 0.547876 2.71977 0.391741 2.87498C0.236532 3.03111 0.149414 3.24232 0.149414 3.46248C0.149414 3.68263 0.236532 3.89384 0.391741 4.04998L4.21674 7.87498C4.37288 8.03019 4.58409 8.1173 4.80424 8.1173C5.0244 8.1173 5.23561 8.03019 5.39174 7.87498L11.5417 1.72498C11.6198 1.64751 11.6818 1.55534 11.7241 1.45379C11.7665 1.35224 11.7882 1.24332 11.7882 1.13331C11.7882 1.0233 11.7665 0.914379 11.7241 0.81283C11.6818 0.711281 11.6198 0.619113 11.5417 0.541643C11.3856 0.386434 11.1744 0.299316 10.9542 0.299316C10.7341 0.299316 10.5229 0.386434 10.3667 0.541643Z' fill='white'/%3e%3c/svg%3e");
}

.checkbox[type='checkbox']:indeterminate {
  background-color: var(--tw-primary);
  border: 1px solid var(--tw-primary);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
}

.checkbox[type='checkbox']:focus {
  border-color: var(--tw-primary);
  box-shadow: var(--tw-input-focus-box-shadow);
  color: var(--tw-gray-700);
}

.checkbox[type='checkbox']:focus::-moz-placeholder {
  color: var(--tw-gray-600);
}

.checkbox[type='checkbox']:focus::placeholder {
  color: var(--tw-gray-600);
}

.checkbox[type='checkbox']:active {
  color: var(--tw-gray-700);
}

.checkbox[type='checkbox']:active::-moz-placeholder {
  color: var(--tw-gray-600);
}

.checkbox[type='checkbox']:active::placeholder {
  color: var(--tw-gray-600);
}

.checkbox[type='checkbox']:active {
  box-shadow: none;
}

.checkbox[type='checkbox']:disabled {
  cursor: not-allowed;
  background-color: var(--tw-gray-200);
  border: 1px solid var(--tw-gray-300);
}

.checkbox[type='checkbox']:disabled:indeterminate {
  background-color: var(--tw-primary-clarity);
  border: 0;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
}

.checkbox[type='checkbox']:disabled:checked,
.checkbox[type='checkbox']:disabled[aria-checked='true'] {
  background-color: var(--tw-primary-clarity);
  border: 0;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='9' viewBox='0 0 12 9' fill='none'%3e%3cpath d='M10.3667 0.541643L4.80007 6.10831L1.56674 2.87498C1.41061 2.71977 1.1994 2.63265 0.979241 2.63265C0.759086 2.63265 0.547876 2.71977 0.391741 2.87498C0.236532 3.03111 0.149414 3.24232 0.149414 3.46248C0.149414 3.68263 0.236532 3.89384 0.391741 4.04998L4.21674 7.87498C4.37288 8.03019 4.58409 8.1173 4.80424 8.1173C5.0244 8.1173 5.23561 8.03019 5.39174 7.87498L11.5417 1.72498C11.6198 1.64751 11.6818 1.55534 11.7241 1.45379C11.7665 1.35224 11.7882 1.24332 11.7882 1.13331C11.7882 1.0233 11.7665 0.914379 11.7241 0.81283C11.6818 0.711281 11.6198 0.619113 11.5417 0.541643C11.3856 0.386434 11.1744 0.299316 10.9542 0.299316C10.7341 0.299316 10.5229 0.386434 10.3667 0.541643Z' fill='white'/%3e%3c/svg%3e");
}

.checkbox-sm[type='checkbox'] {
  border-radius: 0.25rem;
  height: 1.125rem;
  width: 1.125rem;
}

.checkbox-sm[type='checkbox']:checked,
.checkbox-sm[type='checkbox'][aria-checked='true'] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='10' height='7' viewBox='0 0 10 7' fill='none'%3e%3cpath d='M8.4932 0.23329L4.03986 4.68662L1.4532 2.09996C1.32829 1.97579 1.15932 1.9061 0.983198 1.9061C0.807074 1.9061 0.638106 1.97579 0.513198 2.09996C0.38903 2.22487 0.319336 2.39383 0.319336 2.56996C0.319336 2.74608 0.38903 2.91505 0.513198 3.03996L3.5732 6.09996C3.69811 6.22412 3.86707 6.29382 4.0432 6.29382C4.21932 6.29382 4.38829 6.22412 4.5132 6.09996L9.4332 1.17996C9.49568 1.11798 9.54528 1.04425 9.57912 0.963008C9.61297 0.881768 9.6304 0.794632 9.6304 0.706624C9.6304 0.618616 9.61297 0.531479 9.57912 0.45024C9.54528 0.369 9.49568 0.295266 9.4332 0.23329C9.30829 0.109123 9.13932 0.0394287 8.9632 0.0394287C8.78707 0.0394287 8.61811 0.109123 8.4932 0.23329Z' fill='white'/%3e%3c/svg%3e");
}

.checkbox-sm[type='checkbox']:indeterminate {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='10' height='2' viewBox='0 0 10 2' fill='none'%3e%3cpath d='M9.04638 0.293335L1.03971 0.333335C0.862903 0.333335 0.693333 0.403573 0.568309 0.528598C0.443285 0.653622 0.373047 0.823191 0.373047 1C0.373047 1.17681 0.443285 1.34638 0.568309 1.47141C0.693333 1.59643 0.862903 1.66667 1.03971 1.66667L9.03971 1.62667C9.21653 1.62667 9.3861 1.55643 9.51112 1.43141C9.63614 1.30638 9.70638 1.13681 9.70638 0.960002C9.70638 0.783191 9.63614 0.613622 9.51112 0.488597C9.3861 0.363573 9.21653 0.293335 9.03971 0.293335H9.04638Z' fill='white'/%3e%3c/svg%3e");
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 0.4rem;
}

.checkbox-label {
  color: var(--tw-gray-800);
  font-weight: 500;
  font-size: 0.8125rem;
  line-height: 1.125rem;
}

.radio[type='radio'] {
  flex-shrink: 0;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  box-shadow: none;
  background-color: var(--tw-light-light);
  border-radius: 50%;
  height: 1.375rem;
  width: 1.375rem;
  border: 1px solid var(--tw-gray-300);
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
}

.radio[type='radio']:checked,
.radio[type='radio'][aria-checked='true'] {
  background-color: var(--tw-primary);
  border: 1px solid var(--tw-primary);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='22' height='22' viewBox='0 0 22 22' fill='none'%3e%3ccircle cx='10.9995' cy='11' r='5.86667' fill='white'/%3e%3c/svg%3e");
}

.radio[type='radio']:focus {
  border-color: var(--tw-primary);
  box-shadow: var(--tw-input-focus-box-shadow);
  color: var(--tw-gray-700);
}

.radio[type='radio']:focus::-moz-placeholder {
  color: var(--tw-gray-600);
}

.radio[type='radio']:focus::placeholder {
  color: var(--tw-gray-600);
}

.radio[type='radio']:active {
  color: var(--tw-gray-700);
}

.radio[type='radio']:active::-moz-placeholder {
  color: var(--tw-gray-600);
}

.radio[type='radio']:active::placeholder {
  color: var(--tw-gray-600);
}

.radio[type='radio']:active {
  box-shadow: none;
}

.radio[type='radio']:disabled {
  cursor: not-allowed;
  background-color: var(--tw-gray-200);
  border: 1px solid var(--tw-gray-300);
}

.radio[type='radio']:disabled:checked,
.radio[type='radio']:disabled[aria-checked='true'] {
  background-color: var(--tw-primary-clarity);
  border: 0;
}

.radio-sm[type='radio'] {
  height: 1.125rem;
  width: 1.125rem;
}

.radio-sm[type='radio']:checked,
.radio-sm[type='radio'][aria-checked='true'] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='18' height='18' viewBox='0 0 18 18' fill='none'%3e%3ccircle cx='9.0002' cy='8.99995' r='4.8' fill='white'/%3e%3c/svg%3e");
}

.radio-group {
  display: flex;
  align-items: center;
  gap: 0.4rem;
}

.radio-label {
  color: var(--tw-gray-800);
  font-weight: 500;
  font-size: 0.8125rem;
  line-height: 1.125rem;
}

.range {
  width: 100%;
  height: 0.5rem;
  padding: 0;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.range:focus {
  outline: 0;
}

.range::-moz-focus-outer {
  border: 0;
}

.range::-webkit-slider-thumb {
  width: 1.625rem;
  height: 1.625rem;
  margin-top: -0.563rem;
  box-shadow: var(--tw-default-box-shadow);
  -webkit-appearance: none;
  appearance: none;
  border: 1px solid var(--tw-gray-200);
  -webkit-transition:
    background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
  transition:
    background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
  border-radius: 50%;
  background-color: var(--tw-light);
}

.range::-webkit-slider-thumb:active {
  background-color: var(--tw-light);
}

.range::-moz-range-thumb {
  width: 1.625rem;
  height: 1.625rem;
  margin-top: -0.563rem;
  box-shadow: var(--tw-default-box-shadow);
  -moz-appearance: none;
  appearance: none;
  border: 1px solid var(--tw-gray-200);
  -moz-transition:
    background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
  transition:
    background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
  border-radius: 50%;
  background-color: var(--tw-light);
}

.range::-moz-range-thumb:active {
  background-color: var(--tw-light);
}

.range::-moz-range-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: var(--tw-gray-200);
  border-color: transparent;
  border-radius: 0.188rem;
}

.range::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: var(--tw-gray-200);
  border-color: transparent;
  border-radius: 0.188rem;
}

.range:disabled {
  pointer-events: none;
}

.range:disabled::-webkit-slider-thumb {
  background-color: var(--tw-gray-100);
}

.range:disabled::-moz-range-thumb {
  background-color: var(--tw-gray-100);
}

.container-fixed {
  flex-grow: 1;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

/* @media (min-width: 1280px) {
  .container-fixed {
    margin-left: auto;
    margin-right: auto;
    padding-left: 2.5rem;
    padding-right: 2.5rem;
    max-width: 1280px;
  }
} */

@media (min-width: 1280px) {
  /* body {
    font-family: 'Noto Sans CJK KR';
    font-style: normal;
    font-weight: bold;
  } */
  .container-fixed {
    margin-left: auto;
    margin-right: auto;
    padding-left: 0rem;
    padding-right: 0rem;
    max-width: 1160px;
  }
}

@media (max-width: 1280px) {
  .container-fixed {
    margin-left: auto;
    margin-right: auto;
    padding-left: 1.25rem;
    padding-right: 1.25rem;
    max-width: 1024px;
  }
}
/* @media (max-width: 1279px) {
  .container-fixed {
    margin-left: auto;
    margin-right: auto;
    padding-left: 2.5rem;
    padding-right: 2.5rem;
    max-width: 1024px;
  }
} */

.image-input {
  display: inline-flex;
  position: relative;
  align-items: stretch;
  justify-content: center;
}

.image-input input[type='file'] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  position: absolute;
  width: 0 !important;
  height: 0 !important;
  overflow: hidden;
  opacity: 0;
}

.image-input .image-input-preview {
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
}

.image-input .image-input-placeholder {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
}

.\!modal {
  width: 100% !important;
  display: none !important;
  opacity: 0 !important;
  position: fixed !important;
  overflow: auto !important;
  inset: 0 !important;
  padding: 1.25rem !important;
  transition: opacity 300ms ease !important;
}

.modal {
  width: 100%;
  display: none;
  opacity: 0;
  position: fixed;
  overflow: auto;
  inset: 0;
  padding: 1.25rem;
  transition: opacity 300ms ease;
}

.\!modal.open {
  opacity: 1 !important;
  transition: opacity 300ms ease !important;
}

.modal.open {
  opacity: 1;
  transition: opacity 300ms ease;
}

.modal-content {
  position: relative;
  margin-left: auto;
  margin-right: auto;
  border-radius: 0.75rem;
  box-shadow: var(--tw-modal-box-shadow);
  background-color: var(--tw-modal-background-color);
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--tw-gray-200);
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}

.modal-title {
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 600;
  color: var(--tw-gray-900);
}

.modal-body {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}

.modal-table .table th:first-child,
.modal-table .table td:first-child {
  padding-left: 1.25rem;
}

.modal-table .table th:last-child,
.modal-table .table td:last-child {
  padding-right: 1.25rem;
}

.modal-backdrop {
  position: fixed;
  inset: 0;
  background-color: var(--tw-backdrop-background-color);
}

.modal-center {
  left: 50%;
  top: 50%;
  margin-left: 0;
  margin-right: 0;
  transform: translate(-50%, -50%);
}

.modal-rounded-t {
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}

.\!drawer {
  transition: transform 300ms ease !important;
  position: fixed !important;
  background-color: var(--tw-drawer-background-color) !important;
}

.drawer {
  transition: transform 300ms ease;
  position: fixed;
  background-color: var(--tw-drawer-background-color);
}

.\!drawer.open {
  box-shadow: var(--tw-drawer-box-shadow) !important;
  transition: transform 300ms ease !important;
}

.drawer.open {
  box-shadow: var(--tw-drawer-box-shadow);
  transition: transform 300ms ease;
}

.drawer-start {
  top: 0;
  bottom: 0;
  left: 0;
  right: auto;
  transform: translateX(-100%);
}

.drawer-start.drawer.open {
  transform: translateX(0);
}

.drawer-start.\!drawer.open {
  transform: translateX(0) !important;
}

.drawer-end.\!drawer.open {
  transform: translateX(0) !important;
}

.drawer-end.drawer.open {
  transform: translateX(0);
}

.drawer-top.\!drawer.open {
  transform: translateY(0) !important;
}

.drawer-top.drawer.open {
  transform: translateY(0);
}

.drawer-bottom.\!drawer.open {
  transform: translateY(0) !important;
}

.drawer-bottom.drawer.open {
  transform: translateY(0);
}

.drawer-backdrop {
  position: fixed;
  inset: 0;
  background-color: var(--tw-backdrop-background-color);
}

.\!tooltip {
  display: none !important;
  color: white !important;
  box-shadow: var(--tw-tooltip-box-shadow) !important;
  background-color: var(--tw-tooltip-background-color) !important;
  border: var(--tw-tooltip-border) !important;
  border-radius: 0.5rem !important;
  padding: 0.375rem 0.6rem !important;
  font-size: 0.75rem !important;
  font-weight: 400 !important;
  line-height: 1rem !important;
}

.tooltip {
  display: none;
  color: white;
  box-shadow: var(--tw-tooltip-box-shadow);
  background-color: var(--tw-tooltip-background-color);
  border: var(--tw-tooltip-border);
  border-radius: 0.5rem;
  padding: 0.375rem 0.6rem;
  font-size: 0.75rem;
  font-weight: 400;
  line-height: 1rem;
}

.\!tooltip.show {
  display: block !important;
}

.tooltip.show {
  display: block;
}

.popover {
  display: none;
  box-shadow: var(--tw-popover-box-shadow);
  background-color: var(--tw-popover-background-color);
  border-radius: 0.5rem;
  border: var(--tw-popover-border);
}

.popover.show {
  display: block;
}

.btn {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  line-height: 1;
  border-radius: 0.375rem;
  height: 2.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  gap: 0.375rem;
  border: 1px solid transparent;
  font-weight: 500;
  font-size: 0.8125rem;
  outline: none;
}

.btn-icon {
  flex-shrink: 0;
  padding: 0;
  gap: 0;
  width: 2.5rem;
}

.btn-icon i {
  line-height: 0 !important;
}

.btn-xs {
  height: 1.75rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  font-weight: 500;
  font-size: 0.6875rem;
  gap: 0.25rem;
}

.btn-xs.btn-icon {
  width: 1.75rem;
}

.btn-sm {
  height: 2rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  font-weight: 500;
  font-size: 0.75rem;
  gap: 0.275rem;
}

.btn-sm.btn-icon {
  width: 2rem;
}

.btn-lg.btn-icon {
  width: 3rem;
}

.btn[disabled],
.btn.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.btn-icon {
  justify-content: center;
  flex-shrink: 0;
  padding: 0;
  gap: 0;
  width: 2.5rem;
}

.btn-icon.btn-xs {
  width: 1.75rem;
}

.btn-icon.btn-sm {
  width: 2rem;
}

.btn-icon.btn-lg {
  width: 3rem;
}

.btn i {
  font-size: 1.125rem;
  line-height: 0 !important;
}

.btn-icon i {
  font-size: 1.5rem;
}

.btn-xs i {
  font-size: 0.75rem;
}

.btn-xs.btn-icon i {
  font-size: 1rem;
}

.btn-sm i {
  font-size: 0.875rem;
}

.btn-sm.btn-icon i {
  font-size: 1.125rem;
}

.btn-lg.btn-icon i {
  font-size: 1.75rem;
}

.btn-icon-xs i {
  font-size: 0.75rem;
}

.btn-icon-md i {
  font-size: 0.9375rem;
}

.btn-icon-lg i {
  font-size: 1.125rem;
}

.btn.btn-icon-sm i {
  font-size: 0.875rem;
}

.btn.btn-icon-md i {
  font-size: 0.9375rem;
}

.btn.btn-icon-lg i {
  font-size: 1.125rem;
}

.btn.btn-icon-xl i {
  font-size: 1.25rem;
}

.btn.btn-icon-2xl i {
  font-size: 1.5rem;
}

.btn-link {
  color: var(--tw-primary);
  font-size: 0.8125rem;
  height: auto;
  padding-bottom: 0.25em;
  padding-left: 0;
  padding-right: 0;
  border-radius: 0;
  font-weight: 600;
  background-color: transparent;
  border-bottom: 1px dashed var(--tw-primary);
}

.btn-link:hover,
.btn-link:focus,
.btn-link:active,
.btn-link.active {
  background-color: transparent;
  border-bottom: 1px dashed var(--tw-primary-active);
  color: var(--tw-primary-active);
}

.btn-link.btn-sm {
  font-size: 0.75rem;
}

.btn-link.btn-lg {
  font-size: 0.875rem;
}

.btn-tabs {
  display: inline-flex;
  align-items: center;
  line-height: 1;
  background-color: var(--tw-gray-100);
  border: 1px solid var(--tw-gray-200);
}

.btn-tabs .btn {
  color: var(--tw-gray-700);
}

.btn-tabs .btn i {
  color: var(--tw-gray-400);
}

.btn-tabs .btn:hover,
.btn-tabs .btn:focus,
.btn-tabs .btn:active,
.btn-tabs .btn.active {
  background-color: var(--tw-light);
  border: 1px solid var(--tw-gray-200);
  color: var(--tw-gray-900);
  box-shadow: var(--tw-light-box-shadow);
}

.btn-tabs .btn:hover i,
.btn-tabs .btn:focus i,
.btn-tabs .btn:active i,
.btn-tabs .btn.active i {
  color: var(--tw-gray-500);
}

.btn-tabs {
  border-radius: 0.375rem;
  height: 2.5rem;
  padding: 0.25rem;
  gap: 0.25rem;
}

.btn-tabs .btn i {
  font-size: 1.125rem;
}

.btn-tabs .btn {
  height: calc(2.5rem - 2 * 0.25rem);
}

.btn-tabs .btn.btn-icon {
  width: calc(2.5rem - 2 * 0.25rem);
}

.btn-tabs.btn-tabs-sm {
  height: 2rem;
  padding: 0.188rem;
  gap: 0.188rem;
}

.btn-tabs.btn-tabs-sm .btn i {
  font-size: 0.875rem;
}

.btn-tabs.btn-tabs-sm .btn {
  height: calc(2rem - 2 * 0.188rem);
}

.btn-tabs.btn-tabs-sm .btn.btn-icon {
  width: calc(2rem - 2 * 0.188rem);
}

.btn-tabs.btn-tabs-lg {
  height: 3rem;
  padding: 0.313rem;
  gap: 0.313rem;
}

.btn-tabs.btn-tabs-lg .btn i {
  font-size: 1.25rem;
}

.btn-tabs.btn-tabs-lg .btn {
  height: calc(3rem - 2 * 0.313rem);
}

.btn-tabs.btn-tabs-lg .btn.btn-icon {
  width: calc(3rem - 2 * 0.313rem);
}

.btn-primary {
  color: #fff;
  background-color: var(--tw-primary);
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active,
.btn-primary.active {
  background-color: var(--tw-primary-active);
  box-shadow: var(--tw-primary-box-shadow);
}

.btn-outline.btn-primary {
  color: var(--tw-primary);
  background-color: var(--tw-primary-light);
  border-color: var(--tw-primary-clarity);
}

.btn-outline.btn-primary i {
  color: var(--tw-primary);
}

.btn-outline.btn-primary:hover,
.btn-outline.btn-primary:focus,
.btn-outline.btn-primary:active,
.btn-outline.btn-primary.active {
  color: var(--tw-primary-inverse);
  background-color: var(--tw-primary);
  border-color: var(--tw-primary);
  box-shadow: none;
}

.btn-outline.btn-primary:hover i,
.btn-outline.btn-primary:focus i,
.btn-outline.btn-primary:active i,
.btn-outline.btn-primary.active i {
  color: var(--tw-primary-inverse);
}

.btn-clear.btn-primary {
  border-color: transparent;
  background-color: transparent;
  color: var(--tw-primary);
}

.btn-clear.btn-primary:hover,
.btn-clear.btn-primary:focus,
.btn-clear.btn-primary:active,
.btn-clear.btn-primary.active {
  color: var(--tw-primary-inverse);
  background-color: var(--tw-primary);
  border-color: var(--tw-primary);
  box-shadow: none;
}

.btn-clear.btn-primary:hover i,
.btn-clear.btn-primary:focus i,
.btn-clear.btn-primary:active i,
.btn-clear.btn-primary.active i {
  color: var(--tw-primary-inverse);
}

.btn-subtle.btn-primary {
  border-color: transparent;
  background-color: transparent;
  color: var(--tw-primary);
}

.btn-subtle.btn-primary i {
  color: var(--tw-primary);
}

.btn-subtle.btn-primary:hover,
.btn-subtle.btn-primary:focus,
.btn-subtle.btn-primary:active,
.btn-subtle.btn-primary.active {
  color: var(--tw-primary);
  background-color: var(--tw-primary-light);
  border-color: var(--tw-primary-light);
  box-shadow: none;
}

.btn-subtle.btn-primary:hover i,
.btn-subtle.btn-primary:focus i,
.btn-subtle.btn-primary:active i,
.btn-subtle.btn-primary.active i {
  color: var(--tw-primary);
}

.btn-success {
  color: #fff;
  background-color: var(--tw-success);
}

.btn-success:hover,
.btn-success:focus,
.btn-success:active,
.btn-success.active {
  background-color: var(--tw-success-active);
  box-shadow: var(--tw-success-box-shadow);
}

.btn-outline.btn-success {
  color: var(--tw-success);
  background-color: var(--tw-success-light);
  border-color: var(--tw-success-clarity);
}

.btn-outline.btn-success i {
  color: var(--tw-success);
}

.btn-outline.btn-success:hover,
.btn-outline.btn-success:focus,
.btn-outline.btn-success:active,
.btn-outline.btn-success.active {
  color: var(--tw-success-inverse);
  background-color: var(--tw-success);
  border-color: var(--tw-success);
  box-shadow: none;
}

.btn-outline.btn-success:hover i,
.btn-outline.btn-success:focus i,
.btn-outline.btn-success:active i,
.btn-outline.btn-success.active i {
  color: var(--tw-success-inverse);
}

.btn-clear.btn-success {
  border-color: transparent;
  background-color: transparent;
  color: var(--tw-success);
}

.btn-clear.btn-success:hover,
.btn-clear.btn-success:focus,
.btn-clear.btn-success:active,
.btn-clear.btn-success.active {
  color: var(--tw-success-inverse);
  background-color: var(--tw-success);
  border-color: var(--tw-success);
  box-shadow: none;
}

.btn-clear.btn-success:hover i,
.btn-clear.btn-success:focus i,
.btn-clear.btn-success:active i,
.btn-clear.btn-success.active i {
  color: var(--tw-success-inverse);
}

.btn-subtle.btn-success {
  border-color: transparent;
  background-color: transparent;
  color: var(--tw-success);
}

.btn-subtle.btn-success i {
  color: var(--tw-success);
}

.btn-subtle.btn-success:hover,
.btn-subtle.btn-success:focus,
.btn-subtle.btn-success:active,
.btn-subtle.btn-success.active {
  color: var(--tw-success);
  background-color: var(--tw-success-light);
  border-color: var(--tw-success-light);
  box-shadow: none;
}

.btn-subtle.btn-success:hover i,
.btn-subtle.btn-success:focus i,
.btn-subtle.btn-success:active i,
.btn-subtle.btn-success.active i {
  color: var(--tw-success);
}

.btn-danger {
  color: #fff;
  background-color: var(--tw-danger);
}

.btn-danger:hover,
.btn-danger:focus,
.btn-danger:active,
.btn-danger.active {
  background-color: var(--tw-danger-active);
  box-shadow: var(--tw-danger-box-shadow);
}

.btn-outline.btn-danger {
  color: var(--tw-danger);
  background-color: var(--tw-danger-light);
  border-color: var(--tw-danger-clarity);
}

.btn-outline.btn-danger i {
  color: var(--tw-danger);
}

.btn-outline.btn-danger:hover,
.btn-outline.btn-danger:focus,
.btn-outline.btn-danger:active,
.btn-outline.btn-danger.active {
  color: var(--tw-danger-inverse);
  background-color: var(--tw-danger);
  border-color: var(--tw-danger);
  box-shadow: none;
}

.btn-outline.btn-danger:hover i,
.btn-outline.btn-danger:focus i,
.btn-outline.btn-danger:active i,
.btn-outline.btn-danger.active i {
  color: var(--tw-danger-inverse);
}

.btn-clear.btn-danger {
  border-color: transparent;
  background-color: transparent;
  color: var(--tw-danger);
}

.btn-clear.btn-danger:hover,
.btn-clear.btn-danger:focus,
.btn-clear.btn-danger:active,
.btn-clear.btn-danger.active {
  color: var(--tw-danger-inverse);
  background-color: var(--tw-danger);
  border-color: var(--tw-danger);
  box-shadow: none;
}

.btn-clear.btn-danger:hover i,
.btn-clear.btn-danger:focus i,
.btn-clear.btn-danger:active i,
.btn-clear.btn-danger.active i {
  color: var(--tw-danger-inverse);
}

.btn-subtle.btn-danger {
  border-color: transparent;
  background-color: transparent;
  color: var(--tw-danger);
}

.btn-subtle.btn-danger i {
  color: var(--tw-danger);
}

.btn-subtle.btn-danger:hover,
.btn-subtle.btn-danger:focus,
.btn-subtle.btn-danger:active,
.btn-subtle.btn-danger.active {
  color: var(--tw-danger);
  background-color: var(--tw-danger-light);
  border-color: var(--tw-danger-light);
  box-shadow: none;
}

.btn-subtle.btn-danger:hover i,
.btn-subtle.btn-danger:focus i,
.btn-subtle.btn-danger:active i,
.btn-subtle.btn-danger.active i {
  color: var(--tw-danger);
}

.btn-warning:hover,
.btn-warning:focus,
.btn-warning:active,
.btn-warning.active {
  background-color: var(--tw-warning-active);
  box-shadow: var(--tw-warning-box-shadow);
}

.btn-outline.btn-warning {
  color: var(--tw-warning);
  background-color: var(--tw-warning-light);
  border-color: var(--tw-warning-clarity);
}

.btn-outline.btn-warning i {
  color: var(--tw-warning);
}

.btn-outline.btn-warning:hover,
.btn-outline.btn-warning:focus,
.btn-outline.btn-warning:active,
.btn-outline.btn-warning.active {
  color: var(--tw-warning-inverse);
  background-color: var(--tw-warning);
  border-color: var(--tw-warning);
  box-shadow: none;
}

.btn-outline.btn-warning:hover i,
.btn-outline.btn-warning:focus i,
.btn-outline.btn-warning:active i,
.btn-outline.btn-warning.active i {
  color: var(--tw-warning-inverse);
}

.btn-clear.btn-warning {
  border-color: transparent;
  background-color: transparent;
  color: var(--tw-warning);
}

.btn-clear.btn-warning:hover,
.btn-clear.btn-warning:focus,
.btn-clear.btn-warning:active,
.btn-clear.btn-warning.active {
  color: var(--tw-warning-inverse);
  background-color: var(--tw-warning);
  border-color: var(--tw-warning);
  box-shadow: none;
}

.btn-clear.btn-warning:hover i,
.btn-clear.btn-warning:focus i,
.btn-clear.btn-warning:active i,
.btn-clear.btn-warning.active i {
  color: var(--tw-warning-inverse);
}

.btn-subtle.btn-warning:hover,
.btn-subtle.btn-warning:focus,
.btn-subtle.btn-warning:active,
.btn-subtle.btn-warning.active {
  color: var(--tw-warning);
  background-color: var(--tw-warning-light);
  border-color: var(--tw-warning-light);
  box-shadow: none;
}

.btn-subtle.btn-warning:hover i,
.btn-subtle.btn-warning:focus i,
.btn-subtle.btn-warning:active i,
.btn-subtle.btn-warning.active i {
  color: var(--tw-warning);
}

.btn-info:hover,
.btn-info:focus,
.btn-info:active,
.btn-info.active {
  background-color: var(--tw-info-active);
  box-shadow: var(--tw-info-box-shadow);
}

.btn-outline.btn-info {
  color: var(--tw-info);
  background-color: var(--tw-info-light);
  border-color: var(--tw-info-clarity);
}

.btn-outline.btn-info i {
  color: var(--tw-info);
}

.btn-outline.btn-info:hover,
.btn-outline.btn-info:focus,
.btn-outline.btn-info:active,
.btn-outline.btn-info.active {
  color: var(--tw-info-inverse);
  background-color: var(--tw-info);
  border-color: var(--tw-info);
  box-shadow: none;
}

.btn-outline.btn-info:hover i,
.btn-outline.btn-info:focus i,
.btn-outline.btn-info:active i,
.btn-outline.btn-info.active i {
  color: var(--tw-info-inverse);
}

.btn-clear.btn-info {
  border-color: transparent;
  background-color: transparent;
  color: var(--tw-info);
}

.btn-clear.btn-info:hover,
.btn-clear.btn-info:focus,
.btn-clear.btn-info:active,
.btn-clear.btn-info.active {
  color: var(--tw-info-inverse);
  background-color: var(--tw-info);
  border-color: var(--tw-info);
  box-shadow: none;
}

.btn-clear.btn-info:hover i,
.btn-clear.btn-info:focus i,
.btn-clear.btn-info:active i,
.btn-clear.btn-info.active i {
  color: var(--tw-info-inverse);
}

.btn-subtle.btn-info:hover,
.btn-subtle.btn-info:focus,
.btn-subtle.btn-info:active,
.btn-subtle.btn-info.active {
  color: var(--tw-info);
  background-color: var(--tw-info-light);
  border-color: var(--tw-info-light);
  box-shadow: none;
}

.btn-subtle.btn-info:hover i,
.btn-subtle.btn-info:focus i,
.btn-subtle.btn-info:active i,
.btn-subtle.btn-info.active i {
  color: var(--tw-info);
}

.btn-dark {
  color: #fff;
  background-color: var(--tw-dark);
}

.btn-dark:hover,
.btn-dark:focus,
.btn-dark:active,
.btn-dark.active {
  background-color: var(--tw-dark-active);
  box-shadow: var(--tw-dark-box-shadow);
}

.btn-outline.btn-dark {
  color: var(--tw-dark);
  background-color: var(--tw-dark-light);
  border-color: var(--tw-dark-clarity);
}

.btn-outline.btn-dark i {
  color: var(--tw-dark);
}

.btn-outline.btn-dark:hover,
.btn-outline.btn-dark:focus,
.btn-outline.btn-dark:active,
.btn-outline.btn-dark.active {
  color: var(--tw-dark-inverse);
  background-color: var(--tw-dark);
  border-color: var(--tw-dark);
  box-shadow: none;
}

.btn-outline.btn-dark:hover i,
.btn-outline.btn-dark:focus i,
.btn-outline.btn-dark:active i,
.btn-outline.btn-dark.active i {
  color: var(--tw-dark-inverse);
}

.btn-clear.btn-dark {
  border-color: transparent;
  background-color: transparent;
  color: var(--tw-dark);
}

.btn-clear.btn-dark:hover,
.btn-clear.btn-dark:focus,
.btn-clear.btn-dark:active,
.btn-clear.btn-dark.active {
  color: var(--tw-dark-inverse);
  background-color: var(--tw-dark);
  border-color: var(--tw-dark);
  box-shadow: none;
}

.btn-clear.btn-dark:hover i,
.btn-clear.btn-dark:focus i,
.btn-clear.btn-dark:active i,
.btn-clear.btn-dark.active i {
  color: var(--tw-dark-inverse);
}

.btn-subtle.btn-dark {
  border-color: transparent;
  background-color: transparent;
  color: var(--tw-dark);
}

.btn-subtle.btn-dark i {
  color: var(--tw-dark);
}

.btn-subtle.btn-dark:hover,
.btn-subtle.btn-dark:focus,
.btn-subtle.btn-dark:active,
.btn-subtle.btn-dark.active {
  color: var(--tw-dark);
  background-color: var(--tw-dark-light);
  border-color: var(--tw-dark-light);
  box-shadow: none;
}

.btn-subtle.btn-dark:hover i,
.btn-subtle.btn-dark:focus i,
.btn-subtle.btn-dark:active i,
.btn-subtle.btn-dark.active i {
  color: var(--tw-dark);
}

.btn-light {
  color: var(--tw-gray-700);
  border-color: var(--tw-gray-300);
  background-color: var(--tw-light);
}

.btn-light i {
  color: var(--tw-gray-500);
}

.btn-light:hover,
.btn-light:focus,
.btn-light:active,
.btn-light.active {
  border-color: var(--tw-gray-300);
  background-color: var(--tw-light-active);
  box-shadow: var(--tw-default-box-shadow);
  color: var(--tw-gray-800);
}

.btn-light:hover i,
.btn-light:focus i,
.btn-light:active i,
.btn-light.active i {
  color: var(--tw-gray-600);
}

.btn-light[disabled],
.btn-light.disabled {
  opacity: 1;
  color: var(--tw-gray-500);
  border-color: var(--tw-gray-200);
  background-color: var(--tw-light);
}

.btn-light[disabled] i,
.btn-light.disabled i {
  color: var(--tw-gray-400);
}

.btn-light.btn-clear {
  border-color: transparent;
  background-color: transparent;
  color: var(--tw-gray-700);
}

.btn-light.btn-clear i {
  color: var(--tw-gray-600);
}

.btn-light.btn-clear:hover,
.btn-light.btn-clear:focus,
.btn-light.btn-clear:active,
.btn-light.btn-clear.active {
  background-color: var(--tw-gray-200);
  border-color: transparent;
  box-shadow: none;
  color: var(--tw-gray-800);
}

.btn-light.btn-clear:hover i,
.btn-light.btn-clear:focus i,
.btn-light.btn-clear:active i,
.btn-light.btn-clear.active i {
  color: var(--tw-gray-700);
}

.btn-light.btn-clear[disabled],
.btn-light.btn-clear.disabled {
  opacity: 1;
  color: var(--tw-gray-500);
  border-color: transparent;
  background-color: transparent;
}

.btn-light.btn-clear[disabled] i,
.btn-light.btn-clear.disabled i {
  color: var(--tw-gray-400);
}

.btn-secondary {
  color: var(--tw-gray-700);
  border-color: var(--tw-gray-200);
  background-color: var(--tw-secondary);
}

.btn-secondary i {
  color: var(--tw-gray-500);
}

.btn-secondary:hover,
.btn-secondary:focus,
.btn-secondary:active,
.btn-secondary.active {
  border-color: var(--tw-gray-300);
  background-color: var(--tw-secondary-active);
  box-shadow: var(--tw-default-box-shadow);
  color: var(--tw-gray-800);
}

.btn-secondary:hover i,
.btn-secondary:focus i,
.btn-secondary:active i,
.btn-secondary.active i {
  color: var(--tw-gray-600);
}

.btn-secondary[disabled],
.btn-secondary.disabled {
  opacity: 1;
  color: var(--tw-gray-500);
  border-color: var(--tw-gray-200);
  background-color: var(--tw-light);
}

.btn-secondary[disabled] i,
.btn-secondary.disabled i {
  color: var(--tw-gray-400);
}

.btn-group .btn + .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: 0;
}

.btn-group .btn:has(+ .btn) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.tabs {
  display: flex;
  align-items: center;
  gap: 1.25rem;
  border-bottom: 1px solid var(--tw-gray-200);
}

.tab {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8125rem;
  line-height: 1;
  color: var(--tw-gray-700);
}

.tab i {
  font-size: 0.9375rem;
  color: var(--tw-gray-600);
}

.tab {
  font-weight: 500;
  padding: 1rem 0;
  border-bottom: 2px solid transparent;
}

.tab.active,
.tab:hover,
.tab:focus {
  color: var(--tw-primary);
}

.tab.active i,
.tab:hover i,
.tab:focus i {
  color: var(--tw-primary);
}

.tab.active {
  border-bottom-color: var(--tw-primary);
}

.pagination {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.pagination .btn {
  display: inline-flex;
  flex-shrink: 0;
  justify-content: center;
  height: 1.875rem;
  width: 1.875rem;
  font-size: 0.8125rem;
  padding: 0;
  line-height: 0;
  color: var(--tw-gray-600);
}

.pagination .btn i {
  font-size: 1rem;
  color: var(--tw-gray-700);
}

.pagination .btn:hover,
.pagination .btn:focus,
.pagination .btn:active,
.pagination .btn.active {
  background-color: var(--tw-gray-200);
  color: var(--tw-gray-800);
}

.pagination .btn:hover i,
.pagination .btn:focus i,
.pagination .btn:active i,
.pagination .btn.active i {
  color: var(--tw-gray-900);
}

.pagination .btn[disabled],
.pagination .btn.disabled {
  opacity: 1;
  pointer-events: none;
  color: var(--tw-gray-500);
}

.pagination .btn[disabled] i,
.pagination .btn.disabled i {
  color: var(--tw-gray-400);
}

.pagination.pagination-sm {
  gap: 0.175rem;
}

.pagination.pagination-sm .btn {
  height: 1.575rem;
  width: 1.575rem;
  font-size: 0.75rem;
}

.pagination.pagination-sm .btn i {
  font-size: 0.875rem;
}

.pagination.pagination-lg {
  gap: 0.35rem;
}

.pagination.pagination-lg .btn {
  height: 2.275rem;
  width: 2.275rem;
  font-size: 0.875rem;
}

.pagination.pagination-lg .btn i {
  font-size: 1.25rem;
}

.card {
  display: flex;
  flex-direction: column;
  box-shadow: var(--tw-card-box-shadow);
  background-color: var(--tw-card-background-color);
  border-radius: 0.75rem;
  border: var(--tw-card-border);
}

.card-title {
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 600;
  color: var(--tw-gray-900);
}

.card-header {
  display: flex;
  min-height: 56px;
  align-items: center;
  justify-content: space-between;
  border-bottom: var(--tw-card-border);
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.card-body {
  flex-grow: 1;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: var(--tw-card-border);
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.card-table table th:first-child,
.card-table table td:first-child {
  padding-left: 1.875rem;
}

.card-table table th:last-child,
.card-table table td:last-child {
  padding-right: 1.875rem;
}

.card-table table.table-border {
  border: 0;
}

.card-group {
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  border-bottom: var(--tw-card-border);
}

.card-group:last-child {
  border-bottom: 0;
}

.card-group + .card-footer {
  border-top: 0;
}

.table th:first-child {
  /* border-top-left-radius: 0.75rem; */
  border-top-left-radius: 0rem;
}

.table th:last-child {
  /* border-top-right-radius: 0.75rem; */
  border-top-right-radius: 0rem;
}

.card-header + .card-body table th:first-child,
.card-header + .card-body table th:last-child,
.card-header + .card-table table th:first-child,
.card-header + .card-table table th:last-child {
  border-radius: 0;
}

.card-grid .card-header,
.card-grid .card-footer {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.card-grid .card-body {
  padding: 0;
}

.card-grid .card-body table {
  border: 0;
}

.card-grid .card-body table th:first-child,
.card-grid .card-body table td:first-child {
  padding-left: 1.25rem;
}

.card-grid .card-body table th:last-child,
.card-grid .card-body table td:last-child {
  padding-right: 1.25rem;
}

.card-border {
  border: var(--tw-card-border);
}

.card-rounded {
  border-radius: 0.75rem;
}

.card-rounded.table {
  border-collapse: separate;
  border-spacing: 0;
}

.card-rounded-b {
  border-bottom-left-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
}

.card-rounded-t {
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}

.card-rounded-tl {
  border-top-left-radius: 0.75rem;
}

.card-rounded-tr {
  border-top-right-radius: 0.75rem;
}

.table {
  width: 100%;
  caption-side: bottom;
  border-collapse: collapse;
  vertical-align: middle;
  text-align: left;
  color: var(--tw-gray-700);
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.table th input[type='checkbox'],
.table td input[type='checkbox'] {
  vertical-align: inherit;
}

.table thead td,
.table thead th,
.table tfoot td,
.table tfoot th {
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  /* background-color: var(--tw-table-head-background-color); */
  background-color: #ffffff;
  color: var(--tw-gray-600);
  /* font-weight: 500;
  font-size: 0.8125rem; */
  line-height: 1.125rem;
  vertical-align: middle;
}

.table thead td,
.table thead th {
  /* border-bottom: var(--tw-table-border); */
  border-bottom: 2px solid #8247ff;
  font-size: 15px;
  font-weight: 400;
  color: #000000;
}

.table tfoot td,
.table tfoot th {
  border-top: var(--tw-table-border);
}

.table tbody {
  vertical-align: inherit;
  font-size: 15px;
  font-weight: 400;
  color: #000000;
}

.table tbody tr td,
.table tbody tr th {
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  border-bottom: var(--tw-table-border);
}

/* 홀수 ROW의 배경색 */
/* .table tbody tr:nth-child(odd) {
  background-color: #ffffff; 
} */

/* 짝수 ROW의 배경색 */
/* .table tbody tr:nth-child(even) {
  background-color: #f7f8f8; 
} */

.table tbody tr:last-child td,
.table tbody tr:last-child th {
  border-bottom: 0;
}

.table-border {
  border: var(--tw-table-border);
}

.table-border td,
.table-border th {
  border-right: var(--tw-table-border);
}

.table-border td:last-child,
.table-border th:last-child {
  border-right: 0;
}

.table-border-l {
  border-left: var(--tw-table-border);
}

.table-border-r {
  border-right: var(--tw-table-border);
}

.table-border-t {
  border-top: var(--tw-table-border);
}

.table-border-b {
  border-bottom: var(--tw-table-border);
}

.sort {
  display: inline-flex;
  align-items: center;
  gap: 0.35rem;
  cursor: pointer;
  line-height: 1;
}

.sort-icon {
  display: inline-flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 0.875rem;
  width: 0.875rem;
  gap: 0.125rem;
  line-height: 1;
}

.sort-icon:before {
  display: inline-block;
  content: '';
  height: 0.25rem;
  width: 0.438rem;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='5' viewBox='0 0 8 5' fill='none'%3e%3cpath d='M1.08333 4.83333C0.908333 4.83333 0.791667 4.775 0.675 4.65833C0.441667 4.425 0.441667 4.075 0.675 3.84167L3.59167 0.925C3.825 0.691667 4.175 0.691667 4.40833 0.925L7.325 3.84167C7.55833 4.075 7.55833 4.425 7.325 4.65833C7.09167 4.89167 6.74167 4.89167 6.50833 4.65833L4 2.15L1.49167 4.65833C1.375 4.775 1.25833 4.83333 1.08333 4.83333Z' fill='%2378829D'/%3e%3c/svg%3e");
}

.sort-icon:after {
  display: inline-block;
  content: '';
  height: 0.25rem;
  width: 0.438rem;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='5' viewBox='0 0 8 5' fill='none'%3e%3cpath d='M4 4.24984C3.825 4.24984 3.70833 4.1915 3.59167 4.07484L0.675 1.15817C0.441667 0.924838 0.441667 0.574837 0.675 0.341504C0.908333 0.108171 1.25833 0.108171 1.49167 0.341504L4 2.84984L6.50833 0.341504C6.74167 0.108171 7.09167 0.108171 7.325 0.341504C7.55833 0.574837 7.55833 0.924838 7.325 1.15817L4.40833 4.07484C4.29167 4.1915 4.175 4.24984 4 4.24984Z' fill='%2378829D'/%3e%3c/svg%3e");
}

.asc > .sort-icon:before {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='5' viewBox='0 0 8 5' fill='none'%3e%3cpath d='M1.08333 4.83333C0.908333 4.83333 0.791667 4.775 0.675 4.65833C0.441667 4.425 0.441667 4.075 0.675 3.84167L3.59167 0.925C3.825 0.691667 4.175 0.691667 4.40833 0.925L7.325 3.84167C7.55833 4.075 7.55833 4.425 7.325 4.65833C7.09167 4.89167 6.74167 4.89167 6.50833 4.65833L4 2.15L1.49167 4.65833C1.375 4.775 1.25833 4.83333 1.08333 4.83333Z' fill='%234B5675'/%3e%3c/svg%3e");
}

.asc > .sort-icon:after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='5' viewBox='0 0 8 5' fill='none'%3e%3cpath d='M4 4.24984C3.825 4.24984 3.70833 4.1915 3.59167 4.07484L0.675 1.15817C0.441667 0.924838 0.441667 0.574837 0.675 0.341504C0.908333 0.108171 1.25833 0.108171 1.49167 0.341504L4 2.84984L6.50833 0.341504C6.74167 0.108171 7.09167 0.108171 7.325 0.341504C7.55833 0.574837 7.55833 0.924838 7.325 1.15817L4.40833 4.07484C4.29167 4.1915 4.175 4.24984 4 4.24984Z' fill='%23C4CADA'/%3e%3c/svg%3e");
}

.desc > .sort-icon:before {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='5' viewBox='0 0 8 5' fill='none'%3e%3cpath d='M1.08333 4.83333C0.908333 4.83333 0.791667 4.775 0.675 4.65833C0.441667 4.425 0.441667 4.075 0.675 3.84167L3.59167 0.925C3.825 0.691667 4.175 0.691667 4.40833 0.925L7.325 3.84167C7.55833 4.075 7.55833 4.425 7.325 4.65833C7.09167 4.89167 6.74167 4.89167 6.50833 4.65833L4 2.15L1.49167 4.65833C1.375 4.775 1.25833 4.83333 1.08333 4.83333Z' fill='%23C4CADA'/%3e%3c/svg%3e");
}

.desc > .sort-icon:after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='5' viewBox='0 0 8 5' fill='none'%3e%3cpath d='M4 4.24984C3.825 4.24984 3.70833 4.1915 3.59167 4.07484L0.675 1.15817C0.441667 0.924838 0.441667 0.574837 0.675 0.341504C0.908333 0.108171 1.25833 0.108171 1.49167 0.341504L4 2.84984L6.50833 0.341504C6.74167 0.108171 7.09167 0.108171 7.325 0.341504C7.55833 0.574837 7.55833 0.924838 7.325 1.15817L4.40833 4.07484C4.29167 4.1915 4.175 4.24984 4 4.24984Z' fill='%234B5675'/%3e%3c/svg%3e");
}

.sort-label {
  display: inline-flex;
  align-items: center;
  gap: 0.35rem;
}

.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  border-radius: 0.25rem;
  padding: 0.5rem 0.5rem;
  border: 1px solid transparent;
  font-weight: 500;
  font-size: 0.6875rem;
}

.badge.badge-pill {
  border-radius: 1.875rem;
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

.badge-dot {
  padding: 0;
  border-radius: 50%;
}

.badge-circle {
  padding: 0;
  border-radius: 50%;
  flex-shrink: 0;
  line-height: 0;
}

.badge-xs {
  font-size: 0.625rem;
  padding: 0.1875rem 0.3125rem;
}

.badge-xs.badge-pill {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.badge-sm {
  padding: 0.3125rem 0.375rem;
}

.badge-sm.badge-pill {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}

.badge-lg.badge-pill {
  padding-left: 0.6875rem;
  padding-right: 0.6875rem;
}

.badge {
  color: var(--tw-gray-700);
  border-color: transparent;
  background-color: var(--tw-gray-200);
}

.badge-outline {
  color: var(--tw-gray-600);
  border-color: var(--tw-gray-300);
  background-color: var(--tw-gray-100);
}

.badge-primary {
  color: #fff;
  background-color: var(--tw-primary);
}

.badge-outline.badge-primary {
  color: var(--tw-primary);
  background-color: var(--tw-primary-light);
  border-color: var(--tw-primary-clarity);
}

.badge-success {
  color: #fff;
  background-color: var(--tw-success);
}

.badge-outline.badge-success {
  color: var(--tw-success);
  background-color: var(--tw-success-light);
  border-color: var(--tw-success-clarity);
}

.badge-danger {
  color: #fff;
  background-color: var(--tw-danger);
}

.badge-outline.badge-danger {
  color: var(--tw-danger);
  background-color: var(--tw-danger-light);
  border-color: var(--tw-danger-clarity);
}

.badge-warning {
  color: #fff;
  background-color: var(--tw-warning);
}

.badge-outline.badge-warning {
  color: var(--tw-warning);
  background-color: var(--tw-warning-light);
  border-color: var(--tw-warning-clarity);
}

.badge-info {
  color: #fff;
  background-color: var(--tw-info);
}

.badge-outline.badge-info {
  color: var(--tw-info);
  background-color: var(--tw-info-light);
  border-color: var(--tw-info-clarity);
}

.badge-outline.badge-dark {
  color: var(--tw-dark);
  background-color: var(--tw-dark-light);
  border-color: var(--tw-dark-clarity);
}

.rating {
  display: inline-flex;
  align-items: stretch;
}

.rating input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  position: absolute;
  left: 9999px;
}

.rating input[disabled] {
  display: none;
}

.rating-on {
  color: var(--tw-warning);
}

.rating-off {
  color: var(--tw-gray-400);
}

.rating-label {
  display: inline-flex;
  align-items: center;
}

.rating-label .rating-on {
  display: none;
}

.rating-label .rating-off {
  display: inline-flex;
}

.rating:hover label.rating-label .rating-on,
label.rating-label .rating-on,
label.rating-label.checked .rating-on,
div.rating-label.checked .rating-on {
  display: inline-flex;
}

.rating:hover label.rating-label .rating-off,
label.rating-label .rating-off,
label.rating-label.checked .rating-off,
div.rating-label.checked .rating-off {
  display: none;
}

label.rating-label:hover ~ label.rating-label .rating-on,
.rating-input:checked ~ .rating-label .rating-on {
  display: none;
}

label.rating-label:hover ~ label.rating-label .rating-off,
.rating-input:checked ~ .rating-label .rating-off {
  display: inline-flex;
}

.rating-label.indeterminate {
  position: relative;
}

.rating-label.indeterminate .rating-on {
  display: inline-flex;
  position: absolute;
  z-index: 1;
  overflow: hidden;
}

.rating-label.indeterminate .rating-off {
  display: inline-flex;
}

label.rating-label {
  cursor: pointer;
}

.scrollable,
.scrollable-y,
.scrollable-x,
.scrollable-hover,
.scrollable-y-hover,
.scrollable-x-hover,
.scrollable-auto,
.scrollable-y-auto,
.scrollable-x-auto {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  position: relative;
}

.scrollable::-webkit-scrollbar,
.scrollable-y::-webkit-scrollbar,
.scrollable-x::-webkit-scrollbar,
.scrollable-hover::-webkit-scrollbar,
.scrollable-y-hover::-webkit-scrollbar,
.scrollable-x-hover::-webkit-scrollbar,
.scrollable-auto::-webkit-scrollbar,
.scrollable-y-auto::-webkit-scrollbar,
.scrollable-x-auto::-webkit-scrollbar {
  width: 0.35rem;
  height: 0.35rem;
}

.scrollable::-webkit-scrollbar-track,
.scrollable-y::-webkit-scrollbar-track,
.scrollable-x::-webkit-scrollbar-track,
.scrollable-hover::-webkit-scrollbar-track,
.scrollable-y-hover::-webkit-scrollbar-track,
.scrollable-x-hover::-webkit-scrollbar-track,
.scrollable-auto::-webkit-scrollbar-track,
.scrollable-y-auto::-webkit-scrollbar-track,
.scrollable-x-auto::-webkit-scrollbar-track {
  background-color: transparent;
}

.scrollable::-webkit-scrollbar-thumb,
.scrollable-y::-webkit-scrollbar-thumb,
.scrollable-x::-webkit-scrollbar-thumb,
.scrollable-hover::-webkit-scrollbar-thumb,
.scrollable-y-hover::-webkit-scrollbar-thumb,
.scrollable-x-hover::-webkit-scrollbar-thumb,
.scrollable-auto::-webkit-scrollbar-thumb,
.scrollable-y-auto::-webkit-scrollbar-thumb,
.scrollable-x-auto::-webkit-scrollbar-thumb {
  border-radius: 1.25rem;
}

.scrollable::-webkit-scrollbar-corner,
.scrollable-y::-webkit-scrollbar-corner,
.scrollable-x::-webkit-scrollbar-corner,
.scrollable-hover::-webkit-scrollbar-corner,
.scrollable-y-hover::-webkit-scrollbar-corner,
.scrollable-x-hover::-webkit-scrollbar-corner,
.scrollable-auto::-webkit-scrollbar-corner,
.scrollable-y-auto::-webkit-scrollbar-corner,
.scrollable-x-auto::-webkit-scrollbar-corner {
  background-color: transparent;
}

.scrollable,
.scrollable-hover {
  overflow: scroll;
}

.scrollable-y,
.scrollable-y-hover {
  overflow-y: scroll;
}

.scrollable-x,
.scrollable-x-hover {
  overflow-x: scroll;
}

.scrollable-auto {
  overflow: auto;
}

.scrollable-y-auto {
  overflow-y: auto;
}

.scrollable-x-auto {
  overflow-x: auto;
}

.scrollable,
.scrollable-y,
.scrollable-x,
.scrollable-auto,
.scrollable-y-auto,
.scrollable-x-auto,
.scrollable-hover:hover,
.scrollable-y-hover:hover,
.scrollable-x-hover:hover {
  scrollbar-color: var(--tw-scrollbar-thumb-color, var(--tw-gray-200)) transparent;
}

.scrollable::-webkit-scrollbar-thumb,
.scrollable-y::-webkit-scrollbar-thumb,
.scrollable-x::-webkit-scrollbar-thumb,
.scrollable-auto::-webkit-scrollbar-thumb,
.scrollable-y-auto::-webkit-scrollbar-thumb,
.scrollable-x-auto::-webkit-scrollbar-thumb,
.scrollable-hover:hover::-webkit-scrollbar-thumb,
.scrollable-y-hover:hover::-webkit-scrollbar-thumb,
.scrollable-x-hover:hover::-webkit-scrollbar-thumb {
  background-color: var(--tw-scrollbar-thumb-color, var(--tw-gray-200));
}

.scrollable::-webkit-scrollbar-corner,
.scrollable-y::-webkit-scrollbar-corner,
.scrollable-x::-webkit-scrollbar-corner,
.scrollable-auto::-webkit-scrollbar-corner,
.scrollable-y-auto::-webkit-scrollbar-corner,
.scrollable-x-auto::-webkit-scrollbar-corner,
.scrollable-hover:hover::-webkit-scrollbar-corner,
.scrollable-y-hover:hover::-webkit-scrollbar-corner,
.scrollable-x-hover:hover::-webkit-scrollbar-corner {
  background-color: transparent;
}

@media (max-width: 1024px) {
  .scrollable,
  .scrollable-hover {
    overflow: auto;
  }

  .scrollable-y,
  .scrollable-y-hover {
    overflow-y: auto;
  }

  .scrollable-x,
  .scrollable-x-hover {
    overflow-x: auto;
  }
}

.progress {
  width: 100%;
  display: flex;
  min-height: 4px;
  overflow: hidden;
  background-color: var(--tw-gray-100);
  border-radius: 0.5rem;
}

.progress-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  text-align: center;
  white-space: nowrap;
  background-color: var(--tw-gray-300);
  border-radius: 0.5rem;
}

.progress-primary {
  background-color: var(--tw-primary-light);
}

.progress-primary .progress-bar {
  background-color: var(--tw-primary);
}

.progress-success {
  background-color: var(--tw-success-light);
}

.progress-success .progress-bar {
  background-color: var(--tw-success);
}

.progress-danger .progress-bar {
  background-color: var(--tw-danger);
}

.progress-warning .progress-bar {
  background-color: var(--tw-warning);
}

.progress-info .progress-bar {
  background-color: var(--tw-info);
}

.progress-dark .progress-bar {
  background-color: var(--tw-dark);
}

.apexcharts-text,
.apexcharts-title-text,
.apexcharts-legend-text {
  font-family: inherit !important;
}

.apexcharts-title-text {
  font-weight: 400;
}

.apexcharts-pie-label {
  font-size: 0.75rem;
  line-height: 1rem;
}

.apexcharts-toolbar {
  text-align: left !important;
}

.apexcharts-menu {
  border: 1px solid var(--tw-gray-200) !important;
  box-shadow: var(--tw-default-box-shadow) !important;
  background-color: var(--tw-light) !important;
  border-radius: 0.625rem !important;
  padding: 0.5rem 0 !important;
  overflow: hidden;
  min-width: 10rem !important;
}

.apexcharts-menu .apexcharts-menu-item {
  padding: 0.5rem 0.5rem !important;
}

.apexcharts-menu .apexcharts-menu-item:hover {
  background-color: var(--tw-gray-100) !important;
}

.apexcharts-tooltip {
  border: 1px solid var(--tw-gray-200) !important;
  box-shadow: var(--tw-default-box-shadow) !important;
  background-color: var(--tw-light) !important;
  border-radius: 0.625rem !important;
  color: var(--tw-gray-700) !important;
}

.apexcharts-tooltip .apexcharts-tooltip-title {
  padding: 0.25rem 0.5rem !important;
  background-color: transparent !important;
  font-size: 0.8125rem;
  line-height: 1.125rem;
  font-weight: 500;
  color: var(--tw-gray-800);
  border-bottom: 1px solid var(--tw-gray-200) !important;
}

.apexcharts-xaxistooltip {
  border: 1px solid var(--tw-gray-200) !important;
  box-shadow: var(--tw-default-box-shadow);
  background-color: var(--tw-light) !important;
  border-radius: 0.625rem;
  color: var(--tw-gray-900);
}

.apexcharts-xaxistooltip:before {
  border-bottom: 0 !important;
}

.apexcharts-legend {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.apexcharts-legend .apexcharts-legend-series {
  gap: 0.25rem;
  display: flex;
  align-items: center;
}

.apexcharts-legend .apexcharts-legend-series .apexcharts-legend-text {
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  color: var(--tw-gray-500);
}

.apexcharts-card-rounded .apexcharts-canvas svg {
  border-bottom-left-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
}

.apexcharts-rounded-sm .apexcharts-canvas svg {
  border-radius: 0.125rem;
}

.apexcharts-rounded .apexcharts-canvas svg {
  border-radius: 0.25rem;
}

.apexcharts-rounded-lg .apexcharts-canvas svg {
  border-radius: 0.5rem;
}

.apexcharts-rounded-xl .apexcharts-canvas svg {
  border-radius: 0.75rem;
}

.leaflet-container .leaflet-pane,
.leaflet-container .leaflet-top,
.leaflet-container .leaflet-bottom,
.leaflet-container .leaflet-control {
  z-index: 1 !important;
}

.leaflet-container .leaflet-popup-content-wrapper {
  border-radius: 0.75rem;
  text-align: center;
  background-color: var(--tw-dropdown-background-color);
}

.leaflet-container .leaflet-popup-content-wrapper .leaflet-popup-content {
  font-family: inherit;
  font-size: 0.8125rem;
}

.visible {
  visibility: visible;
}

.collapse {
  visibility: collapse;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

.inset-0 {
  inset: 0px;
}

.-right-0\.5 {
  right: -0.125rem;
}

.-top-0\.5 {
  top: -0.125rem;
}

.bottom-0 {
  bottom: 0px;
}

.bottom-0\.5 {
  bottom: 0.125rem;
}

.bottom-2 {
  bottom: 0.5rem;
}

.end-0 {
  inset-inline-end: 0px;
}

.end-0\.5 {
  inset-inline-end: 0.125rem;
}

.left-0 {
  left: 0px;
}

.left-1\/2 {
  left: 50%;
}

.left-16 {
  left: 4rem;
}

.left-2\/4 {
  left: 50%;
}

.left-\[93px\] {
  left: 93px;
}

.left-auto {
  left: auto;
}

.left-full {
  left: 100%;
}

.right-0 {
  right: 0px;
}

.right-0\.5 {
  right: 0.125rem;
}

.right-3 {
  right: 0.75rem;
}

.top-0 {
  top: 0px;
}

.top-0\.5 {
  top: 0.125rem;
}

.top-1\/2 {
  top: 50%;
}

.top-2 {
  top: 0.5rem;
}

.top-2\/4 {
  top: 50%;
}

.top-5 {
  top: 1.25rem;
}

.top-7 {
  top: 1.75rem;
}

.top-9 {
  top: 2.25rem;
}

.top-\[15\%\] {
  top: 15%;
}

.top-\[calc\(var\(--tw-header-height\)\+1\.875rem\)\] {
  top: calc(var(--tw-header-height) + 1.875rem);
}

.z-1 {
  z-index: 1;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.order-1 {
  order: 1;
}

.order-2 {
  order: 2;
}

.col-span-1 {
  grid-column: span 1 / span 1;
}

.col-span-2 {
  grid-column: span 2 / span 2;
}

.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

.mx-1\.5 {
  margin-left: 0.375rem;
  margin-right: 0.375rem;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.mx-5 {
  margin-left: 1.25rem;
  margin-right: 1.25rem;
}

.mx-7\.5 {
  margin-left: 1.875rem;
  margin-right: 1.875rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

.my-1\.5 {
  margin-top: 0.375rem;
  margin-bottom: 0.375rem;
}

.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.my-2\.5 {
  margin-top: 0.625rem;
  margin-bottom: 0.625rem;
}

.my-7\.5 {
  margin-top: 1.875rem;
  margin-bottom: 1.875rem;
}

.my-\[3\%\] {
  margin-top: 3%;
  margin-bottom: 3%;
}

.-ml-1 {
  margin-left: -0.25rem;
}

.-mt-3 {
  margin-top: -0.75rem;
}

.-mt-7\.5 {
  margin-top: -1.875rem;
}

.-mt-8 {
  margin-top: -2rem;
}

.-mt-px {
  margin-top: -1px;
}

.mb-0\.5 {
  margin-bottom: 0.125rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-1\.5 {
  margin-bottom: 0.375rem;
}

.mb-10 {
  margin-bottom: 2.5rem;
}

.mb-16 {
  margin-bottom: 4rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-2\.5 {
  margin-bottom: 0.625rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-3\.5 {
  margin-bottom: 0.875rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-4\.5 {
  margin-bottom: 1.125rem;
}

.mb-5 {
  margin-bottom: 1.25rem;
}

.mb-7 {
  margin-bottom: 1.75rem;
}

.mb-7\.5 {
  margin-bottom: 1.875rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mb-9 {
  margin-bottom: 2.25rem;
}

.mb-px {
  margin-bottom: 1px;
}

.me-0\.5 {
  margin-inline-end: 0.125rem;
}

.me-1 {
  margin-inline-end: 0.25rem;
}

.me-1\.5 {
  margin-inline-end: 0.375rem;
}

.me-2 {
  margin-inline-end: 0.5rem;
}

.ml-1 {
  margin-left: 0.25rem;
}

.ml-2\.5 {
  margin-left: 0.625rem;
}

.ml-3 {
  margin-left: 0.75rem;
}

.ml-5 {
  margin-left: 1.25rem;
}

.ml-7\.5 {
  margin-left: 1.875rem;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-2\.5 {
  margin-right: 0.625rem;
}

.mr-3 {
  margin-right: 0.75rem;
}

.mr-5 {
  margin-right: 1.25rem;
}

.mr-6 {
  margin-right: 1.5rem;
}

.mr-\[-10px\] {
  margin-right: -10px;
}

.ms-1 {
  margin-inline-start: 0.25rem;
}

.ms-2\.5 {
  margin-inline-start: 0.625rem;
}

.ms-5 {
  margin-inline-start: 1.25rem;
}

.mt-0\.5 {
  margin-top: 0.125rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-2\.5 {
  margin-top: 0.625rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-5 {
  margin-top: 1.25rem;
}

.mt-7 {
  margin-top: 1.75rem;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.grid {
  display: grid;
}

.contents {
  display: contents;
}

.hidden {
  display: none;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}

.size-0\.75 {
  width: 0.1875rem;
  height: 0.1875rem;
}

.size-1 {
  width: 0.25rem;
  height: 0.25rem;
}

.size-1\.5 {
  width: 0.375rem;
  height: 0.375rem;
}

.size-10 {
  width: 2.5rem;
  height: 2.5rem;
}

.size-11 {
  width: 2.75rem;
  height: 2.75rem;
}

.size-12 {
  width: 3rem;
  height: 3rem;
}

.size-14 {
  width: 3.5rem;
  height: 3.5rem;
}

.size-16 {
  width: 4rem;
  height: 4rem;
}

.size-2 {
  width: 0.5rem;
  height: 0.5rem;
}

.size-2\.5 {
  width: 0.625rem;
  height: 0.625rem;
}

.size-20 {
  width: 5rem;
  height: 5rem;
}

.size-3 {
  width: 0.75rem;
  height: 0.75rem;
}

.size-3\.5 {
  width: 0.875rem;
  height: 0.875rem;
}

.size-4 {
  width: 1rem;
  height: 1rem;
}

.size-5 {
  width: 1.25rem;
  height: 1.25rem;
}

.size-6 {
  width: 1.5rem;
  height: 1.5rem;
}

.size-7 {
  width: 1.75rem;
  height: 1.75rem;
}

.size-7\.5 {
  width: 1.875rem;
  height: 1.875rem;
}

.size-8 {
  width: 2rem;
  height: 2rem;
}

.size-9 {
  width: 2.25rem;
  height: 2.25rem;
}

.size-\[100px\] {
  width: 100px;
  height: 100px;
}

.size-\[11px\] {
  width: 11px;
  height: 11px;
}

.size-\[120px\] {
  width: 120px;
  height: 120px;
}

.size-\[140px\] {
  width: 140px;
  height: 140px;
}

.size-\[18px\] {
  width: 18px;
  height: 18px;
}

.size-\[30px\] {
  width: 30px;
  height: 30px;
}

.size-\[4\.8px\] {
  width: 4.8px;
  height: 4.8px;
}

.size-\[44px\] {
  width: 44px;
  height: 44px;
}

.size-\[45px\] {
  width: 45px;
  height: 45px;
}

.size-\[50px\] {
  width: 50px;
  height: 50px;
}

.size-\[5px\] {
  width: 5px;
  height: 5px;
}

.size-\[60px\] {
  width: 60px;
  height: 60px;
}

.size-\[70px\] {
  width: 70px;
  height: 70px;
}

.size-\[90px\] {
  width: 90px;
  height: 90px;
}

.h-1\.5 {
  height: 0.375rem;
}

.h-10 {
  height: 2.5rem;
}

.h-11 {
  height: 2.75rem;
}

.h-12 {
  height: 3rem;
}

.h-2 {
  height: 0.5rem;
}

.h-20 {
  height: 5rem;
}

.h-24 {
  height: 6rem;
}

.h-3\.5 {
  height: 0.875rem;
}

.h-4 {
  height: 1rem;
}

.h-40 {
  height: 10rem;
}

.h-44 {
  height: 11rem;
}

.h-48 {
  height: 12rem;
}

.h-5 {
  height: 1.25rem;
}

.h-56 {
  height: 14rem;
}

.h-6 {
  height: 1.5rem;
}

.h-7 {
  height: 1.75rem;
}

.h-9 {
  height: 2.25rem;
}

.h-\[1\.875rem\] {
  height: 1.875rem;
}

.h-\[100px\] {
  height: 100px;
}

.h-\[120px\] {
  height: 120px;
}

.h-\[170px\] {
  height: 170px;
}

.h-\[240px\] {
  height: 240px;
}

.h-\[28px\] {
  height: 28px;
}

.h-\[30px\] {
  height: 30px;
}

.h-\[35px\] {
  height: 35px;
}

.h-\[36px\] {
  height: 36px;
}

.h-\[50px\] {
  height: 50px;
}

.h-\[95\%\] {
  height: 95%;
}

.h-auto {
  height: auto;
}

.h-full {
  height: 100%;
}

.max-h-20 {
  max-height: 5rem;
}

.max-h-36 {
  max-height: 9rem;
}

.max-h-44 {
  max-height: 11rem;
}

.max-h-5 {
  max-height: 1.25rem;
}

.max-h-\[100px\] {
  max-height: 100px;
}

.max-h-\[113px\] {
  max-height: 113px;
}

.max-h-\[120px\] {
  max-height: 120px;
}

.max-h-\[130px\] {
  max-height: 130px;
}

.max-h-\[140px\] {
  max-height: 140px;
}

.max-h-\[150px\] {
  max-height: 150px;
}

.max-h-\[160px\] {
  max-height: 160px;
}

.max-h-\[170px\] {
  max-height: 170px;
}

.max-h-\[180px\] {
  max-height: 180px;
}

.max-h-\[200px\] {
  max-height: 200px;
}

.max-h-\[230px\] {
  max-height: 230px;
}

.max-h-\[25px\] {
  max-height: 25px;
}

.max-h-\[300px\] {
  max-height: 300px;
}

.max-h-\[400px\] {
  max-height: 400px;
}

.max-h-\[55px\] {
  max-height: 55px;
}

.min-h-52 {
  min-height: 13rem;
}

.min-h-80 {
  min-height: 20rem;
}

.min-h-\[22px\] {
  min-height: 22px;
}

.min-h-\[340px\] {
  min-height: 340px;
}

.min-h-\[400px\] {
  min-height: 400px;
}

.w-1\.5 {
  width: 0.375rem;
}

.w-10 {
  width: 2.5rem;
}

.w-11 {
  width: 2.75rem;
}

.w-14 {
  width: 3.5rem;
}

.w-16 {
  width: 4rem;
}

.w-20 {
  width: 5rem;
}

.w-24 {
  width: 6rem;
}

.w-28 {
  width: 7rem;
}

.w-36 {
  width: 9rem;
}

.w-40 {
  width: 10rem;
}

.w-48 {
  width: 12rem;
}

.w-5 {
  width: 1.25rem;
}

.w-7 {
  width: 1.75rem;
}

.w-8 {
  width: 2rem;
}

.w-80 {
  width: 20rem;
}

.w-9 {
  width: 2.25rem;
}

.w-\[1\.875rem\] {
  width: 1.875rem;
}

.w-\[100px\] {
  width: 100px;
}

.w-\[125px\] {
  width: 125px;
}

.w-\[170px\] {
  width: 170px;
}

.w-\[185px\] {
  width: 185px;
}

.w-\[200px\] {
  width: 200px;
}

.w-\[20px\] {
  width: 20px;
}

.w-\[225px\] {
  width: 225px;
}

.w-\[230px\] {
  width: 230px;
}

.w-\[240px\] {
  width: 240px;
}

.w-\[26px\] {
  width: 26px;
}

.w-\[280px\] {
  width: 280px;
}

.w-\[285px\] {
  width: 285px;
}

.w-\[30px\] {
  width: 30px;
}

.w-\[350px\] {
  width: 350px;
}

.w-\[50px\] {
  width: 50px;
}

.w-\[60px\] {
  width: 60px;
}

.w-\[6px\] {
  width: 6px;
}

.w-\[70px\] {
  width: 70px;
}

.w-full {
  width: 100%;
}

.min-w-12 {
  min-width: 3rem;
}

.min-w-14 {
  min-width: 3.5rem;
}

.min-w-16 {
  min-width: 4rem;
}

.min-w-20 {
  min-width: 5rem;
}

.min-w-24 {
  min-width: 6rem;
}

.min-w-28 {
  min-width: 7rem;
}

.min-w-32 {
  min-width: 8rem;
}

.min-w-36 {
  min-width: 9rem;
}

.min-w-40 {
  min-width: 10rem;
}

.min-w-44 {
  min-width: 11rem;
}

.min-w-48 {
  min-width: 12rem;
}

.min-w-52 {
  min-width: 13rem;
}

.min-w-56 {
  min-width: 14rem;
}

.min-w-60 {
  min-width: 15rem;
}

.min-w-64 {
  min-width: 16rem;
}

.min-w-72 {
  min-width: 18rem;
}

.min-w-\[1000px\] {
  min-width: 1000px;
}

.min-w-\[100px\] {
  min-width: 100px;
}

.min-w-\[103px\] {
  min-width: 103px;
}

.min-w-\[110px\] {
  min-width: 110px;
}

.min-w-\[120px\] {
  min-width: 120px;
}

.min-w-\[122px\] {
  min-width: 122px;
}

.min-w-\[130px\] {
  min-width: 130px;
}

.min-w-\[135px\] {
  min-width: 135px;
}

.min-w-\[137px\] {
  min-width: 137px;
}

.min-w-\[150px\] {
  min-width: 150px;
}

.min-w-\[160px\] {
  min-width: 160px;
}

.min-w-\[165px\] {
  min-width: 165px;
}

.min-w-\[175px\] {
  min-width: 175px;
}

.min-w-\[180px\] {
  min-width: 180px;
}

.min-w-\[190px\] {
  min-width: 190px;
}

.min-w-\[200px\] {
  min-width: 200px;
}

.min-w-\[206px\] {
  min-width: 206px;
}

.min-w-\[220px\] {
  min-width: 220px;
}

.min-w-\[224px\] {
  min-width: 224px;
}

.min-w-\[225px\] {
  min-width: 225px;
}

.min-w-\[240px\] {
  min-width: 240px;
}

.min-w-\[250px\] {
  min-width: 250px;
}

.min-w-\[260px\] {
  min-width: 260px;
}

.min-w-\[300px\] {
  min-width: 300px;
}

.min-w-\[98px\] {
  min-width: 98px;
}

.min-w-full {
  min-width: 100%;
}

.max-w-16 {
  max-width: 4rem;
}

.max-w-24 {
  max-width: 6rem;
}

.max-w-2xl {
  max-width: 42rem;
}

.max-w-32 {
  max-width: 8rem;
}

.max-w-48 {
  max-width: 12rem;
}

.max-w-56 {
  max-width: 14rem;
}

.max-w-64 {
  max-width: 16rem;
}

.max-w-72 {
  max-width: 18rem;
}

.max-w-\[125px\] {
  max-width: 125px;
}

.max-w-\[15\%\] {
  max-width: 15%;
}

.max-w-\[170px\] {
  max-width: 170px;
}

.max-w-\[175px\] {
  max-width: 175px;
}

.max-w-\[200px\] {
  max-width: 200px;
}

.max-w-\[220px\] {
  max-width: 220px;
}

.max-w-\[25\%\] {
  max-width: 25%;
}

.max-w-\[250px\] {
  max-width: 250px;
}

.max-w-\[280px\] {
  max-width: 280px;
}

.max-w-\[320px\] {
  max-width: 320px;
}

.max-w-\[370px\] {
  max-width: 370px;
}

.max-w-\[380px\] {
  max-width: 380px;
}

.max-w-\[440px\] {
  max-width: 440px;
}

.max-w-\[450px\] {
  max-width: 450px;
}

.max-w-\[460px\] {
  max-width: 460px;
}

.max-w-\[500px\] {
  max-width: 500px;
}

.max-w-\[60\%\] {
  max-width: 60%;
}

.max-w-\[600px\] {
  max-width: 600px;
}

.max-w-full {
  max-width: 100%;
}

.max-w-none {
  max-width: none;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-none {
  flex: none;
}

.shrink-0 {
  flex-shrink: 0;
}

.grow {
  flex-grow: 1;
}

.grow-0 {
  flex-grow: 0;
}

.table-auto {
  table-layout: auto;
}

.table-fixed {
  table-layout: fixed;
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
    skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-2\/4 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
    skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
    skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-2\/4 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
    skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-1\/2 {
  --tw-translate-x: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
    skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
    skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-1\/2 {
  --tw-translate-y: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
    skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
    skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.cursor-pointer {
  cursor: pointer;
}

.resize {
  resize: both;
}

.appearance-none {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-rows-2 {
  grid-template-rows: repeat(2, minmax(0, 1fr));
}

.flex-row {
  flex-direction: row;
}

.flex-col {
  flex-direction: column;
}

.flex-col-reverse {
  flex-direction: column-reverse;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.place-content-center {
  place-content: center;
}

.place-content-between {
  place-content: space-between;
}

.place-items-center {
  place-items: center;
}

.content-between {
  align-content: space-between;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-baseline {
  align-items: baseline;
}

.items-stretch {
  align-items: stretch;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-0 {
  gap: 0px;
}

.gap-0\.5 {
  gap: 0.125rem;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-1\.25 {
  gap: 0.3rem;
}

.gap-1\.5 {
  gap: 0.375rem;
}

.gap-10 {
  gap: 2.5rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-2\.5 {
  gap: 0.625rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-3\.5 {
  gap: 0.875rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-5 {
  gap: 1.25rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-7 {
  gap: 1.75rem;
}

.gap-7\.5 {
  gap: 1.875rem;
}

.gap-\[10px\] {
  gap: 10px;
}

.gap-\[14px\] {
  gap: 14px;
}

.gap-\[5px\] {
  gap: 5px;
}

.gap-px {
  gap: 1px;
}

.gap-x-4 {
  -moz-column-gap: 1rem;
  column-gap: 1rem;
}

.gap-y-3\.5 {
  row-gap: 0.875rem;
}

.gap-y-5 {
  row-gap: 1.25rem;
}

.-space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(-0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(-0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}

.divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
  border-color: var(--tw-gray-200);
}

.place-self-end {
  place-self: end;
}

.justify-self-end {
  justify-self: end;
}

.justify-self-center {
  justify-self: center;
}

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.text-nowrap {
  text-wrap: nowrap;
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-\[30px\] {
  border-radius: 30px;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.rounded-sm {
  border-radius: 0.125rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-t-lg {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}

.rounded-t-xl {
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}

.rounded-bl-none {
  border-bottom-left-radius: 0px;
}

.rounded-br-none {
  border-bottom-right-radius: 0px;
}

.border {
  border-width: 1px;
}

.border-0 {
  border-width: 0px;
}

.border-2 {
  border-width: 2px;
}

.border-3 {
  border-width: 3px;
}

.border-\[0\.5px\] {
  border-width: 0.5px;
}

.\!border-b-0 {
  border-bottom-width: 0px !important;
}

.border-b {
  border-bottom-width: 1px;
}

.border-b-0 {
  border-bottom-width: 0px;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-l {
  border-left-width: 1px;
}

.border-l-2 {
  border-left-width: 2px;
}

.border-r {
  border-right-width: 1px;
}

.border-t {
  border-top-width: 1px;
}

.border-dashed {
  border-style: dashed;
}

.border-none {
  border-style: none;
}

.border-brand {
  border-color: var(--tw-brand);
}

.border-brand-clarity {
  border-color: var(--tw-brand-clarity);
}

.border-danger {
  border-color: var(--tw-danger);
}

.border-danger-clarity {
  border-color: var(--tw-danger-clarity);
}

.border-gray-200 {
  border-color: var(--tw-gray-200);
}

.border-gray-300 {
  border-color: var(--tw-gray-300);
}

.border-gray-400 {
  border-color: var(--tw-gray-400);
}

.border-primary {
  border-color: var(--tw-primary);
}

.border-success {
  border-color: var(--tw-success);
}

.border-success-clarity {
  border-color: var(--tw-success-clarity);
}

.border-transparent {
  border-color: transparent;
}

.border-warning {
  border-color: var(--tw-warning);
}

.border-b-brand-clarity {
  border-bottom-color: var(--tw-brand-clarity);
}

.border-b-gray-200 {
  border-bottom-color: var(--tw-gray-200);
}

.border-b-transparent {
  border-bottom-color: transparent;
}

.border-l-gray-200 {
  border-left-color: var(--tw-gray-200);
}

.border-l-gray-300 {
  border-left-color: var(--tw-gray-300);
}

.border-r-gray-200 {
  border-right-color: var(--tw-gray-200);
}

.border-r-gray-300 {
  border-right-color: var(--tw-gray-300);
}

.border-t-gray-200 {
  border-top-color: var(--tw-gray-200);
}

.bg-\[\#fefefe\] {
  --tw-bg-opacity: 1;
  background-color: rgb(254 254 254 / var(--tw-bg-opacity));
}

.bg-brand {
  background-color: var(--tw-brand);
}

.bg-brand-light {
  background-color: var(--tw-brand-light);
}

.bg-danger {
  background-color: var(--tw-danger);
}

.bg-danger-light {
  background-color: var(--tw-danger-light);
}

.bg-dark-clarity {
  background-color: var(--tw-dark-clarity);
}

.bg-gray-100 {
  background-color: var(--tw-gray-100);
}

.bg-gray-300 {
  background-color: var(--tw-gray-300);
}

.bg-gray-400 {
  background-color: var(--tw-gray-400);
}

.bg-gray-500 {
  background-color: var(--tw-gray-500);
}

.bg-gray-600 {
  background-color: var(--tw-gray-600);
}

.bg-gray-900 {
  background-color: var(--tw-gray-900);
}

.bg-info {
  background-color: var(--tw-info);
}

.bg-info-light {
  background-color: var(--tw-info-light);
}

.bg-light {
  background-color: var(--tw-light);
}

.bg-light-active {
  background-color: var(--tw-light-active);
}

.bg-primary {
  background-color: var(--tw-primary);
}

.bg-primary-light {
  background-color: var(--tw-primary-light);
}

.bg-secondary-clarity {
  background-color: var(--tw-secondary-clarity);
}

.bg-success {
  background-color: var(--tw-success);
}

.bg-success-light {
  background-color: var(--tw-success-light);
}

.bg-transparent {
  background-color: transparent;
}

.bg-warning {
  background-color: var(--tw-warning);
}

.bg-warning-light {
  background-color: var(--tw-warning-light);
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.bg-gradient-to-t {
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}

.from-light {
  --tw-gradient-from: var(--tw-light) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.to-transparent {
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}

.bg-\[length\:500px\] {
  background-size: 500px;
}

.bg-\[length\:550px\] {
  background-size: 550px;
}

.bg-\[length\:600px\] {
  background-size: 600px;
}

.bg-\[length\:650px\] {
  background-size: 650px;
}

.bg-\[length\:660px_310px\] {
  background-size: 660px 310px;
}

.bg-\[length\:700px\] {
  background-size: 700px;
}

.bg-\[length\:750px\] {
  background-size: 750px;
}

.bg-\[length\:80\%\] {
  background-size: 80%;
}

.bg-cover {
  background-size: cover;
}

.bg-\[center_right_-8rem\] {
  background-position: center right -8rem;
}

.bg-\[center_top_1\.3rem\] {
  background-position: center top 1.3rem;
}

.bg-\[right_top_-1\.7rem\] {
  background-position: right top -1.7rem;
}

.bg-center {
  background-position: center;
}

.bg-top {
  background-position: top;
}

.bg-no-repeat {
  background-repeat: no-repeat;
}

.fill-brand-light {
  fill: var(--tw-brand-light);
}

.fill-danger-light {
  fill: var(--tw-danger-light);
}

.fill-gray-100 {
  fill: var(--tw-gray-100);
}

.fill-info-light {
  fill: var(--tw-info-light);
}

.fill-light {
  fill: var(--tw-light);
}

.fill-primary-light {
  fill: var(--tw-primary-light);
}

.fill-success-light {
  fill: var(--tw-success-light);
}

.stroke-brand-clarity {
  stroke: var(--tw-brand-clarity);
}

.stroke-danger-clarity {
  stroke: var(--tw-danger-clarity);
}

.stroke-gray-300 {
  stroke: var(--tw-gray-300);
}

.stroke-info-clarity {
  stroke: var(--tw-info-clarity);
}

.stroke-primary-clarity {
  stroke: var(--tw-primary-clarity);
}

.stroke-success-clarity {
  stroke: var(--tw-success-clarity);
}

.\!p-5 {
  padding: 1.25rem !important;
}

.p-0 {
  padding: 0px;
}

.p-1\.5 {
  padding: 0.375rem;
}

.p-10 {
  padding: 2.5rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-2\.5 {
  padding: 0.625rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-3\.5 {
  padding: 0.875rem;
}

.p-4 {
  padding: 1rem;
}

.p-5 {
  padding: 1.25rem;
}

.p-7 {
  padding: 1.75rem;
}

.p-7\.5 {
  padding: 1.875rem;
}

.p-8 {
  padding: 2rem;
}

.\!px-5 {
  padding-left: 1.25rem !important;
  padding-right: 1.25rem !important;
}

.\!py-3\.5 {
  padding-top: 0.875rem !important;
  padding-bottom: 0.875rem !important;
}

.\!py-5\.5 {
  padding-top: 1.375rem !important;
  padding-bottom: 1.375rem !important;
}

.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}

.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

.px-2\.75 {
  padding-left: 0.688rem;
  padding-right: 0.688rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-3\.5 {
  padding-left: 0.875rem;
  padding-right: 0.875rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-7\.5 {
  padding-left: 1.875rem;
  padding-right: 1.875rem;
}

.px-9 {
  padding-left: 2.25rem;
  padding-right: 2.25rem;
}

.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}

.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-2\.25 {
  padding-top: 0.563rem;
  padding-bottom: 0.563rem;
}

.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-3\.5 {
  padding-top: 0.875rem;
  padding-bottom: 0.875rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-4\.5 {
  padding-top: 1.125rem;
  padding-bottom: 1.125rem;
}

.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.py-7\.5 {
  padding-top: 1.875rem;
  padding-bottom: 1.875rem;
}

.py-9 {
  padding-top: 2.25rem;
  padding-bottom: 2.25rem;
}

.py-\[5\%\] {
  padding-top: 5%;
  padding-bottom: 5%;
}

.py-\[6px\] {
  padding-top: 6px;
  padding-bottom: 6px;
}

.py-\[8px\] {
  padding-top: 8px;
  padding-bottom: 8px;
}

.\!pb-6 {
  padding-bottom: 1.5rem !important;
}

.\!pr-7\.5 {
  padding-right: 1.875rem !important;
}

.\!pt-7\.5 {
  padding-top: 1.875rem !important;
}

.pb-0 {
  padding-bottom: 0px;
}

.pb-0\.5 {
  padding-bottom: 0.125rem;
}

.pb-1 {
  padding-bottom: 0.25rem;
}

.pb-1\.5 {
  padding-bottom: 0.375rem;
}

.pb-10 {
  padding-bottom: 2.5rem;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pb-2\.5 {
  padding-bottom: 0.625rem;
}

.pb-3 {
  padding-bottom: 0.75rem;
}

.pb-3\.5 {
  padding-bottom: 0.875rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pb-5 {
  padding-bottom: 1.25rem;
}

.pb-6 {
  padding-bottom: 1.5rem;
}

.pb-7 {
  padding-bottom: 1.75rem;
}

.pb-7\.5 {
  padding-bottom: 1.875rem;
}

.pb-px {
  padding-bottom: 1px;
}

.pe-3 {
  padding-inline-end: 0.75rem;
}

.pe-4 {
  padding-inline-end: 1rem;
}

.pe-6 {
  padding-inline-end: 1.5rem;
}

.pe-7\.5 {
  padding-inline-end: 1.875rem;
}

.pl-2 {
  padding-left: 0.5rem;
}

.pl-2\.5 {
  padding-left: 0.625rem;
}

.pl-5 {
  padding-left: 1.25rem;
}

.pl-6 {
  padding-left: 1.5rem;
}

.pl-8 {
  padding-left: 2rem;
}

.pl-10 {
  padding-left: 2.5rem;
}

.pl-\[10px\] {
  padding-left: 10px;
}

.pl-\[22px\] {
  padding-left: 22px;
}

.pr-1 {
  padding-right: 0.25rem;
}

.pr-10 {
  padding-right: 2.5rem;
}

.pr-2 {
  padding-right: 0.5rem;
}

.pr-2\.5 {
  padding-right: 0.625rem;
}

.pr-3 {
  padding-right: 0.75rem;
}

.pr-\[10px\] {
  padding-right: 10px;
}

.ps-12 {
  padding-inline-start: 3rem;
}

.ps-3 {
  padding-inline-start: 0.75rem;
}

.ps-8 {
  padding-inline-start: 2rem;
}

.ps-px {
  padding-inline-start: 1px;
}

.pt-0 {
  padding-top: 0px;
}

.pt-1 {
  padding-top: 0.25rem;
}

.pt-1\.5 {
  padding-top: 0.375rem;
}

.pt-2 {
  padding-top: 0.5rem;
}

.pt-2\.25 {
  padding-top: 0.563rem;
}

.pt-2\.5 {
  padding-top: 0.625rem;
}

.pt-3 {
  padding-top: 0.75rem;
}

.pt-3\.5 {
  padding-top: 0.875rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-5 {
  padding-top: 1.25rem;
}

.pt-7\.5 {
  padding-top: 1.875rem;
}

.pt-px {
  padding-top: 1px;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-start {
  text-align: start;
}

.align-middle {
  vertical-align: middle;
}

.align-bottom {
  vertical-align: bottom;
}

.text-1\.5xl {
  font-size: 1.375rem;
  line-height: 1.8125rem;
}

.text-2\.5xl {
  font-size: 1.625rem;
  line-height: 2.125rem;
}

.text-2sm {
  font-size: 0.8125rem;
  line-height: 1.125rem;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-2xs {
  font-size: 0.6875rem;
  line-height: 0.75rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-3xs {
  font-size: 0.625rem;
  line-height: 0.75rem;
}

.text-4xs {
  font-size: 0.5625rem;
  line-height: 0.6875rem;
}

.text-\[0\.75rem\] {
  font-size: 0.75rem;
}

.text-\[1\.875rem\] {
  font-size: 1.875rem;
}

.text-\[2\.25rem\] {
  font-size: 2.25rem;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-md {
  font-size: 0.9375rem;
  line-height: 1.375rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.uppercase {
  text-transform: uppercase;
}

.italic {
  font-style: italic;
}

.leading-3 {
  line-height: 0.75rem;
}

.leading-4 {
  line-height: 1rem;
}

.leading-5 {
  line-height: 1.25rem;
}

.leading-5\.5 {
  line-height: 1.375rem;
}

.leading-6 {
  line-height: 1.5rem;
}

.leading-\[14px\] {
  line-height: 14px;
}

.leading-\[22px\] {
  line-height: 22px;
}

.leading-\[32px\] {
  line-height: 32px;
}

.leading-none {
  line-height: 1;
}

.tracking-tight {
  letter-spacing: -0.025em;
}

.text-brand {
  color: var(--tw-brand);
}

.text-danger {
  color: var(--tw-danger);
}

.text-danger-inverse {
  color: var(--tw-danger-inverse);
}

.text-gray-300 {
  color: var(--tw-gray-300);
}

.text-gray-400 {
  color: var(--tw-gray-400);
}

.text-gray-500 {
  color: var(--tw-gray-500);
}

.text-gray-600 {
  color: var(--tw-gray-600);
}

.text-gray-700 {
  color: var(--tw-gray-700);
}

.text-gray-800 {
  color: var(--tw-gray-800);
}

.text-gray-900 {
  color: var(--tw-gray-900);
}

.text-info {
  color: var(--tw-info);
}

.text-info-inverse {
  color: var(--tw-info-inverse);
}

.text-light {
  color: var(--tw-light);
}

.text-primary {
  color: var(--tw-primary);
}

.text-primary-inverse {
  color: var(--tw-primary-inverse);
}

.text-success {
  color: var(--tw-success);
}

.text-success-inverse {
  color: var(--tw-success-inverse);
}

.text-warning {
  color: var(--tw-warning);
}

.text-warning-inverse {
  color: var(--tw-warning-inverse);
}

.opacity-25 {
  opacity: 0.25;
}

.opacity-75 {
  opacity: 0.75;
}

.opacity-80 {
  opacity: 0.8;
}

.shadow-card {
  --tw-shadow: var(--tw-card-box-shadow);
  --tw-shadow-colored: var(--tw-card-box-shadow);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-default {
  --tw-shadow: var(--tw-default-box-shadow);
  --tw-shadow-colored: var(--tw-default-box-shadow);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-none {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.outline {
  outline-style: solid;
}

.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-danger-clarity {
  --tw-ring-color: var(--tw-danger-clarity);
}

.ring-danger-light {
  --tw-ring-color: var(--tw-danger-light);
}

.ring-gray-200 {
  --tw-ring-color: var(--tw-gray-200);
}

.ring-gray-300 {
  --tw-ring-color: var(--tw-gray-300);
}

.ring-info-clarity {
  --tw-ring-color: var(--tw-info-clarity);
}

.ring-info-light {
  --tw-ring-color: var(--tw-info-light);
}

.ring-light {
  --tw-ring-color: var(--tw-light);
}

.ring-light-light {
  --tw-ring-color: var(--tw-light-light);
}

.ring-primary-clarity {
  --tw-ring-color: var(--tw-primary-clarity);
}

.ring-primary-light {
  --tw-ring-color: var(--tw-primary-light);
}

.ring-success-clarity {
  --tw-ring-color: var(--tw-success-clarity);
}

.ring-success-light {
  --tw-ring-color: var(--tw-success-light);
}

.ring-warning-clarity {
  --tw-ring-color: var(--tw-warning-clarity);
}

.ring-warning-light {
  --tw-ring-color: var(--tw-warning-light);
}

.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert)
    var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert)
    var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.\[background-position\:121\%_41\%\] {
  background-position: 121% 41%;
}

.\[background-position\:175\%_25\%\] {
  background-position: 175% 25%;
}

.\[background-position\:220\%_44\%\] {
  background-position: 220% 44%;
}

/* Demos */

/* Variables */

.demo1 {
  --tw-sidebar-transition-duration: 0.3s;
  --tw-sidebar-transition-timing: ease;
  --tw-sidebar-width: 280px;
  --tw-sidebar-defualt-width: 280px;
  --tw-header-height: 70px;
}

@media (max-width: 1024px) {
  .demo1 {
    --tw-sidebar-width: 280px;
    --tw-header-height: 60px;
  }
}

/* Base */

.demo1 .header {
  height: var(--tw-header-height);
}

.demo1 .sidebar {
  width: var(--tw-sidebar-width);
}

.demo1.header-fixed .wrapper {
  padding-top: var(--tw-header-height);
}

/* Desktop mode */

@media (min-width: 1024px) {
  .demo1 .sidebar {
    width: var(--tw-sidebar-width);
    transition: width var(--tw-sidebar-transition-duration) var(--tw-sidebar-transition-timing);
  }

  .demo1 .sidebar .sidebar-header {
    height: var(--tw-header-height);
  }

  .demo1 .sidebar .sidebar-wrapper {
    width: var(--tw-sidebar-defualt-width);
  }

  .demo1 .sidebar .sidebar-logo {
    width: var(--tw-sidebar-defualt-width);
  }

  .demo1 .sidebar .small-logo {
    display: none;
  }

  .demo1.sidebar-fixed .wrapper {
    padding-left: var(--tw-sidebar-width);
    transition: padding-left var(--tw-sidebar-transition-duration) var(--tw-sidebar-transition-timing);
  }

  .demo1.sidebar-fixed.header-fixed .header {
    left: var(--tw-sidebar-width);
    transition: left var(--tw-sidebar-transition-duration) var(--tw-sidebar-transition-timing);
  }

  .demo1.sidebar-fixed.header-fixed .wrapper {
    padding-top: var(--tw-header-height);
  }

  .demo1.sidebar-collapse {
    --tw-sidebar-width: 80px;
  }

  .demo1.sidebar-collapse .sidebar {
    transition: width var(--tw-sidebar-transition-duration) var(--tw-sidebar-transition-timing);
  }

  .demo1.sidebar-collapse .sidebar.animating {
    pointer-events: none;
  }

  .demo1.sidebar-collapse .sidebar:hover:not(.animating) {
    width: var(--tw-sidebar-defualt-width);
    transition: width var(--tw-sidebar-transition-duration) var(--tw-sidebar-transition-timing);
  }

  .demo1.sidebar-collapse .sidebar:not(:hover) .default-logo {
    display: none;
  }

  .demo1.sidebar-collapse .sidebar:not(:hover) .small-logo {
    display: flex;
  }

  .demo1.sidebar-collapse .sidebar:not(:hover) .menu > .menu-item > .menu-link .menu-title,
  .demo1.sidebar-collapse .sidebar:not(:hover) .menu > .menu-item > .menu-link .menu-arrow,
  .demo1.sidebar-collapse .sidebar:not(:hover) .menu > .menu-item > .menu-link .menu-badge,
  .demo1.sidebar-collapse .sidebar:not(:hover) .menu > .menu-item > .menu-label .menu-title,
  .demo1.sidebar-collapse .sidebar:not(:hover) .menu > .menu-item > .menu-label .menu-arrow,
  .demo1.sidebar-collapse .sidebar:not(:hover) .menu > .menu-item > .menu-label .menu-badge {
    display: none;
  }

  .demo1.sidebar-collapse .sidebar:not(:hover) .menu > .menu-item > .menu-accordion {
    display: none;
  }

  .demo1.sidebar-collapse .sidebar:not(:hover) .menu > .menu-item > .menu-heading {
    visibility: hidden;
    position: relative;
  }

  .demo1.sidebar-collapse .sidebar:not(:hover) .menu > .menu-item > .menu-heading::before {
    content: '...';
    color: currentColor;
    font-size: inherit;
    position: absolute;
    visibility: visible;
    display: inline-block;
    bottom: 50%;
    left: 0;
    margin-left: 0.225rem;
    transform: translateX(100%);
  }

  .demo1.sidebar-collapse .sidebar .sidebar-content {
    overflow: hidden;
  }
}

.\[\&_tr\:nth-of-type\(2\)\>td\:first-child\]\:card-rounded-tl tr:nth-of-type(2) > td:first-child {
  border-top-left-radius: 0.75rem;
}

.\[\&_tr\:nth-of-type\(2\)\>td\]\:table-border-t tr:nth-of-type(2) > td {
  border-top: var(--tw-table-border);
}

.placeholder\:text-gray-700::-moz-placeholder {
  color: var(--tw-gray-700);
}

.placeholder\:text-gray-700::placeholder {
  color: var(--tw-gray-700);
}

.before\:absolute::before {
  content: var(--tw-content);
  position: absolute;
}

.before\:bottom-0::before {
  content: var(--tw-content);
  bottom: 0px;
}

.before\:left-\[11px\]::before {
  content: var(--tw-content);
  left: 11px;
}

.before\:left-\[20px\]::before {
  content: var(--tw-content);
  left: 20px;
}

.before\:left-\[32px\]::before {
  content: var(--tw-content);
  left: 32px;
}

.before\:left-px::before {
  content: var(--tw-content);
  left: 1px;
}

.before\:top-0::before {
  content: var(--tw-content);
  top: 0px;
}

.before\:size-1\.5::before {
  content: var(--tw-content);
  width: 0.375rem;
  height: 0.375rem;
}

.before\:size-\[6px\]::before {
  content: var(--tw-content);
  width: 6px;
  height: 6px;
}

.before\:-translate-x-1\/2::before {
  content: var(--tw-content);
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
    skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.before\:-translate-x-2\/4::before {
  content: var(--tw-content);
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
    skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.before\:-translate-y-1\/2::before {
  content: var(--tw-content);
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
    skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.before\:-translate-y-2\/4::before {
  content: var(--tw-content);
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
    skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.before\:rounded-full::before {
  content: var(--tw-content);
  border-radius: 9999px;
}

.before\:border-l::before {
  content: var(--tw-content);
  border-left-width: 1px;
}

.before\:border-gray-200::before {
  content: var(--tw-content);
  border-color: var(--tw-gray-200);
}

.last\:mr-5:last-child {
  margin-right: 1.25rem;
}

.last-of-type\:hidden:last-of-type {
  display: none;
}

.hover\:z-5:hover {
  z-index: 5;
}

.hover\:rounded-lg:hover {
  border-radius: 0.5rem;
}

.hover\:bg-primary-light:hover {
  background-color: var(--tw-primary-light);
}

.hover\:bg-secondary-active:hover {
  background-color: var(--tw-secondary-active);
}

.hover\:text-gray-700:hover {
  color: var(--tw-gray-700);
}

.hover\:text-gray-800:hover {
  color: var(--tw-gray-800);
}

.hover\:text-primary:hover {
  color: var(--tw-primary);
}

.hover\:text-primary-active:hover {
  color: var(--tw-primary-active);
}

.focus\:border-primary-clarity:focus {
  border-color: var(--tw-primary-clarity);
}

.focus\:ring:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-primary-clarity:focus {
  --tw-ring-color: var(--tw-primary-clarity);
}

.group:hover .group-hover\:text-primary {
  color: var(--tw-primary);
}

.group:hover .group-hover\:text-primary-active {
  color: var(--tw-primary-active);
}

.has-\[\:checked\]\:border-3:has(:checked) {
  border-width: 3px;
}

.has-\[\:checked\]\:border-primary:has(:checked) {
  border-color: var(--tw-primary);
}

.has-\[\:checked\]\:border-success:has(:checked) {
  border-color: var(--tw-success);
}

.menu-item.active.menu-item-active\:rounded-lg {
  border-radius: 0.5rem;
}

.menu-item.active.menu-item-active\:border-gray-200 {
  border-color: var(--tw-gray-200);
}

.menu-item.active.menu-item-active\:border-b-primary {
  border-bottom-color: var(--tw-primary);
}

.menu-item.active.menu-item-active\:\!bg-light {
  background-color: var(--tw-light) !important;
}

.menu-item.active.menu-item-active\:bg-secondary-active {
  background-color: var(--tw-secondary-active);
}

.menu-item.active.menu-item-active\:font-semibold {
  font-weight: 600;
}

.menu-item.active.menu-item-active\:text-gray-900 {
  color: var(--tw-gray-900);
}

.menu-item.active.menu-item-active\:text-primary {
  color: var(--tw-primary);
}

.menu-item.active.menu-item-active\:before\:bg-primary::before {
  content: var(--tw-content);
  background-color: var(--tw-primary);
}

.menu-item.active > .menu-link.menu-item-active\:rounded-lg {
  border-radius: 0.5rem;
}

.menu-item.active > .menu-link.menu-item-active\:border-gray-200 {
  border-color: var(--tw-gray-200);
}

.menu-item.active > .menu-link.menu-item-active\:border-b-primary {
  border-bottom-color: var(--tw-primary);
}

.menu-item.active > .menu-link.menu-item-active\:\!bg-light {
  background-color: var(--tw-light) !important;
}

.menu-item.active > .menu-link.menu-item-active\:bg-secondary-active {
  background-color: var(--tw-secondary-active);
}

.menu-item.active > .menu-link.menu-item-active\:font-semibold {
  font-weight: 600;
}

.menu-item.active > .menu-link.menu-item-active\:text-gray-900 {
  color: var(--tw-gray-900);
}

.menu-item.active > .menu-link.menu-item-active\:text-primary {
  color: var(--tw-primary);
}

.menu-item.active > .menu-link.menu-item-active\:before\:bg-primary::before {
  content: var(--tw-content);
  background-color: var(--tw-primary);
}

.menu-item.active > .menu-link .menu-item-active\:rounded-lg {
  border-radius: 0.5rem;
}

.menu-item.active > .menu-link .menu-item-active\:border-gray-200 {
  border-color: var(--tw-gray-200);
}

.menu-item.active > .menu-link .menu-item-active\:border-b-primary {
  border-bottom-color: var(--tw-primary);
}

.menu-item.active > .menu-link .menu-item-active\:\!bg-light {
  background-color: var(--tw-light) !important;
}

.menu-item.active > .menu-link .menu-item-active\:bg-secondary-active {
  background-color: var(--tw-secondary-active);
}

.menu-item.active > .menu-link .menu-item-active\:font-semibold {
  font-weight: 600;
}

.menu-item.active > .menu-link .menu-item-active\:text-gray-900 {
  color: var(--tw-gray-900);
}

.menu-item.active > .menu-link .menu-item-active\:text-primary {
  color: var(--tw-primary);
}

.menu-item.active > .menu-link .menu-item-active\:before\:bg-primary::before {
  content: var(--tw-content);
  background-color: var(--tw-primary);
}

.menu-item.active > .menu-label.menu-item-active\:rounded-lg {
  border-radius: 0.5rem;
}

.menu-item.active > .menu-label.menu-item-active\:border-gray-200 {
  border-color: var(--tw-gray-200);
}

.menu-item.active > .menu-label.menu-item-active\:border-b-primary {
  border-bottom-color: var(--tw-primary);
}

.menu-item.active > .menu-label.menu-item-active\:\!bg-light {
  background-color: var(--tw-light) !important;
}

.menu-item.active > .menu-label.menu-item-active\:bg-secondary-active {
  background-color: var(--tw-secondary-active);
}

.menu-item.active > .menu-label.menu-item-active\:font-semibold {
  font-weight: 600;
}

.menu-item.active > .menu-label.menu-item-active\:text-gray-900 {
  color: var(--tw-gray-900);
}

.menu-item.active > .menu-label.menu-item-active\:text-primary {
  color: var(--tw-primary);
}

.menu-item.active > .menu-label.menu-item-active\:before\:bg-primary::before {
  content: var(--tw-content);
  background-color: var(--tw-primary);
}

.menu-item.active > .menu-label .menu-item-active\:rounded-lg {
  border-radius: 0.5rem;
}

.menu-item.active > .menu-label .menu-item-active\:border-gray-200 {
  border-color: var(--tw-gray-200);
}

.menu-item.active > .menu-label .menu-item-active\:border-b-primary {
  border-bottom-color: var(--tw-primary);
}

.menu-item.active > .menu-label .menu-item-active\:\!bg-light {
  background-color: var(--tw-light) !important;
}

.menu-item.active > .menu-label .menu-item-active\:bg-secondary-active {
  background-color: var(--tw-secondary-active);
}

.menu-item.active > .menu-label .menu-item-active\:font-semibold {
  font-weight: 600;
}

.menu-item.active > .menu-label .menu-item-active\:text-gray-900 {
  color: var(--tw-gray-900);
}

.menu-item.active > .menu-label .menu-item-active\:text-primary {
  color: var(--tw-primary);
}

.menu-item.active > .menu-label .menu-item-active\:before\:bg-primary::before {
  content: var(--tw-content);
  background-color: var(--tw-primary);
}

.menu-item.here.menu-item-here\:border-b-primary {
  border-bottom-color: var(--tw-primary);
}

.menu-item.here.menu-item-here\:font-semibold {
  font-weight: 600;
}

.menu-item.here.menu-item-here\:text-gray-900 {
  color: var(--tw-gray-900);
}

.menu-item.here.menu-item-here\:text-primary {
  color: var(--tw-primary);
}

.menu-item.here > .menu-link.menu-item-here\:border-b-primary {
  border-bottom-color: var(--tw-primary);
}

.menu-item.here > .menu-link.menu-item-here\:font-semibold {
  font-weight: 600;
}

.menu-item.here > .menu-link.menu-item-here\:text-gray-900 {
  color: var(--tw-gray-900);
}

.menu-item.here > .menu-link.menu-item-here\:text-primary {
  color: var(--tw-primary);
}

.menu-item.here > .menu-link .menu-item-here\:border-b-primary {
  border-bottom-color: var(--tw-primary);
}

.menu-item.here > .menu-link .menu-item-here\:font-semibold {
  font-weight: 600;
}

.menu-item.here > .menu-link .menu-item-here\:text-gray-900 {
  color: var(--tw-gray-900);
}

.menu-item.here > .menu-link .menu-item-here\:text-primary {
  color: var(--tw-primary);
}

.menu-item.here > .menu-label.menu-item-here\:border-b-primary {
  border-bottom-color: var(--tw-primary);
}

.menu-item.here > .menu-label.menu-item-here\:font-semibold {
  font-weight: 600;
}

.menu-item.here > .menu-label.menu-item-here\:text-gray-900 {
  color: var(--tw-gray-900);
}

.menu-item.here > .menu-label.menu-item-here\:text-primary {
  color: var(--tw-primary);
}

.menu-item.here > .menu-label .menu-item-here\:border-b-primary {
  border-bottom-color: var(--tw-primary);
}

.menu-item.here > .menu-label .menu-item-here\:font-semibold {
  font-weight: 600;
}

.menu-item.here > .menu-label .menu-item-here\:text-gray-900 {
  color: var(--tw-gray-900);
}

.menu-item.here > .menu-label .menu-item-here\:text-primary {
  color: var(--tw-primary);
}

.menu-item.show.menu-item-show\:\!flex {
  display: flex !important;
}

.menu-item.show.menu-item-show\:inline-flex {
  display: inline-flex;
}

.menu-item.show.menu-item-show\:hidden {
  display: none;
}

.menu-item.show.menu-item-show\:text-gray-800 {
  color: var(--tw-gray-800);
}

.menu-item.show.menu-item-show\:text-primary {
  color: var(--tw-primary);
}

.menu-item.show > .menu-link.menu-item-show\:\!flex {
  display: flex !important;
}

.menu-item.show > .menu-link.menu-item-show\:inline-flex {
  display: inline-flex;
}

.menu-item.show > .menu-link.menu-item-show\:hidden {
  display: none;
}

.menu-item.show > .menu-link.menu-item-show\:text-gray-800 {
  color: var(--tw-gray-800);
}

.menu-item.show > .menu-link.menu-item-show\:text-primary {
  color: var(--tw-primary);
}

.menu-item.show > .menu-link .menu-item-show\:\!flex {
  display: flex !important;
}

.menu-item.show > .menu-link .menu-item-show\:inline-flex {
  display: inline-flex;
}

.menu-item.show > .menu-link .menu-item-show\:hidden {
  display: none;
}

.menu-item.show > .menu-link .menu-item-show\:text-gray-800 {
  color: var(--tw-gray-800);
}

.menu-item.show > .menu-link .menu-item-show\:text-primary {
  color: var(--tw-primary);
}

.menu-item.show > .menu-label.menu-item-show\:\!flex {
  display: flex !important;
}

.menu-item.show > .menu-label.menu-item-show\:inline-flex {
  display: inline-flex;
}

.menu-item.show > .menu-label.menu-item-show\:hidden {
  display: none;
}

.menu-item.show > .menu-label.menu-item-show\:text-gray-800 {
  color: var(--tw-gray-800);
}

.menu-item.show > .menu-label.menu-item-show\:text-primary {
  color: var(--tw-primary);
}

.menu-item.show > .menu-label .menu-item-show\:\!flex {
  display: flex !important;
}

.menu-item.show > .menu-label .menu-item-show\:inline-flex {
  display: inline-flex;
}

.menu-item.show > .menu-label .menu-item-show\:hidden {
  display: none;
}

.menu-item.show > .menu-label .menu-item-show\:text-gray-800 {
  color: var(--tw-gray-800);
}

.menu-item.show > .menu-label .menu-item-show\:text-primary {
  color: var(--tw-primary);
}

.menu-link:hover.menu-link-hover\:border-gray-200 {
  border-color: var(--tw-gray-200);
}

.menu-link:hover.menu-link-hover\:\!bg-light {
  background-color: var(--tw-light) !important;
}

.menu-link:hover.menu-link-hover\:\!text-primary {
  color: var(--tw-primary) !important;
}

.menu-link:hover.menu-link-hover\:text-primary {
  color: var(--tw-primary);
}

.menu-link:hover .menu-link-hover\:border-gray-200 {
  border-color: var(--tw-gray-200);
}

.menu-link:hover .menu-link-hover\:\!bg-light {
  background-color: var(--tw-light) !important;
}

.menu-link:hover .menu-link-hover\:\!text-primary {
  color: var(--tw-primary) !important;
}

.menu-link:hover .menu-link-hover\:text-primary {
  color: var(--tw-primary);
}

[data-accordion-item].active.accordion-active\:block {
  display: block;
}

[data-accordion-item].active.accordion-active\:hidden {
  display: none;
}

[data-accordion-item].active > [data-accordion-toggle] .accordion-active\:block {
  display: block;
}

[data-accordion-item].active > [data-accordion-toggle] .accordion-active\:hidden {
  display: none;
}

[data-accordion-item].active > [data-accordion-toggle].accordion-active\:block {
  display: block;
}

[data-accordion-item].active > [data-accordion-toggle].accordion-active\:hidden {
  display: none;
}

[data-modal].open.modal-open\:\!flex {
  display: flex !important;
}

[data-modal].open .modal-open\:\!flex {
  display: flex !important;
}

[data-dropdown].open .dropdown-open\:bg-primary-light {
  background-color: var(--tw-primary-light);
}

[data-dropdown].open .dropdown-open\:text-primary {
  color: var(--tw-primary);
}

[data-dropdown-content].open .dropdown-open\:bg-primary-light {
  background-color: var(--tw-primary-light);
}

[data-dropdown-content].open .dropdown-open\:text-primary {
  color: var(--tw-primary);
}

[data-toggle].active.toggle-active\:rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
    skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

[data-toggle].active .toggle-active\:rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
    skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

[data-tab-toggle].active.tab-active\:border-primary-clarity {
  border-color: var(--tw-primary-clarity);
}

[data-tab-toggle].active.tab-active\:bg-primary-light {
  background-color: var(--tw-primary-light);
}

[data-tab-toggle].active.tab-active\:text-primary {
  color: var(--tw-primary);
}

[data-tab-toggle].active .tab-active\:border-primary-clarity {
  border-color: var(--tw-primary-clarity);
}

[data-tab-toggle].active .tab-active\:bg-primary-light {
  background-color: var(--tw-primary-light);
}

[data-tab-toggle].active .tab-active\:text-primary {
  color: var(--tw-primary);
}

[data-toggle-password].active.toggle-password-active\:block {
  display: block;
}

[data-toggle-password].active.toggle-password-active\:hidden {
  display: none;
}

[data-toggle-password].active .toggle-password-active\:block {
  display: block;
}

[data-toggle-password].active .toggle-password-active\:hidden {
  display: none;
}

[data-scrollspy-anchor].active.scrollspy-active\:bg-secondary-active {
  background-color: var(--tw-secondary-active);
}

[data-scrollspy-anchor].active.scrollspy-active\:text-primary {
  color: var(--tw-primary);
}

[data-scrollspy-anchor].active.scrollspy-active\:before\:bg-primary::before {
  content: var(--tw-content);
  background-color: var(--tw-primary);
}

[data-scrollspy-anchor].active .scrollspy-active\:bg-secondary-active {
  background-color: var(--tw-secondary-active);
}

[data-scrollspy-anchor].active .scrollspy-active\:text-primary {
  color: var(--tw-primary);
}

[data-scrollspy-anchor].active .scrollspy-active\:before\:bg-primary::before {
  content: var(--tw-content);
  background-color: var(--tw-primary);
}

[data-image-input].empty .image-input-empty\:border-gray-300 {
  border-color: var(--tw-gray-300);
}

.light .light\:hidden {
  display: none;
}

.light .light\:border-gray-300 {
  border-color: var(--tw-gray-300);
}

.switch:has([type='checkbox']:checked) .switch-on\:inline {
  display: inline;
}

.switch:has([type='checkbox']:checked) .switch-on\:hidden {
  display: none;
}

.dark\:block:is(.dark *) {
  display: block;
}

.dark\:hidden:is(.dark *) {
  display: none;
}

.dark\:border-b:is(.dark *) {
  border-bottom-width: 1px;
}

.dark\:border-gray-300:is(.dark *) {
  border-color: var(--tw-gray-300);
}

.dark\:border-b-coal-100:is(.dark *) {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(21 23 28 / var(--tw-border-opacity));
}

.dark\:border-r-coal-100:is(.dark *) {
  --tw-border-opacity: 1;
  border-right-color: rgb(21 23 28 / var(--tw-border-opacity));
}

.dark\:bg-coal-100:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(21 23 28 / var(--tw-bg-opacity));
}

.dark\:bg-coal-500:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(13 14 18 / var(--tw-bg-opacity));
}

.dark\:bg-coal-600:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(11 12 16 / var(--tw-bg-opacity));
}

.dark\:text-gray-400:is(.dark *) {
  color: var(--tw-gray-400);
}

.dark\:hover\:border-gray-100:hover:is(.dark *) {
  border-color: var(--tw-gray-100);
}

.dark\:hover\:bg-coal-300:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(17 18 23 / var(--tw-bg-opacity));
}

.menu-item.active.dark\:menu-item-active\:border-gray-100:is(.dark *) {
  border-color: var(--tw-gray-100);
}

.menu-item.active.dark\:menu-item-active\:\!bg-coal-600:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(11 12 16 / var(--tw-bg-opacity)) !important;
}

.menu-item.active.dark\:menu-item-active\:bg-coal-300:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(17 18 23 / var(--tw-bg-opacity));
}

.menu-item.active > .menu-link.dark\:menu-item-active\:border-gray-100:is(.dark *) {
  border-color: var(--tw-gray-100);
}

.menu-item.active > .menu-link.dark\:menu-item-active\:\!bg-coal-600:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(11 12 16 / var(--tw-bg-opacity)) !important;
}

.menu-item.active > .menu-link.dark\:menu-item-active\:bg-coal-300:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(17 18 23 / var(--tw-bg-opacity));
}

.menu-item.active > .menu-link .dark\:menu-item-active\:border-gray-100:is(.dark *) {
  border-color: var(--tw-gray-100);
}

.menu-item.active > .menu-link .dark\:menu-item-active\:\!bg-coal-600:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(11 12 16 / var(--tw-bg-opacity)) !important;
}

.menu-item.active > .menu-link .dark\:menu-item-active\:bg-coal-300:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(17 18 23 / var(--tw-bg-opacity));
}

.menu-item.active > .menu-label.dark\:menu-item-active\:border-gray-100:is(.dark *) {
  border-color: var(--tw-gray-100);
}

.menu-item.active > .menu-label.dark\:menu-item-active\:\!bg-coal-600:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(11 12 16 / var(--tw-bg-opacity)) !important;
}

.menu-item.active > .menu-label.dark\:menu-item-active\:bg-coal-300:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(17 18 23 / var(--tw-bg-opacity));
}

.menu-item.active > .menu-label .dark\:menu-item-active\:border-gray-100:is(.dark *) {
  border-color: var(--tw-gray-100);
}

.menu-item.active > .menu-label .dark\:menu-item-active\:\!bg-coal-600:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(11 12 16 / var(--tw-bg-opacity)) !important;
}

.menu-item.active > .menu-label .dark\:menu-item-active\:bg-coal-300:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(17 18 23 / var(--tw-bg-opacity));
}

.menu-link:hover.dark\:menu-link-hover\:border-gray-100:is(.dark *) {
  border-color: var(--tw-gray-100);
}

.menu-link:hover.dark\:menu-link-hover\:\!bg-coal-600:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(11 12 16 / var(--tw-bg-opacity)) !important;
}

.menu-link:hover .dark\:menu-link-hover\:border-gray-100:is(.dark *) {
  border-color: var(--tw-gray-100);
}

.menu-link:hover .dark\:menu-link-hover\:\!bg-coal-600:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(11 12 16 / var(--tw-bg-opacity)) !important;
}

[data-scrollspy-anchor].active.dark\:scrollspy-active\:border-gray-100:is(.dark *) {
  border-color: var(--tw-gray-100);
}

[data-scrollspy-anchor].active.dark\:scrollspy-active\:bg-coal-300:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(17 18 23 / var(--tw-bg-opacity));
}

[data-scrollspy-anchor].active .dark\:scrollspy-active\:border-gray-100:is(.dark *) {
  border-color: var(--tw-gray-100);
}

[data-scrollspy-anchor].active .dark\:scrollspy-active\:bg-coal-300:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(17 18 23 / var(--tw-bg-opacity));
}

@media (min-width: 640px) {
  .sm\:ml-8 {
    margin-left: 2rem;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .sm\:pl-8 {
    padding-left: 2rem;
  }
}

@media (min-width: 768px) {
  .md\:order-1 {
    order: 1;
  }

  .md\:order-2 {
    order: 2;
  }

  .md\:w-80 {
    width: 20rem;
  }

  .md\:max-w-\[60\%\] {
    max-width: 60%;
  }

  .md\:flex-1 {
    flex: 1 1 0%;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .md\:items-center {
    align-items: center;
  }

  .md\:justify-between {
    justify-content: space-between;
  }

  .md\:gap-10 {
    gap: 2.5rem;
  }
}

@media (min-width: 1024px) {
  .lg\:top-\[15\%\] {
    top: 15%;
  }

  .lg\:order-1 {
    order: 1;
  }

  .lg\:order-2 {
    order: 2;
  }

  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .lg\:m-5 {
    margin: 1.25rem;
  }

  .lg\:mb-0 {
    margin-bottom: 0px;
  }

  .lg\:mb-10 {
    margin-bottom: 2.5rem;
  }

  .lg\:mb-5 {
    margin-bottom: 1.25rem;
  }

  .lg\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .lg\:mb-7 {
    margin-bottom: 1.75rem;
  }

  .lg\:mb-8 {
    margin-bottom: 2rem;
  }

  .lg\:mr-10 {
    margin-right: 2.5rem;
  }

  .lg\:mt-0 {
    margin-top: 0px;
  }

  .lg\:block {
    display: block;
  }

  .lg\:flex {
    display: flex;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:h-\[50px\] {
    height: 50px;
  }

  .lg\:w-\[250px\] {
    width: 250px;
  }

  .lg\:w-\[50px\] {
    width: 50px;
  }

  .lg\:w-full {
    width: 100%;
  }

  .lg\:min-w-24 {
    min-width: 6rem;
  }

  .lg\:max-w-\[1240px\] {
    max-width: 1240px;
  }

  .lg\:max-w-\[220px\] {
    max-width: 220px;
  }

  .lg\:max-w-\[670px\] {
    max-width: 670px;
  }

  .lg\:max-w-\[875px\] {
    max-width: 875px;
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:flex-col {
    flex-direction: column;
  }

  .lg\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .lg\:items-end {
    align-items: flex-end;
  }

  .lg\:items-stretch {
    align-items: stretch;
  }

  .lg\:justify-end {
    justify-content: flex-end;
  }

  .lg\:justify-center {
    justify-content: center;
  }

  .lg\:justify-between {
    justify-content: space-between;
  }

  .lg\:gap-10 {
    gap: 2.5rem;
  }

  .lg\:gap-11 {
    gap: 2.75rem;
  }

  .lg\:gap-12 {
    gap: 3rem;
  }

  .lg\:gap-14 {
    gap: 3.5rem;
  }

  .lg\:gap-2\.5 {
    gap: 0.625rem;
  }

  .lg\:gap-20 {
    gap: 5rem;
  }

  .lg\:gap-3 {
    gap: 0.75rem;
  }

  .lg\:gap-3\.5 {
    gap: 0.875rem;
  }

  .lg\:gap-4 {
    gap: 1rem;
  }

  .lg\:gap-4\.5 {
    gap: 1.125rem;
  }

  .lg\:gap-5 {
    gap: 1.25rem;
  }

  .lg\:gap-6 {
    gap: 1.5rem;
  }

  .lg\:gap-7\.5 {
    gap: 1.875rem;
  }

  .lg\:rounded-none {
    border-radius: 0px;
  }

  .lg\:rounded-xl {
    border-radius: 0.75rem;
  }

  .lg\:rounded-l-none {
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
  }

  .lg\:rounded-l-xl {
    border-top-left-radius: 0.75rem;
    border-bottom-left-radius: 0.75rem;
  }

  .lg\:rounded-r-none {
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
  }

  .lg\:rounded-r-xl {
    border-top-right-radius: 0.75rem;
    border-bottom-right-radius: 0.75rem;
  }

  .lg\:rounded-t-none {
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
  }

  .lg\:rounded-tl-xl {
    border-top-left-radius: 0.75rem;
  }

  .lg\:border {
    border-width: 1px;
  }

  .lg\:border-0 {
    border-width: 0px;
  }

  .lg\:border-l {
    border-left-width: 1px;
  }

  .lg\:border-r {
    border-right-width: 1px;
  }

  .lg\:border-t {
    border-top-width: 1px;
  }

  .lg\:border-gray-200 {
    border-color: var(--tw-gray-200);
  }

  .lg\:border-l-gray-200 {
    border-left-color: var(--tw-gray-200);
  }

  .lg\:border-r-gray-200 {
    border-right-color: var(--tw-gray-200);
  }

  .lg\:border-t-gray-300 {
    border-top-color: var(--tw-gray-300);
  }

  .lg\:p-0 {
    padding: 0px;
  }

  .lg\:p-10 {
    padding: 2.5rem;
  }

  .lg\:p-12 {
    padding: 3rem;
  }

  .lg\:p-16 {
    padding: 4rem;
  }

  .lg\:p-7 {
    padding: 1.75rem;
  }

  .lg\:p-7\.5 {
    padding: 1.875rem;
  }

  .lg\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .lg\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .lg\:px-7 {
    padding-left: 1.75rem;
    padding-right: 1.75rem;
  }

  .lg\:px-7\.5 {
    padding-left: 1.875rem;
    padding-right: 1.875rem;
  }

  .lg\:py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }

  .lg\:py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .lg\:py-5 {
    padding-top: 1.25rem;
    padding-bottom: 1.25rem;
  }

  .lg\:py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .lg\:py-7\.5 {
    padding-top: 1.875rem;
    padding-bottom: 1.875rem;
  }

  .lg\:py-9 {
    padding-top: 2.25rem;
    padding-bottom: 2.25rem;
  }

  .lg\:pb-10 {
    padding-bottom: 2.5rem;
  }

  .lg\:pb-2\.5 {
    padding-bottom: 0.625rem;
  }

  .lg\:pb-4 {
    padding-bottom: 1rem;
  }

  .lg\:pb-5 {
    padding-bottom: 1.25rem;
  }

  .lg\:pb-7 {
    padding-bottom: 1.75rem;
  }

  .lg\:pb-7\.5 {
    padding-bottom: 1.875rem;
  }

  .lg\:pb-9 {
    padding-bottom: 2.25rem;
  }

  .lg\:pe-10 {
    padding-inline-end: 2.5rem;
  }

  .lg\:pe-12 {
    padding-inline-end: 3rem;
  }

  .lg\:pe-6 {
    padding-inline-end: 1.5rem;
  }

  .lg\:pe-8 {
    padding-inline-end: 2rem;
  }

  .lg\:pl-5 {
    padding-left: 1.25rem;
  }

  .lg\:pr-12\.5 {
    padding-right: 3.125rem;
  }

  .lg\:pr-3 {
    padding-right: 0.75rem;
  }

  .lg\:pt-10 {
    padding-top: 2.5rem;
  }

  .lg\:pt-4 {
    padding-top: 1rem;
  }

  .lg\:pt-5 {
    padding-top: 1.25rem;
  }

  .lg\:pt-6 {
    padding-top: 1.5rem;
  }

  .lg\:pt-7 {
    padding-top: 1.75rem;
  }

  .lg\:pt-7\.5 {
    padding-top: 1.875rem;
  }

  .lg\:pt-9 {
    padding-top: 2.25rem;
  }

  .lg\:text-right {
    text-align: right;
  }

  .lg\:text-2\.5xl {
    font-size: 1.625rem;
    line-height: 2.125rem;
  }

  .lg\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .dark\:lg\:border-l-coal-100:is(.dark *) {
    --tw-border-opacity: 1;
    border-left-color: rgb(21 23 28 / var(--tw-border-opacity));
  }

  .dark\:lg\:border-r-coal-100:is(.dark *) {
    --tw-border-opacity: 1;
    border-right-color: rgb(21 23 28 / var(--tw-border-opacity));
  }

  .dark\:lg\:border-t-gray-100:is(.dark *) {
    border-top-color: var(--tw-gray-100);
  }
}

@media (min-width: 1280px) {
  .xl\:mr-14 {
    margin-right: 3.5rem;
  }

  .xl\:mr-16 {
    margin-right: 4rem;
  }

  .xl\:w-\[38\.75rem\] {
    width: 38.75rem;
  }

  .xl\:min-w-24 {
    min-width: 6rem;
  }

  .xl\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .xl\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .xl\:flex-row {
    flex-direction: row;
  }

  .xl\:gap-7\.5 {
    gap: 1.875rem;
  }

  .xl\:bg-cover {
    background-size: cover;
  }
}

.\[\&\.dark\]\:bg-coal-600.dark {
  --tw-bg-opacity: 1;
  background-color: rgb(11 12 16 / var(--tw-bg-opacity));
}

.\[\&\:not\(\:last-child\)\]\:block:not(:last-child) {
  display: block;
}

.\[\&\:not\(\:last-child\)\]\:border-b:not(:last-child) {
  border-bottom-width: 1px;
}

.\[\&\:not\(\:last-child\)\]\:border-r:not(:last-child) {
  border-right-width: 1px;
}

.\[\&_\.checked\]\:has-\[\:checked\]\:flex:has(:checked) .checked {
  display: flex;
}

.authors-row .\[\.authors-row_\&\]\:left-\[64px\] {
  left: 64px;
}

.authors-row .\[\.authors-row_\&\]\:size-\[80px\] {
  width: 80px;
  height: 80px;
}

@media screen and (max-width: 1024px) {
  .header .\[\.header_\&\]\:below-lg\:hidden {
    display: none;
  }
}

.menu-dropdown .\[\.menu-dropdown_\&\]\:-rotate-90 {
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
    skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

html.dark .\[html\.dark_\&\]\:block {
  display: block;
}

html.dark .\[html\.dark_\&\]\:hidden {
  display: none;
}

.G-bg-gradient {
  background: radial-gradient(circle farthest-corner at 32% 26%, #5d10ff 0%, #8015e8 36.0321044921875%, #000000 75.677490234375%);
  --bg-overlay-color: transparent;
  /* font-family: 'Noto Sans CJK', sans-serif; */
}

/* .G-bg-gradient * {
  font-family: inherit;
} */

.bg-multiple-images {
  background-image: url('/da/img/leanding/sector_06_bg_l.png'), url('/da/img/leanding/sector_06_bg_r.png');
  background-position: left, right;
  background-size: 30%, 30%;
  background-repeat: no-repeat, no-repeat;
}

.header-menu {
  font-size: 16px;
  font-weight: 500;
  line-height: 16px;
}

.H-22-22-600 {
  font-size: 22px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px;
  letter-spacing: -0.01em;
}

.H-30-30-600 {
  font-size: 30px;
  font-style: normal;
  font-weight: 600;
  line-height: 30px;
  letter-spacing: -2%;
}

.H-34-42-700 {
  font-size: 34px;
  font-weight: 700;
  line-height: 42px;
  letter-spacing: -0.02em;
}

.H-50-54-700 {
  font-size: 50px;
  font-weight: 700;
  line-height: 54px;
  letter-spacing: -0.02em;
}

.H-80-96-600 {
  font-size: 80px;
  font-weight: 600;
  line-height: 96px;
  letter-spacing: -2px;
}

.B-16-16-500 {
  font-size: 16px;
  font-weight: 500;
  line-height: 16px;
}

.B-20-30-500 {
  font-size: 20px;
  font-weight: 500;
  line-height: 30px;
}

.B-16-25-500 {
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 25px;
  order: 1;
}

.B-14-14-500 {
  font-family: Inter !important;
  font-size: 14px;
  font-weight: 500;
  line-height: 14px;
}

.B-18-30-500 {
  font-size: 18px;
  font-weight: 500;
  line-height: 30px;
}

.B-22-38-500 {
  font-size: 22px;
  font-weight: 500;
  line-height: 38px;
  letter-spacing: -1%;
}

pre {
  font-family: Inter !important;
}
.table-form {
  font-family: Inter !important;
  margin-bottom: 8px;
  width: 100%;
}
.table-form thead {
  background-color: #f6faff;
  text-align: center;
  border: 1px solid #e6eaee;
}
.table-form thead th {
  font-size: 0.9em;
  font-weight: 600;
  min-width: 50px;
  padding: 5px 0 5px 0;
}
.table-form tbody {
  border-bottom: 1px solid #e6eaee;
}

.table-form tbody td {
  font-size: 0.8em;
  font-weight: 400;
  color: #323232;
  border-bottom: 1px solid #e6eaee;
  padding: 8px 0 8px 0;
  text-align: center;
}

/* @media (max-width: 640px) { */
.gcube-chat-button-container {
  position: fixed;
  right: 15px;
  bottom: 10px;
  text-align: center; /* 버튼을 중앙에 정렬 */
  /* margin-top: 50px; */
}

.gcube-chat-button {
  width: 45px;
  height: 44px;
  border: none;
  cursor: pointer;
  background-image: url('/da/img/kakaotalk_sharing_btn_medium.png'); /* 기본 이미지 */
  background-size: cover; /* 이미지를 버튼 크기에 맞게 조정 */
  background-position: center;
  /* transition: background-image 0.3s ease-in-out;  */
}
/* } */

@media (min-width: 768px) {
  .gcube-chat-button-container {
    position: fixed;
    right: 25px;
    bottom: 25px;
    text-align: center; /* 버튼을 중앙에 정렬 */
    /* margin-top: 50px; */
  }
  .gcube-chat-button {
    width: 56px; /* 버튼의 폭 */
    height: 55px; /* 버튼의 높이 */
    border: none;
    cursor: pointer;
    background-image: url('/da/img/kakaotalk_sharing_btn_medium.png'); /* 기본 이미지 */
    background-size: cover; /* 이미지를 버튼 크기에 맞게 조정 */
    background-position: center;
    /* transition: background-image 0.3s ease-in-out;  */
  }
  .gcube-chat-button:hover {
    background-image: url('/da/img/kakaotalk_sharing_btn_medium_ov.png'); /* 마우스 오버 시 변경될 이미지 */
  }
}
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* Popup styling */
.popup {
  position: relative;
  background: #fff;
  padding: 20px;
  width: auth;
  height: 100%;
  max-height: 80vh;

  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  text-align: center;
}

.popup img {
  height: 95%;
  width: auto;
}

.popup .deepseekBtn button {
  font-size: 13px;
  height: 32px;
}

.popup .deepseekBtn {
  position: absolute;
  /* bottom: 138px; */
  /* right: 153px; */
  bottom: calc(100% * 0.183); /* 높이의 10% */
  right: calc(100% * 0.21); /* 높이의 10% */
  width: 180px;
}

.popup .deepseekBtn button {
  font-size: 13px;
  height: 32px;
}

.popup-mobile {
  position: relative;
  width: 360px;
  height: 455px;
  background: url('/da/img/popup/popup_bg_mobile.png');
}

.popup-mobile .deepseekBtn {
  width: 320px;
  height: 52px;
  margin-top: 200px;
}

.popup-mobile .deepseekBtn:hover {
  cursor: pointer;
}

.popup-mobile .kakaoChannelBtn {
  width: 280px;
  height: 70px;
  margin-top: 140px;
}

.popup-mobile .kakaoChannelBtn:hover {
  cursor: pointer;
}

.banner-container {
  /* max-width: 100vw; */

  max-width: calc(100vw - var(--scroll-width, 0px));
  width: 100%;
}

.banner {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 100%;
  height: 100%;
}

.mobile_banner_1 {
  background: url('/da/img/banner/banner_sm_background_1.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  max-height: 120px;
  height: 100%;
}

.pc_banner_1 {
  background: url('/da/img/banner/banner_lg_background_1.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  max-height: 80px;
  height: 80px;
  /* height: 100%; */
}

.mobile_banner_2 {
  background: url('/da/img/banner/banner_sm_background_2.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  max-height: 120px;
  height: 100%;
}

.pc_banner_2 {
  background: url('/da/img/banner/banner_lg_background_2.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  max-height: 80px;
  height: 80px;
  /* height: 100%; */
}

.mobile_banner_img {
  width: 100vw;
  height: 90px;
  object-fit: contain;
}

.pc_banner_img {
  width: 100vw;
  height: 70px;
  object-fit: contain;
}
