html {
}

body {
  margin: 0;
  font-size: 1rem;
  font-weight: 400;
  color: #858796;
  background-color: #f2f5f9;
  font-family: sans-serif, Arial;
}
.layout {
  margin: 0;
  width: 100%;
}

.header {
  width: 100%;
  height: 50px;
  background-color: #fff;
  display: inline-block;
}

.header .content {
  width: 1280px;
  margin: 0 auto;
  padding-top: 10px;
  padding-bottom: 10px;
}
.header .content .left {
  padding-left: 10px;
  float: left;
}

.header .content .right {
  padding-right: 10px;
  float: right;
}

.header .title {
  font-size: 1.4em;
  font-weight: 600;
  color: #000000;
  display: inline-block;
}
.header .title_sub {
  font-size: 1.3em;
  font-family: inherit;
  font-weight: 600;
  flex: 1;
  display: inline-block;
}
.header .login {
  font-size: 1.2em;
  font-weight: 600;
  color: #8247ff;
  display: inline-block;
}
.container {
  width: 100%;
  padding: 0;
  background-color: #f2f5f9;
  text-align: center;
}

.container .content {
  margin: 0 auto;
  padding: 0;
  width: 1920px;
  background-color: #fff;
}
.container .content .title {
  font-size: 1.5em;
  font-weight: 800;
  padding: 10px;
  color: #000000;
  display: inline-block;
}
.container .content .con-box {
  height: 300px;
  padding: 10px;
  margin: 10px;
  outline: dashed 1px black;
  border-radius: 10px;
}

.container .content .con-backimg01 {
  width: 1920px;
  height: 687px;
  text-align: center;
}

.text-title {
  font-size: 1.8em;
  font-weight: 800;
  padding: 5px;
  color: #000000;
  display: inline-block;
}
.text-title-sub {
  font-size: 1.4em;
  font-weight: 600;
  padding: 5px;
  color: #808c98;
  display: inline-block;
}
.text-footer {
  font-size: 0.9em;
  font-weight: 400;
  padding: 5px;
  color: #808c98;
  display: inline-block;
}
.text-email {
  font-size: 1.3em;
  font-weight: 600;
  padding: 10px;
  color: #505050;
  display: inline-block;
}
.text-error {
  font-size: 1.3em;
  font-weight: 600;
  padding: 10px;
  color: #ff1e01;
  display: inline-block;
}
.text-box {
  width: 300px;
  height: 50px;
  padding: 10px;
  margin: 0 auto;
  outline: dashed 1px #5a5c69;
  border-radius: 10px;
  text-align: center;
}

.form-button-white {
  background-color: #ffffff;
  border: 1px solid #dcdcdc;
  color: #0a0a0a;
  font-size: 0.8em;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
}
.form-button-purple {
  background-color: #8247ff;
  border: 1px solid #dcdcdc;
  color: #fff;
  font-size: 0.8em;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
}
.form-button-cancel {
  background-color: #dcdcdc;
  border: 1px solid #dcdcdc;
  color: #808080;
  font-size: 0.8em;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
}

.form-button-white:hover {
  background-color: #8247ff;
  color: #ffffff;
  transition: 0.7s;
  fill: white;
}
.form-button-white:focus {
  background-color: #ffffff;
  color: #0a0a0a;
  transition: 0.7s;
  fill: black;
}
.form-button-white:active {
  background-color: #8247ff;
  color: #ffffff;
  transition: 0.7s;
  fill: white;
}

.con_top_bg {
  width: 100%;
  height: 687px;
  overflow: hidden;
  margin: 0 auto;
  position: relative;
}
.con_top {
  width: 1920px;
  height: 687px;
  margin: 0 auto;
  position: absolute;
  border: 1px solid #dcdcdc;
  top: 0;
}
#background-video {
  width: 100%;
  height: 687px;
  object-fit: cover;
  z-index: -1;
}
@media (max-width: 1920px) {
  .con_top_bg {
    height: auto;
  }
  video {
    height: 687px;
  }
}
.landing-bg01 {
  float: left;
  width: 400px;
  height: 400px;
  margin-right: 20px;
  background-image: url('/da/img/landing_photo_01.jpg');
  background-repeat: no-repeat;
  background-size: 400px 400px;
}
.landing-bg02 {
  float: left;
  width: 400px;
  height: 400px;
  margin-right: 20px;
  background-image: url('/da/img/landing_photo_02.jpg');
  background-repeat: no-repeat;
  background-size: 400px 400px;
}
.landing-bg03 {
  float: left;
  width: 400px;
  height: 400px;
  background-image: url('/da/img/landing_photo_03.jpg');
  background-repeat: no-repeat;
  background-size: 400px 400px;
}

object {
  vertical-align: middle;
}
