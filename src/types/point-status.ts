export interface PointStatusResponse {
  status: number;
  points: PointStatus;
}

export interface PointStatus {
  chargePoint: number;
  payantPoint: number;
  freeUsedPoint: number;
  freeUnusedPoint: number;
  availablePoint: number;
  availablePayantPoint: number;
  availableFreePoint: number;
  availableSettlePoint: number;
  availableRefundPoint: number;
  spendPoint: number;
  incomePoint: number;
  netPoint: number;
  blockingPoint: number;
  alarmPoint: number;
  spend: {
    point: number;
    usdAmount: number;
    usdUnitAmount: number;
    usdUsageAmount: number;
    krwAmount: number;
    krwUnitAmount: number;
    krwUsageAmount: number;
  };
  income: {
    point: number;
    usdAmount: number;
    usdUnitAmount: number;
    usdUsageAmount: number;
    krwAmount: number;
    krwUnitAmount: number;
    krwUsageAmount: number;
  };
  settle: {
    reqPoint: number;
    settlePoint: number;
    taxPoint: number;
    usdReqAmount: number;
    usdSettleAmount: number;
    usdTaxAmount: number;
    krwReqAmount: number;
    krwSettleAmount: number;
    krwTaxAmount: number;
    availablePoint: number;
  };
  refund: {
    reqPoint: number;
    refundPoint: number;
    feePoint: number;
    usdReqAmount: number;
    usdRefundAmount: number;
    usdFeeAmount: number;
    krwReqAmount: number;
    krwRefundAmount: number;
    krwFeeAmount: number;
    availablePoint: number;
  };
  isBlocked: false;
  isAlarm: false;
}
