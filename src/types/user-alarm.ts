import { Paging } from './paging';

export interface UserAlarmResponse {
  status: number;
  count?: UserAlarmCount;
  alarms?: UserAlarm[];
  paging?: Paging;
  alarm?: UserAlarm;
}

export interface UserAlarmCount {
  totalCount: number;
  noReadCount: number;
  newAlarmCount: number;
}
export interface UserAlarm {
  ser: number;
  sender: string;
  receiverEmail: string;
  receiverName: string;
  receiverType: string;
  alarmCategory: string;
  sendDate: string;
  channels: string;
  title: string;
  content: string;
  link: string | null;
  readYn: string;
  lastSeen: number[] | null;
  contentDetailYn: string;
  exposureTime?: string;
  popupYn?: string;
}
