interface TermsList {
  ser: number;
  title: string;
  summary: string;
  locale: string;
  rowNum: number;
  isUsed: boolean;
  isMust: boolean;
  isMarketing: boolean;
  createdAt: number[];
  lastSeen: number[];
}

interface TermsListResponse {
  status: number;
  termsList: TermsList[];
}

interface TermsContents {
  ser: string;
  termsSer: number;
  title: string;
  content: string;
  rowNum: number;
  locale: string;
  createdAt: number[];
  lastSeen: number[] | null;
}

interface TermsContentResponse {
  status: number;
  termsContentsList: TermsContents[];
}
