import { Paging } from '@/types/paging.ts';

export interface NodeNetwork {
  ser: string;
  owner: string;
  nodeName: string;
  upSpeed: number;
  createdAt: number[];
}

export interface NodeNetworkResponse {
  status: number;
  nodeNetworks: NodeNetwork[];
  paging: Paging;
}

export interface NodeNetworkRequest {
  startNum: number | undefined;
  scaleNum: number | undefined;
  owner: string | undefined;
  nodeName: string | undefined;
  startTime: string | undefined;
  endTime: string | undefined;
  sortName: string | undefined;
  sortType: string | undefined;
}
