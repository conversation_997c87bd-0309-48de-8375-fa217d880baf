interface CloudProvider {
  cloudName: string;
  cloudImg: string;
  cloudLink: string;
  cloudAlias: string;
}

interface GpuPricing {
  cloud: string;
  productName: string;
  productCode: string;
  gpuName: string;
  gpu: number;
  vram: number;
  cpu: number;
  memory: number;
  disk: number;
  price: number;
  usagePrice: number;
  order: number;
  currencyUnit: string;
  capUnit: string;
  createdAt: number[];
}
