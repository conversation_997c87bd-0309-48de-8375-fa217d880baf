import { Paging } from './paging';
import { Workload } from './workload';

export interface Qna {
  ser: number;
  owner: string;
  name: string;
  categoryCode: string;
  title: string;
  content: string;
  answer?: string;
  manager?: string;
  status?: string;
  createdAt?: number[] | null;
  answerAt?: number[] | null;
  targetType?: string | null;
  target?: string | null;
  locale: string;
}

export interface QnaResponse {
  status: number;
  qnas: Qna[];
  qna: Qna;
  paging: Paging;
}

export interface QnaRequest {
  startNum: number | undefined;
  scaleNum: number | undefined;
  categoryCode?: string | undefined;
  email?: string | undefined;
  name?: string | undefined;
  keyword?: string | undefined;
  manager?: string | undefined;
  isAssign?: string | undefined;
  status?: string | undefined;
  startTime?: string | undefined;
  endTime?: string | undefined;
}
