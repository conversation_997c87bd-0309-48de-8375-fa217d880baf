export interface UsageRequest {
  owner: string;
  namespace: string;
  node: string;
  pod?: string;
  isPod?: boolean;
  isOwner?: boolean;
  interval: number;
  startTime: string;
  endTime: string;
}

export interface Usage {
  gpuName: string;
  cpuUsage: number;
  memUsage: number;
  gpuUsage: number;
  vramUsage: number;
  timeInterval: number[];
  diskUsage: number;
  diskOccu: number;
  vramCap: number;
  netTx: number;
  netRx: number;
}

export interface UsageResponse {
  status: number;
  usages: Usage[];
}
