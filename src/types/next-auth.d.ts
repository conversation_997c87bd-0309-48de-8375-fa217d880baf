import type { User } from 'next-auth';

declare module 'next-auth' {
  /**
   * Returned by `useSession`, `getSession` and received as a prop on the `Provider` React Context
   */
  interface Session {
    accessToken?: string;
    refreshToken?: string;
    idToken?: string;
    // authorizationCode?: string;

    user: {
      sub: string;
      email_verified: boolean;
      name: string;
      preferred_username: string;
      given_name: string;
      family_name: string;
      email: string;
      id: string;
      org_name?: string;
      telephone?: string;
      roles: string[];
      picture: string;
      redirectUrl?: string;
      owner: string;
      namespace: string;
    };
    error: string;
  }
  /**
   * The shape of the user object returned in the OAuth providers' `profile` callback,
   * or the second parameter of the `session` callback, when using a database.
   */
  interface User {
    sub: string;
    email_verified: boolean;
    name: string;
    telephone: string;
    preferred_username: string;
    org_name: string;
    given_name: string;
    family_name: string;
    email: string;
    id: string;
    roles: string[];
    accessToken: string;
    provider: string;
    idToken: string;
    picture: string;
    redirectUrl?: string;
    locale: string;
    mode: string;
  }
  /**
   * Usually contains information about the provider being used
   * and also extends `TokenSet`, which is different tokens returned by OAuth Providers.
   */
  interface Account {
    provider: string;
    type: string;
    id: string;
    accessToken: string;
    accessTokenExpires?: any;
    refreshToken: string;
    idToken: string;
    access_token: string;
    expires_in: number;
    refresh_expires_in: number;
    refresh_token: string;
    token_type: string;
    id_token: string;
    'not-before-policy': number;
    session_state: string;
    scope: string;
    code: string;
  }
  /** The OAuth profile returned from your provider */
  interface Profile {
    sub: string;
    email_verified: boolean;
    name: string;
    telephone: string;
    preferred_username: string;
    org_name: string;
    given_name: string;
    family_name: string;
    email: string;
    termsUrl: string;
  }

  interface JWT {
    accessToken?: string;
    refreshToken?: string;
    idToken?: string;
    authorizationCode?: string;
  }
}

declare module 'next-auth/jwt' {
  /** Returned by the `jwt` callback and `getToken`, when using JWT sessions */

  interface JWT {
    idToken: string;
    name: string;
    email: string;
    sub: string;
    name: string;
    email: string;
    sub: string;
    accessToken: string;
    refreshToken: string;
    accessTokenExpired: number;
    refreshTokenExpired: number;
    user: User;
    error: string;
    namespace: string;
    owner: string;
    realmAccess: {
      roles: string[];
    };
    // authorizationCode?: string;
  }
}
export declare module '@auth/core/jwt' {
  interface JWT {
    idToken: string;
    name: string;
    email: string;
    sub: string;
    name: string;
    email: string;
    sub: string;
    accessToken: string;
    refreshToken: string;
    accessTokenExpired: number;
    refreshTokenExpired: number;
    user: User;
    error: string;
    namespace: string;
    owner: string;
    realmAccess: {
      roles: string[];
    };
    accessToken: string;
    provider: string;
  }
}
