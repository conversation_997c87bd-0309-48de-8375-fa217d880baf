import { boolean, number } from 'zod';

export interface GpuPricesResponse {
  status: number;
  pricings: GpuPrice[];
}
export interface GpuPrice {
  ser: number;
  cloud: string;
  productName: string;
  productCode: string;
  gpuName: string;
  gpu: number;
  vram: number;
  cpu: number;
  memory: number;
  disk: number;
  price: number;
  usagePrice: number;
  order: number;
  currencyUnit: string;
  createdAt: number[];
  nodes: GpuPriceNode[];
  capUnit: string;
  isAvailable?: boolean;
}

export interface GpuPriceNode {
  name: string;
  tier: string;
  gpuName: string;
  gpuCount: number;
  vram: number;
  cpu: number;
  memory: number;
  disk: number;
  osImage: string;
  state: string;
  podCount: 0;
  check: boolean;
}
