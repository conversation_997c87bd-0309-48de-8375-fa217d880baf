import { Paging } from './paging';

export interface SiteMenuResponse {
  status: number;
  siteMenus: string[];
}

export interface CategoryCodeResponse {
  status: number;
  codes: CategoryCode[];
  paging: Paging;
}

export interface CategoryCode {
  category: string;
  code: string;
  type: string;
  description: string;
  isUsed: boolean;
  manager: string;
  createdAt: number[] | null;
}

export interface CategoryCodeRequest {
  startNum: number | undefined;
  scaleNum: number | undefined;
  siteMenu?: string | undefined;
}

export interface SiteMenu {
  label: string;
  siteMenu: string;
}
