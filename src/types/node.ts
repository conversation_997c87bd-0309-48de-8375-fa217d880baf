import { Paging } from './paging';
import { Workload } from './workload';

export interface Node {
  name: string;
  instId: string;
  macId: string;
  ip: string;
  owner: string;
  category: string;
  cloud: string;
  gpuSpec: string;
  hostSpec: string;
  hostName: string | null;
  hostInfo: string | null;
  share: number;
  createdAt: number[];
  lastSeen: number[];
  state: string;
  provisionCode: string;
  subState: string;
  wgPubkey: string;
  price: number;
  region: string;
  vmIp: string;
  vmParams: string;
  unitPrice: number;
  usagePrice: number;
  point: number;
  amount: number;
  cuda: string;
  driver: string;
  taintState: string;
  lastUpSpeed: number;
  avgUpSpeed: number;
  taintStateArray: string[];
  dedicatedNmsp: string;
}

export interface NodeResponse {
  status: number;
  nodes: Node[];
  paging: Paging;
}

export interface NodeStateCount {
  count: number;
  state: string;
}

export interface NodeRequest {
  startNum: number | undefined;
  scaleNum: number | undefined;
  owner: string | undefined;
  category: string | undefined;
  cloud: string | undefined;
  gpuSpec: string | undefined;
  hostSpec: string | undefined;
  state: string | undefined;
  startTime: string | undefined;
  endTime: string | undefined;
  sortName: string | undefined;
  sortType: string | undefined;
  dedicatedNmsp: string | undefined;
  isDedicated: boolean | undefined;
}

export interface DedicatedNodeResponse {
  status: number;
  nodes: DedicatedNode[];
  paging: Paging;
}
export type DedicatedNode = {
  name: string;
  tier: string;
  gpuName: string;
  gpuCount: number;
  vram: number;
  cpu: number;
  memory: number;
  disk: number;
  osImage: string;
  state: string;
  driver: string;
  cuda: string;
  podCount: number;
  workload: string;
  check: boolean;
  workloadDetail?: Workload;
};

export type DedicatedGropuNode = {
  node: DedicatedNode;
  count: number;
};

export type NodeTaint = {
  ser: string;
  owner: string;
  nodeName: string;
  type: string;
  status: string;
  content: string;
  settingAt: number[];
  cancelAt: number[] | null;
};

export interface NodeTaintResponse {
  status: number;
  nodeTaints: NodeTaint[];
  paging: Paging;
}

export interface NodeTaintRequest {
  startNum: number | undefined;
  scaleNum: number | undefined;
  owner?: string | undefined;
  nodeName?: string | undefined;
  type?: string | undefined;
  status?: string | undefined;
  startTime?: string | undefined;
  endTime?: string | undefined;
  sortName?: string | undefined;
  sortType?: string | undefined;
}
export const mapSearchParamsToNodeRequest = (searchParams: URLSearchParams): NodeRequest => {
  return {
    startNum: parseInt(searchParams.get('startNum') || '0', 10),
    scaleNum: parseInt(searchParams.get('scaleNum') || process.env.NEXT_PUBLIC_PAGE_SCALE + '', 10),
    owner: searchParams.get('owner') || undefined,
    category: searchParams.get('category') || undefined,
    cloud: searchParams.get('cloud') || undefined,
    gpuSpec: searchParams.get('gpuSpec') || undefined,
    hostSpec: searchParams.get('hostSpec') || undefined,
    state: searchParams.get('state') || undefined,
    startTime: searchParams.get('startTime') || undefined,
    endTime: searchParams.get('endTime') || undefined,
    sortName: searchParams.get('sortName') || undefined,
    sortType: searchParams.get('sortType') || undefined,
    dedicatedNmsp: searchParams.get('dedicatedNmsp') || undefined,
    isDedicated: Boolean(searchParams.get('isDedicated')) || undefined
  };
};
