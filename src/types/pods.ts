export interface Pods {
  podName: string;
  nodeName: string;
  hostIp: string;
  podIp: string;
  state: string;
}
export interface PodStateCount {
  count: number;
  state: string;
}

export class PodSshDto {
  podName: string = '';
  namespace: string = '';
  workload: string = '';
  publicIp: string = '';
  shell: string = 'bash';
}

export interface PodSshInfo {
  cont_ser: number;
  created_at: string;
  namespace: string;
  pod_name: string;
  port: number;
  pub_ip: string;
  shell: string;
  ssh_url: string;
  user_name: string;
  user_pwd: string;
}
