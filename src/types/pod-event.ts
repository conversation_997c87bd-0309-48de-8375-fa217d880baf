import { Paging } from './paging';

export interface PodEventResponse {
  status: number;
  events: PodEvent[];
  paging: Paging;
}

export interface PodEvent {
  count: number;
  firstTimestamp: string;
  kind: string;
  lastTimestamp: string;
  manager: string;
  message: string;
  name: string;
  namespace: string;
  operation: string;
  reason: string;
  reportingComponent: string;
  reportingInstance: string;
  resType: string;
  type: string;
}
