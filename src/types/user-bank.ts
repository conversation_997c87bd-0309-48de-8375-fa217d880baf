export interface UserBankResponse {
  status: number;
  bank: UserBank;
}

export interface UserBank {
  ser: number;
  accountType: string;
  owner: string;
  ownerCI: string;
  ownerName: string;
  ownerGender: string;
  ownerBirthday: string;
  ownerNationality: string;
  ownerPhone: string;
  ownerEmail: string;
  bankType: string;
  bankName: string;
  bankNumber: string;
  bankCode: string;
  certAt: number[];
  createdAt: number[];
}
