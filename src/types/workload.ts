import { Paging } from './paging';

export interface Workload {
  ser: string;
  owner: string;
  description: string;
  target: string;
  category: string;
  repo: string;
  cont_image: string;
  cont_cmd: string;
  cont_envs: any;
  gpu: string;
  gpu_count: number;
  vram: number;
  maxPrice: number;
  cpu: number;
  tier: number;
  svc_url: string;
  k8s_svc: string;
  port: number;
  replica: number;
  created_at: number[];
  deploy_at: number[];
  close_at: number[];
  state: string;
  lb_opt: number;
  pv_capa: number;
  mount_path: string;
  pv_class: string;
  pv_name: string;
  img_sec: string;
  point: number;
  amount: number;
  unitAmount: number;
  usageAmount: number;
  isDeploy: boolean;
  k8_port: number;
  gw_port: number;
  cuda: string;
  shared_memory: number;
  is_dedicated: boolean;
  user_storages: any;
}
export interface WorkloadPod {
  name: string;
  node: string;
  status: string;
}

export interface WorkloadStateCount {
  count: number;
  state: string;
}

export interface WorkloadRequest {
  owner: string | undefined;
  startNum: number | undefined;
  scaleNum: number | undefined;
  category: string | undefined;
  target: string | undefined;
  state: string | undefined;
  startTime: string | undefined;
  endTime: string | undefined;
  sortName: string | undefined;
  sortType: string | undefined;
  isDedicated?: boolean | undefined;
}

export interface WorkloadResponse {
  status: number;
  workloads?: Workload[];
  workload?: Workload;
  pods: WorkloadPod[];
  paging: Paging;
}

export class WorkloadDto {
  ser?: string = '';
  owner: string = '';
  repo: string = '';
  description: string = '';
  containerImage: string = '';
  containerCommand: string = '';
  containerEnvs: any[] = [];
  category: string = 'infer';
  target: string = 'any';
  cpu: number = 0;
  gpu: string = '';
  gpuCount: number = 1;
  vram: number = 4;
  port: number = 8000;
  replica: number = 1;
  isDeploy?: boolean = false;
  maxPrice?: number = 0;
  lbOpt: number = 0;
  tier: number = 0;
  cuda: string = '';
  sharedMemory: number = 1;
  isDedicated: boolean = false;
  userStorages: any[] = [];
}

export const mapSearchParamsToWorkloadRequest = (searchParams: URLSearchParams): WorkloadRequest => {
  return {
    startNum: parseInt(searchParams.get('startNum') || '0', 10),
    scaleNum: parseInt(searchParams.get('scaleNum') || process.env.NEXT_PUBLIC_PAGE_SCALE + '', 10),
    owner: searchParams.get('owner') || undefined,
    category: searchParams.get('category') || undefined,
    target: searchParams.get('cloud') || undefined,
    state: searchParams.get('state') || undefined,
    startTime: searchParams.get('startTime') || undefined,
    endTime: searchParams.get('endTime') || undefined,
    sortName: searchParams.get('sortName') || undefined,
    sortType: searchParams.get('sortType') || undefined
  };
};
