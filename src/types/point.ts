import { Paging } from './paging';

export interface Point {
  chargePoint: number;
  totalAvailable: number;
  totalSpend: number;
  totalIncome: number;
  spend: number;
  income: number;
}
export interface PointResponse {
  status: number;
  points: Point;
}

export interface PointIncomes {
  nodeName: string;
  date: string;
  krwUsageAmount: number;
  gpuName: string;
  cpuUsage: number;
  usdAmount: number;
  krwGpuAmount: number;
  gpuUsage: number;
  krwUnitAmount: number;
  krwGpuUnitAmount: number;
  usdGpuAmount: number;
  point: number;
  gpuPoint: number;
  memUsage: number;
  useTime: number;
  usdGpuUnitAmount: number;
  vramUsage: number;
  diskOccu: number;
  krwAmount: number;
  krw: number;
  usdUsageAmount: number;
  diskUsage: number;
  vramCap: number;
  netTx: number;
  usdUnitAmount: number;
  usdGpuUsageAmount: number;
  netRx: number;
  krwGpuUsageAmount: number;
  memCap: number;
}

export interface PointIncomesResponse {
  status: number;
  pointIncomes: PointIncomes[];
}

export interface PointSpends {
  cpuUsage: number;
  date: string;
  diskOccu: number;
  diskUsage: number;
  gpuName: string;
  gpuPoint: number;
  gpuUsage: number;
  krw: number;
  krwAmount: number;
  krwGpuAmount: number;
  krwGpuUnitAmount: number;
  krwGpuUsageAmount: number;
  krwUnitAmount: number;
  krwUsageAmount: number;
  memCap: number;
  memUsage: number;
  namespace: string;
  netRx: number;
  netTx: number;
  owner: string;
  point: number;
  usdAmount: number;
  usdGpuAmount: number;
  usdGpuUnitAmount: number;
  usdGpuUsageAmount: number;
  usdUnitAmount: number;
  usdUsageAmount: number;
  useTime: number;
  vramCap: number;
  vramUsage: number;
  workload: number;
}
export interface PointSpendsResponse {
  status: number;
  pointSpends: PointSpends[];
}

export interface PointPayments {
  ser: number;
  owner: string;
  orderId: string;
  payType: string;
  payKey: string;
  payMethod: string;
  country: string;
  currency: string;
  amount: number;
  balanceAmount: number;
  suppliedAmount: number;
  vat: number;
  taxFreeAmount: number;
  taxExemptionAmount: number;
  point: number;
  appNo: string;
  cardNo: string;
  installment: number;
  issueCompanyNo: string;
  acqCompanyNo: string;
  useCardPoint: boolean;
  cardType: string;
  ownerType: string;
  bankCode: string;
  receiptUrl: string;
  status: string;
  errorCode: string;
  errorMessage: string;
  isExcept: boolean;
  exceptNote: string;
  requestedAt: number[] | null;
  approvedAt: number[] | null;
  cancelAt: number[] | null;
}

export interface PointPaymentResponse {
  status: number;
  payments: PointPayments[];
}

export interface PointHistory {
  ser: number;
  owner: string;
  namespace: string;
  nodeName: string;
  workload: number;
  podName: string;
  point: number;
  amount: number;
  unitAmount: number;
  usageAmount: number;
  krw: number;
  gpuAmount: number;
  gpuName: string;
  gpuPoint: number;
  gpuUnitAmount: number;
  gpuUnitPrice: number;
  gpuUsage: number;
  gpuUsageAmount: number;
  gpuUsagePrice: number;
  vramCap: number;
  vramUsage: number;
  cpuUsage: number;
  memCap: number;
  memUsage: number;
  netRx: number;
  netTx: number;
  diskUsage: number;
  diskOccu: number;
  unitPrice: number;
  usagePrice: number;
  useTime: number;
  startAt: number[];
  endAt: number[];
  createdAt: number[];
  size: number;
}

export interface PointHistoryResponse {
  status: number;
  pointHistory: PointHistory[];
}

export interface PointSettle {
  ser: number;
  owner: string;
  namespace: string;
  availablePoint: number;
  chargePoint: number;
  incomePoint: number;
  spendPoint: number;
  networkPoint: number;
  reqPoint: number;
  settlePoint: number;
  taxPoint: number;
  availableAmount: number;
  chargeAmount: number;
  incomeAmount: number;
  spendAmount: number;
  networkAmount: number;
  reqAmount: number;
  settleAmount: number;
  taxAmount: number;
  taxRate: number;
  krw: number;
  isSettle: boolean;
  note: string;
  reqAt: number[] | null;
  settleAt: number[] | null;
  createdAt: number[] | null;
}

export interface PointSettleSearch {
  startNum?: number;
  scaleNum?: number;
  owner?: string;
  reqStartTime?: string;
  reqEndTime?: string;
  settleStartTime?: string;
  settleEndTime?: string;
  sortName?: string;
  sortType?: string;
}

export interface PointSettleResponse {
  status: number;
  pointSettles: PointSettle[];
  paging?: Paging;
  pointSettle?: PointSettle;
}

export interface PointCharge {
  ser: number;
  owner: string;
  type: string;
  point: number;
  balance: number;
  paymentId: string;
  isUsed?: boolean;
  isExcept: boolean;
  exceptNote: string;
  createdAt: number[] | null;
  canceledAt: number[] | null;
  isLatest: boolean;
}

export interface PointChargeSearch {
  owner?: string;
  startNum?: number;
  scaleNum?: number;
  type?: string;
  paymentId?: string;
  isUsed?: string;
  isExcept?: string;
  exceptNote?: string;
  startTime?: string;
  endTime?: string;
  sortName?: string;
  sortType?: string;
}

export interface PointChargeResponse {
  status: number;
  charges?: PointCharge[];
  charge?: PointCharge;
  paging?: Paging;
}
