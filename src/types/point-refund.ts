import { Paging } from '@/types/paging.ts';
import { PointSettle } from '@/types/point.ts';

export interface PointRefundStatusResponse {
  status: number;
  points: PointStatus;
}

export interface PointStatus {
  balance: number;
  payantBalance: number;
  freeBalance: number;
  availablePoint: number;
  availablePayantPoint: number;
  availableFreePoint: number;
  network: number;
  income: PointIncomeStatus;
  spend: PointSpendStatus;
  refund: PointRefundStatus;
}

export interface PointSpendStatus {
  point: number;
  usdAmount: number;
  usdUnitAmount: number;
  usdUsageAmount: number;
  krwAmount: number;
  krwUnitAmount: number;
  krwUsageAmount: number;
}

export interface PointIncomeStatus {
  point: number;
  usdAmount: number;
  usdUnitAmount: number;
  usdUsageAmount: number;
  krwAmount: number;
  krwUnitAmount: number;
  krwUsageAmount: number;
}

export interface PointRefundStatus {
  reqPoint: number;
  refundPoint: number;
  feePoint: number;
  availablePoint: number;
  usdReqAmount: number;
  usdRefundAmount: number;
  usdTaxAmount: number;
  krwReqAmount: number;
  krwRefundAmount: number;
  krwTaxAmount: number;
}

export interface PointRefundSearch {
  startNum?: number;
  scaleNum?: number;
  owner?: string;
  reqStartTime?: string;
  reqEndTime?: string;
  refundStartTime?: string;
  refundEndTime?: string;
  transferStartTime?: string;
  transferEndTime?: string;
  sortName?: string;
  sortType?: string;
}

export interface PointRefundResponse {
  status: number;
  pointRefunds: PointRefund[];
  paging?: Paging;
  pointRefund?: PointRefund;
}

export interface PointRefund {
  ser: number;
  owner: string;
  namespace: string;
  availablePoint: number;
  chargePoint: number;
  spendPoint: number;
  networkPoint: number;
  reqPoint: number;
  refundPoint: number;
  feePoint: number;
  availableAmount: number;
  chargeAmount: number;
  spendAmount: number;
  networkAmount: number;
  reqAmount: number;
  refundAmount: number;
  feeAmount: number;
  feeRate: number;
  krw: number;
  isRefund: boolean;
  note: string;
  reqAt: number[] | null;
  refundAt: number[] | null;
  transferAt: number[] | null;
  createdAt: number[] | null;
}

class PointRefundDto {
  owner: string = '';
  point: number = 0;
  amount: number = 0;
}

export { PointRefundDto };
