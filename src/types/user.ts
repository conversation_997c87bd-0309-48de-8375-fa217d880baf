import { Paging } from './paging';

export interface User {
  email: string;
  name: string;
  role: string;
  picture: string;
  phone: string;
  inferPort: string | number;
  namespace: string;
  privKey: string;
  createdAt: string;
  lastSeen: string;
  company: string;
  jobPosition: string;
  isConditions: boolean;
  isPrivacy: boolean;
  isMarketing: boolean;
  isDedicated: boolean;
}

export interface UserResponse {
  status: number;
  users: User[];
  user: User;
  paging: Paging;
}

export interface UserRequest {
  startNum: number | undefined;
  scaleNum: number | undefined;
  email: string | undefined;
  name: string | undefined;
  role: string | undefined;
  namespace: string | undefined;
  startTime: string | undefined;
  endTime: string | undefined;
  sortName: string | undefined;
  sortType: string | undefined;
}

export interface UserStorage {
  ser: number;
  owner: string;
  description: string;
  name: string;
  type: string;
  credential: string;
  remotePath: string;
  capacity: number;
  accessMode: string;
  pvStatus: string;
  pvcStatus: string;
  createdAt: string;
  lastSeen: string;
}

export interface UserStorageRegister {
  owner: string;
  description: string;
  type: string;
  clientId: string;
  clientSecret: string;
  remotePath?: string;
  capacity?: number;
  accessMode?: string;
  accessKeyId?: string;
  secretAccessKey?: string;
  region?: string;
}

export const mapSearchParamsToUserRequest = (searchParams: URLSearchParams): UserRequest => {
  return {
    startNum: parseInt(searchParams.get('startNum') || '0', 10),
    scaleNum: parseInt(searchParams.get('scaleNum') || process.env.NEXT_PUBLIC_PAGE_SCALE + '', 10),
    email: searchParams.get('owner') || undefined,
    name: searchParams.get('name') || undefined,
    role: searchParams.get('role') || undefined,
    namespace: searchParams.get('namespace') || undefined,
    startTime: searchParams.get('startTime') || undefined,
    endTime: searchParams.get('endTime') || undefined,
    sortName: searchParams.get('sortName') || undefined,
    sortType: searchParams.get('sortType') || undefined
  };
};
