class WorkloadType {
  description?: string = '';
  repo?: string = '';
  containerImage?: string = '';
  containerImageCheck?: string = '';
  port?: number = 0;
  replica?: number = 0;
  gpu?: string = '';
  maxPrice?: string = '';
  vram?: number = 0;
  sharedMemory?: number = 0;
}

type WorkloadError = {
  description?: string;
  repo?: string;
  containerImage?: string;
  containerImageCheck?: string;
  port?: number;
  replica?: number;
  gpu?: string;
  maxPrice?: string;
  vram?: number;
  sharedMemory?: number;
};

type NodePriceType = {
  unitPrice?: number;
  usagePrice?: number;
};

type NodePriceError = {
  unitPrice?: number;
  usagePrice?: number;
};

class PointPaymentType {
  point: number = 0;
  payMethod: string = '';
}

type PointPaymentError = {
  point?: number;
  payMethod?: string;
};

class PointSettleType {
  point: number = 0;
  payMethod: string = '';
}

type PointSettleError = {
  point?: number;
  payMethod?: string;
};

class PointRefundType {
  point: number = 0;
  payMethod: string = '';
}

type PointRefundError = {
  point?: number;
  payMethod?: string;
};

type SignupFormType = {
  email?: string;
  name?: string;
  phone?: string;
  picture?: string;
  company?: string;
  jobPosition?: string;
  termsAgrees?: string;
};

type SignupFormError = {
  name?: string;
  phone?: string;
  company?: string;
  jobPosition?: string;
};

type ContactUsType = {
  familyName: string;
  name: string;
  email: string;
  country: string;
  company: string;
  phone: string;
  inquiry: string;
};

type ContactUsError = {
  familyName?: string;
  name?: string;
  email?: string;
  country?: string;
  company?: string;
  phone?: string;
  inquiry?: string;
};

type UserType = {
  name: string;
  phone: string;
};

type UserError = {
  name?: string;
  phone?: string;
};

type CredentialType = {
  accKey: string;
  secKey: string;
  region: string;
  registryId: string;
};

type CredentialError = {
  accKey?: string;
  secKey?: string;
  region?: string;
  registryId?: string;
};

class PodSshType {
  podName?: string = '';
  namespace?: string = '';
  workload?: string = '';
  publicIp: string = '';
  shell?: string = 'bash';
}

type PodSshError = {
  podName?: string;
  namespace?: string;
  workload?: string;
  publicIp?: string;
  shell?: string;
};

type StorageRegisterType = {
  description: string;
  remotePath?: string;
  capacity?: string;
  accessMode?: string;
  accessKeyId?: string;
  secretAccessKey?: string;
  region?: string;
  // clientId: string;
  // clientSecret: string;
};

type StorageRegisterError = {
  description?: string;
  remotePath?: string;
  capacity?: string;
  accessMode?: string;
  accessKeyId?: string;
  secretAccessKey?: string;
  region?: string;
  // clientId?: string;
  // clientSecret?: string;
};

type StorageUpdateType = {
  description?: string;
  remotePath: string;
  capacity: string;
  accessMode: string;
};

type StorageUpdateError = {
  description?: string;
  remotePath?: string;
  capacity?: string;
  accessMode?: string;
};

type QnaType = {
  categoryCode: string;
  name: string;
  title: string;
  content: string;
};

type QnaError = {
  categoryCode?: string;
  name?: string;
  title?: string;
  content?: string;
};
