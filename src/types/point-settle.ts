export interface PointSettleStatusResponse {
  status: number;
  points: PointSettleStatus;
}

export interface PointSettleStatus {
  balance: number;
  network: number;
  income: PointIncome;
  spend: PointSpend;
  settle: PointSettle;
}

export interface PointSpend {
  point: number;
  usdAmount: number;
  usdUnitAmount: number;
  usdUsageAmount: number;
  krwAmount: number;
  krwUnitAmount: number;
  krwUsageAmount: number;
}

export interface PointIncome {
  point: number;
  usdAmount: number;
  usdUnitAmount: number;
  usdUsageAmount: number;
  krwAmount: number;
  krwUnitAmount: number;
  krwUsageAmount: number;
}

export interface PointSettle {
  reqPoint: number;
  settlePoint: number;
  taxPoint: number;
  availablePoint: number;
  usdReqAmount: number;
  usdSettleAmount: number;
  usdTaxAmount: number;
  krwReqAmount: number;
  krwSettleAmount: number;
  krwTaxAmount: number;
}

class PointSettleDto {
  owner: string = '';
  point: number = 0;
  amount: number = 0;
}

export { PointSettleDto };
