/* eslint-disable max-len */
const KTGlobalComponentsConfig = {
  modal: {
    backdropClass: 'transition-all duration-300',
  },
  drawer: {
    backdropClass: 'transition-all duration-300',
    hiddenClass: 'hidden'
  },
  collapse: {
    hiddenClass: 'hidden',
  },
  dismiss: {
    hiddenClass: 'hidden',
  },
  tabs: {
    hiddenClass: 'hidden',
  },
  accordion: {
    hiddenClass: 'hidden',
  }
};

export default KTGlobalComponentsConfig;

