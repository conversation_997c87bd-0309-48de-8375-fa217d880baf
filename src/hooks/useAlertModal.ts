import { create } from 'zustand';

interface AlertModalStore {
  isOpen: boolean;
  title: string;
  content: string;
  setAlertData: (title: string, content: string) => void;
  onOpen: (isLoading: boolean) => void;
  onClose: () => void;
}

export const useAlertModal = create<AlertModalStore>((set) => ({
  isOpen: false,
  title: '',
  content: '',
  setAlertData: (title: string, content: string) => set((state) => ({ isLoading: true, title: title, content: content })),
  onOpen: (isOpen: boolean) => set((state) => ({ isOpen: true })),
  onClose: () => set((state) => ({ isLoading: false, title: '', content: '' }))
}));
